### 注意

* 关于火狐浏览器安装zip格式的扩展包请参考这里：#60 ！

* 为了进一步改善翻译体验，我们建立了侧边翻译用户交流群，欢迎大家加入：[侧边翻译用户交流QQ群](https://jq.qq.com/?_wv=1027&k=gT5EYfFB)

### 新增

* 在翻译展示框中增加跳转到设置页面的按钮，便于调整设置；

* 为翻译展示框中的按钮添加文本说明信息，便于理解；

### 优化

* 调整翻译展示框两侧调整大小功能的触发阈值，避免影响页面元素的点击；

### 修复

* 修复在火狐浏览器上侧边翻译的样式与原网页样式冲突的问题 (#115, #116, #117)；

* 修复使用必应翻译时某些情况下翻译结果与翻译结果读音不匹配的问题 (#118)；

* 修复在浏览器原生PDF阅读器中右键翻译失效的问题；

* 修复在火狐浏览器上导致某些页面不能正常加载的问题 (#122)；

* 修复某些情况下翻译展示框超出页面边界的问题；

### 关于打赏

开发这个项目花费了我们许多的时间和精力，如果你真的觉得这个项目对你有帮助，不妨请我们喝罐可乐，支持我们继续做下去！

当然，这 __纯属自愿__，打赏并不能获得什么优待，不打赏也不会有任何影响，请量力而为！

| 微信 | 支付宝 |
| :-: | :-: |
| <img src="https://user-images.githubusercontent.com/25877145/80864662-b6617c00-8cb6-11ea-915a-582ca046118c.png" height=200 alt="微信支付"/> | <img src="https://user-images.githubusercontent.com/25877145/80864685-ced19680-8cb6-11ea-94e5-f5ca8e4389b9.jpg" height=200 alt="支付宝支付"/> |