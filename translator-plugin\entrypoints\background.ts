// import { createWorker } from 'tesseract.js'; // 在Service Worker中不可用

export default defineBackground(() => {
  console.log('Translator plugin background script loaded', { id: browser.runtime.id });

  // 监听来自content script的消息
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Background received message:', message);

    switch (message.type) {
      case 'TRANSLATE_TEXT':
        handleTranslateText(message.payload)
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 保持消息通道开放

      case 'GET_SETTINGS':
        handleGetSettings()
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true;

      case 'UPDATE_SETTINGS':
        handleUpdateSettings(message.payload)
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true;

      case 'CAPTURE_SCREENSHOT':
        handleCaptureScreenshot(message.payload, sender.tab)
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true;

      case 'CAPTURE_SCREENSHOT_ONLY':
        handleCaptureScreenshotOnly(message.payload, sender.tab)
          .then(result => sendResponse(result))
          .catch(error => sendResponse({ success: false, error: error.message }));
        return true;

      default:
        sendResponse({ success: false, error: 'Unknown message type' });
    }
  });

  // 创建右键菜单
  chrome.runtime.onInstalled.addListener(() => {
    chrome.contextMenus.create({
      id: 'translate-selection',
      title: '翻译选中文本',
      contexts: ['selection']
    });

    chrome.contextMenus.create({
      id: 'sidebar-translate',
      title: '侧边翻译',
      contexts: ['selection']
    });

    chrome.contextMenus.create({
      id: 'screenshot-translate',
      title: '截图翻译',
      contexts: ['page']
    });

    chrome.contextMenus.create({
      id: 'toggle-sidebar',
      title: '显示/隐藏翻译助手',
      contexts: ['page']
    });
  });

  // 处理右键菜单点击
  chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === 'translate-selection' && info.selectionText) {
      // 向content script发送消息显示翻译
      if (tab?.id) {
        chrome.tabs.sendMessage(tab.id, {
          type: 'SHOW_TRANSLATION',
          payload: {
            text: info.selectionText
          }
        });
      }
    } else if (info.menuItemId === 'sidebar-translate' && info.selectionText) {
      // 使用侧边栏翻译选中文本
      if (tab?.id) {
        chrome.tabs.sendMessage(tab.id, {
          type: 'SIDEBAR_TRANSLATE',
          payload: {
            text: info.selectionText
          }
        });
      }
    } else if (info.menuItemId === 'toggle-sidebar') {
      // 切换侧边栏显示/隐藏
      if (tab?.id) {
        chrome.tabs.sendMessage(tab.id, {
          type: 'TOGGLE_SIDEBAR'
        });
      }
    } else if (info.menuItemId === 'screenshot-translate') {
      // 启动截图翻译模式
      if (tab?.id) {
        chrome.tabs.sendMessage(tab.id, {
          type: 'START_SCREENSHOT_TRANSLATION'
        });
      }
    }
  });
});

// 处理翻译请求
async function handleTranslateText(payload: any) {
  try {
    const { text, source_language = 'auto', target_language = 'zh', model_id } = payload;

    // 验证输入
    if (!text || text.length === 0) {
      throw new Error('文本不能为空');
    }

    if (text.length > 5000) {
      throw new Error('文本过长（最多5000字符）');
    }

    // 检查缓存
    const cacheKey = `${source_language}-${target_language}-${text}`;
    const cached = await getFromCache(cacheKey);
    if (cached) {
      return { success: true, data: cached };
    }

    // 调用翻译API
    const result = await callTranslationAPI(text, source_language, target_language, model_id);

    // 缓存结果
    await saveToCache(cacheKey, result);

    return { success: true, data: result };
  } catch (error: any) {
    console.error('Translation error:', error);
    return { success: false, error: error.message };
  }
}

// 调用翻译API
async function callTranslationAPI(text: string, from: string, to: string, modelId?: string) {
  console.log('🔄 Starting translation API call', { modelId, text: text.substring(0, 50) + '...' });

  try {
    // 获取API配置
    const apiSettings = await chrome.storage.local.get([
      'preferred_provider',
      'google_api_key',
      'openai_api_key',
      'openai_endpoint',
      'kimi_api_key',
      'kimi_endpoint'
    ]);

    // 优先使用传入的modelId，否则使用preferred_provider
    const provider = modelId || apiSettings.preferred_provider || 'google';
    console.log('🎯 Using provider:', provider, modelId ? '(from modelId)' : '(from settings)');

    switch (provider) {
      case 'openai':
        if (apiSettings.openai_api_key) {
          return await callOpenAITranslate(
            apiSettings.openai_endpoint || 'https://api.openai.com/v1/chat/completions',
            apiSettings.openai_api_key,
            text,
            from,
            to
          );
        }
        break;

      case 'kimi':
        if (apiSettings.kimi_api_key) {
          return await callKimiTranslate(
            apiSettings.kimi_endpoint || 'https://api.moonshot.cn/v1/chat/completions',
            apiSettings.kimi_api_key,
            text,
            from,
            to
          );
        }
        break;
    }

    // 默认使用免费的Google翻译
    console.log('🌐 Falling back to free Google Translate');
    const result = await callGoogleTranslate(text, from, to);
    return {
      ...result,
      model_used: 'Google Translate (Free)',
      response_time_ms: 0
    };

  } catch (error) {
    console.error('💥 Translation API failed:', error);
    throw new Error('翻译服务暂时不可用: ' + (error as Error).message);
  }
}

// Google翻译（EdgeTranslate风格）- 完全按照EdgeTranslate逻辑重构
async function callGoogleTranslate(text: string, from: string, to: string) {
  console.log('🌐 Calling EdgeTranslate-style Google Translate:', { text, from, to });

  const translateOnce = async (): Promise<any> => {
    try {
      // 生成EdgeTranslate风格的URL
      const url = edgeTranslateGoogle.generateTranslateURL(text, from, to);
      console.log('🔗 EdgeTranslate URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Referer': 'https://translate.google.com/',
          'Origin': 'https://translate.google.com',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin'
        }
      });

      console.log('📡 EdgeTranslate response status:', response.status);

      if (response.status === 200) {
        const data = await response.json();
        console.log('📊 EdgeTranslate raw data:', data);

        // 使用EdgeTranslate的解析逻辑
        let result;
        if (edgeTranslateGoogle.fallBacking) {
          result = parseFallbackResult(data, text, from, to);
        } else {
          result = parseBetterResult(data, text, from, to);
        }

        return result;
      }

      // 429错误表示被Google阻止，降级到备用API
      if (response.status === 429 && !edgeTranslateGoogle.fallBacking) {
        console.log('🚫 Blocked by Google (429), falling back to old API');
        edgeTranslateGoogle.fallBack();
        await edgeTranslateGoogle.updateTKK();
        return await translateOnce();
      }

      throw new Error(`Google Translate API error: ${response.status}`);
    } catch (error) {
      console.error('💥 EdgeTranslate Google Translate failed:', error);
      throw error;
    }
  };

  return await translateOnce();
}

// EdgeTranslate的parseBetterResult - 处理结构化JSON响应（dj=1格式）
function parseBetterResult(response: any, originalText: string, from: string, to: string): any {
  console.log('🔍 Parsing better result (structured JSON)');

  const result: any = {
    originalText: originalText,
    mainMeaning: "",
    sourceLanguage: from,
    targetLanguage: to,
    confidence: 0.9,
    responseTime: Date.now(),
    translatedText: '', // 兼容当前系统
  };

  try {
    // 解析sentences - 主要翻译内容
    if (response.sentences) {
      result.mainMeaning = "";
      result.originalText = "";

      let i = 0;
      for (; i < response.sentences.length && response.sentences[i].trans; i++) {
        result.mainMeaning += response.sentences[i].trans;
        result.originalText += response.sentences[i].orig;
      }

      // 如果有不包含翻译的项目，应该是发音
      if (i < response.sentences.length) {
        if (response.sentences[i].translit) {
          result.tPronunciation = response.sentences[i].translit;
        }
        if (response.sentences[i].src_translit) {
          result.sPronunciation = response.sentences[i].src_translit;
        }
      }

      result.translatedText = result.mainMeaning; // 兼容字段
      console.log('✅ Sentences parsed:', result.mainMeaning);
    }

    // 解析dict - 词典信息
    if (response.dict) {
      result.detailedMeanings = [];
      for (let item of response.dict) {
        for (let entry of item.entry) {
          result.detailedMeanings.push({
            pos: item.pos,
            meaning: entry.word,
            synonyms: entry.reverse_translation,
          });
        }
      }
      console.log('📚 Dict parsed:', result.detailedMeanings.length, 'entries');
    }

    // 解析definitions - 定义
    if (response.definitions) {
      result.definitions = [];
      for (let item of response.definitions) {
        for (let entry of item.entry) {
          result.definitions.push({
            pos: item.pos,
            meaning: entry.gloss,
            synonyms: [],
            example: entry.example,
          });
        }
      }
      console.log('📖 Definitions parsed:', result.definitions.length, 'entries');
    }

    // 解析examples - 例句
    if (response.examples) {
      result.examples = [];
      for (let example of response.examples.example) {
        result.examples.push({
          source: example.text,
          target: null,
        });
      }

      // 排序例句
      result.examples.sort((a: any, b: any) =>
        a.source! > b.source! ? 1 : a.source === b.source ? 0 : -1
      );
      console.log('📝 Examples parsed:', result.examples.length, 'entries');
    }

  } catch (parseError) {
    console.error('💥 Failed to parse better result:', parseError);
  }

  return result;
}

// EdgeTranslate的parseFallbackResult - 处理数组格式响应（备用API格式）
function parseFallbackResult(response: any[], originalText: string, from: string, to: string): any {
  console.log('🔍 Parsing fallback result (array format)');

  const result: any = {
    originalText: originalText,
    mainMeaning: "",
    sourceLanguage: from,
    targetLanguage: to,
    confidence: 0.9,
    responseTime: Date.now(),
    translatedText: '', // 兼容当前系统
  };

  try {
    for (let i = 0; i < response.length; i++) {
      if (response[i]) {
        const items = response[i];
        switch (i) {
          // 单词的基本意思和音标
          case 0: {
            let mainMeanings = [];
            let originalTexts = [];
            let lastIndex = items.length - 1;

            for (let j = 0; j <= lastIndex; j++) {
              mainMeanings.push(items[j][0]);
              originalTexts.push(items[j][1]);
            }

            result.mainMeaning = mainMeanings.join("");
            result.originalText = originalTexts.join("");
            result.translatedText = result.mainMeaning; // 兼容字段

            try {
              if (lastIndex > 0) {
                if (items[lastIndex][2] && items[lastIndex][2].length > 0) {
                  result.tPronunciation = items[lastIndex][2];
                }
                if (items[lastIndex][3] && items[lastIndex][3].length > 0) {
                  result.sPronunciation = items[lastIndex][3];
                }
              }
            } catch (error) {}

            console.log('✅ Basic translation parsed:', result.mainMeaning);
            break;
          }

          // 单词的所有词性及对应的意思
          case 1:
            result.detailedMeanings = [];
            items.forEach((item: any) =>
              result.detailedMeanings.push({
                pos: item[0],
                meaning: item[1].join(", "),
              })
            );
            console.log('📚 Detailed meanings parsed:', result.detailedMeanings.length, 'entries');
            break;

          // 单词的定义及对应例子
          case 12:
            result.definitions = [];
            items.forEach((item: any) => {
              item[1].forEach((element: any) => {
                result.definitions.push({
                  pos: item[0],
                  meaning: element[0],
                  example: element[2],
                });
              });
            });
            console.log('📖 Definitions parsed:', result.definitions.length, 'entries');
            break;

          // 单词的例句
          case 13:
            result.examples = [];
            items.forEach((item: any) =>
              item.forEach((element: any) =>
                result.examples.push({ source: null, target: element[0] })
              )
            );
            console.log('📝 Examples parsed:', result.examples.length, 'entries');
            break;

          default:
            break;
        }
      }
    }
  } catch (parseError) {
    console.error('💥 Failed to parse fallback result:', parseError);
  }

  return result;
}

// 旧的解析函数 - 保留作为最后的备用
function parseGoogleTranslateResult(data: any, originalText: string, from: string, to: string) {
  console.log('🔍 Parsing Google Translate result, data structure:', JSON.stringify(data, null, 2));

  const result: any = {
    originalText,
    mainMeaning: '',
    sourceLanguage: from,
    targetLanguage: to,
    confidence: 0.9,
    responseTime: Date.now(),
    translatedText: '', // 兼容当前系统
  };

  try {
    // 解析主要翻译结果 (data[0]) - 这是最重要的部分
    if (data[0] && Array.isArray(data[0])) {
      const translations = [];
      for (const item of data[0]) {
        if (item && item[0]) {
          translations.push(item[0]);
        }
      }

      // 优化翻译结果选择逻辑
      let bestTranslation = translations.join('');

      // 对于单个词汇，尝试获取更准确的翻译
      if (originalText.trim().split(/\s+/).length === 1) {
        bestTranslation = selectBestSingleWordTranslation(translations, originalText);
      }

      result.mainMeaning = bestTranslation;
      result.translatedText = result.mainMeaning; // 兼容字段
      console.log('✅ Main translation extracted:', result.mainMeaning);
      console.log('📝 All translation options:', translations);
    }

    // 解析检测到的源语言 (data[2])
    if (data[2]) {
      result.sourceLanguage = data[2];
      console.log('🔍 Detected source language:', data[2]);
    }

    // 解析词典信息 (data[1]) - 词性和释义
    if (data[1] && Array.isArray(data[1])) {
      result.detailedMeanings = [];
      data[1].forEach((category: any, index: number) => {
        try {
          if (category && Array.isArray(category) && category.length >= 2) {
            const pos = category[0]; // 词性
            const meanings = category[1]; // 释义数组

            if (pos && Array.isArray(meanings)) {
              // 过滤和优化释义
              const filteredMeanings = filterAndRankMeanings(meanings, originalText);

              result.detailedMeanings.push({
                pos: pos,
                meanings: filteredMeanings.slice(0, 5) // 限制释义数量
              });
              console.log(`📚 Found meanings for ${pos}:`, filteredMeanings.slice(0, 3));

              // 如果主翻译不够好，尝试使用词典中的最佳释义
              if (originalText.trim().split(/\s+/).length === 1 && filteredMeanings.length > 0) {
                const bestDictMeaning = filteredMeanings[0];
                if (shouldUseDictionaryMeaning(result.mainMeaning, bestDictMeaning)) {
                  console.log(`🔄 Using dictionary meaning instead: "${bestDictMeaning}"`);
                  result.mainMeaning = bestDictMeaning;
                  result.translatedText = bestDictMeaning;
                }
              }
            }
          }
        } catch (err) {
          console.warn(`⚠️ Error parsing meaning category ${index}:`, err);
        }
      });
    }

    // 解析例句 (data[13] 或其他位置)
    for (let i = 10; i < Math.min(data.length, 20); i++) {
      if (data[i] && Array.isArray(data[i]) && data[i][0] && Array.isArray(data[i][0])) {
        try {
          result.examples = data[i][0].slice(0, 3).map((example: any) => ({
            source: example[0] || '',
            target: example[1] || ''
          })).filter((ex: any) => ex.source && ex.target);

          if (result.examples.length > 0) {
            console.log('📝 Found examples:', result.examples.length);
            break;
          }
        } catch (err) {
          console.warn(`⚠️ Error parsing examples at index ${i}:`, err);
        }
      }
    }

    // 解析同义词 (data[11] 或其他位置)
    for (let i = 10; i < Math.min(data.length, 15); i++) {
      if (data[i] && Array.isArray(data[i])) {
        try {
          const synData = data[i];
          if (synData.length > 0 && Array.isArray(synData[0]) && synData[0].length >= 2) {
            result.synonyms = [];
            synData.forEach((synGroup: any) => {
              if (synGroup && synGroup[0] && synGroup[1] && Array.isArray(synGroup[1])) {
                result.synonyms.push({
                  pos: synGroup[0],
                  words: synGroup[1].slice(0, 5).map((syn: any) => syn[0] || syn).filter(Boolean)
                });
              }
            });

            if (result.synonyms.length > 0) {
              console.log('🔄 Found synonyms:', result.synonyms.length);
              break;
            }
          }
        } catch (err) {
          console.warn(`⚠️ Error parsing synonyms at index ${i}:`, err);
        }
      }
    }

    // 解析发音信息
    if (data[0] && Array.isArray(data[0]) && data[0][0]) {
      if (data[0][0][3]) {
        result.sPronunciation = data[0][0][3]; // 源语言发音
        console.log('🔊 Source pronunciation:', result.sPronunciation);
      }
      if (data[0][0][2]) {
        result.tPronunciation = data[0][0][2]; // 目标语言发音
        console.log('🔊 Target pronunciation:', result.tPronunciation);
      }
    }

    // 计算置信度
    if (result.mainMeaning && result.mainMeaning.length > 0) {
      result.confidence = Math.min(0.95, 0.7 + (result.detailedMeanings?.length || 0) * 0.05);
    }

  } catch (parseError) {
    console.error('💥 Failed to parse detailed Google Translate result:', parseError);
    // 如果解析失败，至少返回基本翻译结果
    if (!result.mainMeaning && data[0] && Array.isArray(data[0])) {
      result.mainMeaning = data[0].map((item: any) => item[0] || '').join('');
      result.translatedText = result.mainMeaning;
    }
  }

  console.log('📊 Final parsed result:', {
    mainMeaning: result.mainMeaning,
    detailedMeanings: result.detailedMeanings?.length || 0,
    examples: result.examples?.length || 0,
    synonyms: result.synonyms?.length || 0,
    sourceLanguage: result.sourceLanguage
  });

  return result;
}

// 为单个词汇选择最佳翻译结果
function selectBestSingleWordTranslation(translations: string[], originalWord: string): string {
  if (!translations || translations.length === 0) {
    return '';
  }

  // 如果只有一个翻译，直接返回
  if (translations.length === 1) {
    return translations[0];
  }

  const word = originalWord.toLowerCase().trim();
  console.log(`🎯 Selecting best translation for "${word}" from:`, translations);

  // 评分系统：为每个翻译选项打分
  const scoredTranslations = translations.map(translation => {
    let score = 0;
    const trans = translation.trim();

    // 基础分数
    score += 10;

    // 长度评分：优先选择较短的翻译（通常更基础）
    if (trans.length <= 3) {
      score += 20; // 短翻译加分
    } else if (trans.length <= 6) {
      score += 10;
    } else if (trans.length > 10) {
      score -= 15; // 过长的翻译减分
    }

    // 复杂度评分：避免包含特殊字符或过于复杂的翻译
    if (trans.includes('部门') || trans.includes('系统') || trans.includes('机构')) {
      score -= 25; // 组织机构类词汇减分
    }

    if (trans.includes('的') && trans.length > 4) {
      score -= 10; // 包含"的"的长翻译减分
    }

    // 常见词汇优先
    const commonTranslations = {
      'support': ['支持', '支撑'],
      'supports': ['支持', '支撑'],
      'help': ['帮助', '帮忙'],
      'create': ['创建', '创造'],
      'make': ['制作', '做'],
      'get': ['获得', '得到'],
      'use': ['使用', '用'],
      'work': ['工作', '运行'],
      'run': ['运行', '跑'],
      'start': ['开始', '启动'],
      'stop': ['停止', '结束'],
      'open': ['打开', '开放'],
      'close': ['关闭', '关'],
      'save': ['保存', '储存'],
      'load': ['加载', '装载'],
      'send': ['发送', '送'],
      'receive': ['接收', '收到']
    };

    if (commonTranslations[word] && commonTranslations[word].includes(trans)) {
      score += 30; // 常见翻译大幅加分
    }

    // 避免过于技术性的翻译
    if (trans.includes('技术') || trans.includes('方法') || trans.includes('功能')) {
      score -= 15;
    }

    console.log(`📊 Translation "${trans}" scored: ${score}`);
    return { translation: trans, score };
  });

  // 按分数排序，选择最高分的翻译
  scoredTranslations.sort((a, b) => b.score - a.score);
  const bestTranslation = scoredTranslations[0].translation;

  console.log(`🏆 Best translation selected: "${bestTranslation}" (score: ${scoredTranslations[0].score})`);
  return bestTranslation;
}

// 过滤和排序释义，优先显示最准确的释义
function filterAndRankMeanings(meanings: string[], originalWord: string): string[] {
  if (!meanings || meanings.length === 0) {
    return [];
  }

  const word = originalWord.toLowerCase().trim();

  // 评分和过滤释义
  const scoredMeanings = meanings.map(meaning => {
    let score = 0;
    const m = meaning.trim();

    // 基础分数
    score += 10;

    // 长度评分
    if (m.length <= 3) {
      score += 15; // 简短释义优先
    } else if (m.length <= 6) {
      score += 10;
    } else if (m.length > 12) {
      score -= 10; // 过长释义减分
    }

    // 避免过于复杂的释义
    if (m.includes('部门') || m.includes('机构') || m.includes('组织')) {
      score -= 20;
    }

    if (m.includes('的') && m.length > 6) {
      score -= 8;
    }

    // 避免技术术语
    if (m.includes('技术') || m.includes('系统') || m.includes('方法')) {
      score -= 15;
    }

    // 优先基础动词形式
    if (word.endsWith('s') && (m === '支持' || m === '帮助' || m === '创建' || m === '使用')) {
      score += 25;
    }

    return { meaning: m, score };
  });

  // 按分数排序
  scoredMeanings.sort((a, b) => b.score - a.score);

  // 去重并返回
  const uniqueMeanings = [];
  const seen = new Set();

  for (const item of scoredMeanings) {
    if (!seen.has(item.meaning) && item.score > 0) {
      uniqueMeanings.push(item.meaning);
      seen.add(item.meaning);
    }
  }

  return uniqueMeanings;
}

// 判断是否应该使用词典释义替代主翻译
function shouldUseDictionaryMeaning(mainMeaning: string, dictMeaning: string): boolean {
  if (!mainMeaning || !dictMeaning) {
    return false;
  }

  // 如果主翻译包含"部门"等组织词汇，而词典释义更简单，则使用词典释义
  if (mainMeaning.includes('部门') && !dictMeaning.includes('部门') && dictMeaning.length <= 4) {
    return true;
  }

  // 如果主翻译过长，而词典释义更简洁
  if (mainMeaning.length > 8 && dictMeaning.length <= 4) {
    return true;
  }

  return false;
}

// EdgeTranslate风格的Google翻译器类
class EdgeTranslateGoogleTranslator {
  // TK需要的密钥 - 从EdgeTranslate复制
  private TKK = [434217, 1534559001];

  // API URLs - 完全按照EdgeTranslate配置
  private readonly HOME_PAGE = "https://translate.google.com/";
  private readonly HOST = "https://translate.googleapis.com/";
  private readonly TRANSLATE_URL = `${this.HOST}translate_a/single?client=gtx&dj=1&dt=t&dt=at&dt=bd&dt=ex&dt=md&dt=rw&dt=ss&dt=rm`;
  private readonly FALLBACK_TRANSLATE_URL = `${this.HOST}translate_a/single?ie=UTF-8&client=webapp&otf=1&ssel=0&tsel=0&kc=5&dt=t&dt=at&dt=bd&dt=ex&dt=md&dt=rw&dt=ss&dt=rm`;

  public fallBacking = false;

  /**
   * Generate TK - 完全按照EdgeTranslate的算法
   */
  generateTK(a: string, b: number, c: number): string {
    b = Number(b) || 0;
    let e = [];
    let f = 0;
    let g = 0;

    for (; g < a.length; g++) {
      let l = a.charCodeAt(g);
      128 > l
        ? (e[f++] = l)
        : (2048 > l
            ? (e[f++] = (l >> 6) | 192)
            : (55296 == (l & 64512) &&
              g + 1 < a.length &&
              56320 == (a.charCodeAt(g + 1) & 64512)
                ? ((l = 65536 + ((l & 1023) << 10) + (a.charCodeAt(++g) & 1023)),
                  (e[f++] = (l >> 18) | 240),
                  (e[f++] = ((l >> 12) & 63) | 128))
                : (e[f++] = (l >> 12) | 224),
              (e[f++] = ((l >> 6) & 63) | 128)),
            (e[f++] = (l & 63) | 128));
    }

    a = b;
    for (f = 0; f < e.length; f++) {
      (a += e[f]), (a = this._magic(a, "+-a^+6"));
    }

    a = this._magic(a, "+-3^+b+-f");
    a ^= c || 0;
    0 > a && (a = (a & 2147483647) + 2147483648);
    a %= 1e6;

    return a.toString() + "." + (a ^ b);
  }

  /**
   * Magic function for TK generation - 从EdgeTranslate复制
   */
  private _magic(a: number, b: string): number {
    for (let c = 0; c < b.length - 2; c += 3) {
      let d = b.charAt(c + 2);
      d = "a" <= d ? d.charCodeAt(0) - 87 : Number(d);
      d = "+" == b.charAt(c + 1) ? a >>> d : a << d;
      a = "+" == b.charAt(c) ? (a + d) & 4294967295 : a ^ d;
    }
    return a;
  }

  /**
   * Update TKK from Google translate page - 从EdgeTranslate复制
   */
  async updateTKK(): Promise<void> {
    try {
      const response = await fetch(this.HOME_PAGE);
      const body = await response.text();

      let tkk = (body.match(/TKK=(.*?)\(\)\)'\);/i) || [""])[0]
        .replace(/\\x([0-9A-Fa-f]{2})/g, "") // remove hex chars
        .match(/[+-]?\d+/g);

      if (tkk) {
        this.TKK[0] = Number(tkk[2]);
        this.TKK[1] = Number(tkk[0]) + Number(tkk[1]);
      } else {
        tkk = body.match(/TKK[=:]['"](\d+?)\.(\d+?)['"]/i);
        if (tkk) {
          this.TKK[0] = Number(tkk[1]);
          this.TKK[1] = Number(tkk[2]);
        }
      }
      console.log('🔑 TKK updated:', this.TKK);
    } catch (error) {
      console.warn('⚠️ Failed to update TKK:', error);
    }
  }

  /**
   * Fall back to use old API and set a timeout to recover
   */
  fallBack(): void {
    this.fallBacking = true;
    console.log('🔄 Falling back to old API');
    setTimeout(() => {
      this.fallBacking = false;
      console.log('🔄 Recovered from fallback');
    }, 30 * 60 * 1000); // 30 minutes
  }

  /**
   * Generate translate URL - 按照EdgeTranslate逻辑
   */
  generateTranslateURL(text: string, from: string, to: string): string {
    const tk = this.generateTK(text, this.TKK[0], this.TKK[1]);
    let query = `&sl=${from}&tl=${to}&tk=${tk}&q=${encodeURIComponent(text)}`;

    if (this.fallBacking) {
      return this.FALLBACK_TRANSLATE_URL + query;
    }
    return this.TRANSLATE_URL + query;
  }
}

// 全局翻译器实例
const edgeTranslateGoogle = new EdgeTranslateGoogleTranslator();

// 缓存管理
async function getFromCache(key: string) {
  try {
    const result = await chrome.storage.local.get(`cache_${key}`);
    const cached = result[`cache_${key}`];

    if (cached && Date.now() - cached.timestamp < 24 * 60 * 60 * 1000) { // 24小时缓存
      return cached.data;
    }

    return null;
  } catch (error) {
    console.error('Cache get error:', error);
    return null;
  }
}

async function saveToCache(key: string, data: any) {
  try {
    await chrome.storage.local.set({
      [`cache_${key}`]: {
        data,
        timestamp: Date.now()
      }
    });
  } catch (error) {
    console.error('Cache save error:', error);
  }
}

// 获取设置
async function handleGetSettings() {
  try {
    const result = await chrome.storage.sync.get([
      'source_language',
      'target_language',
      'theme',
      'auto_translate',
      'show_pronunciation'
    ]);

    return {
      success: true,
      data: {
        source_language: result.source_language || 'auto',
        target_language: result.target_language || 'zh',
        theme: result.theme || 'system',
        auto_translate: result.auto_translate !== false,
        show_pronunciation: result.show_pronunciation !== false
      }
    };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

// 更新设置
async function handleUpdateSettings(settings: any) {
  try {
    await chrome.storage.sync.set(settings);
    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

// 处理截图和OCR
async function handleCaptureScreenshot(payload: any, tab: any) {
  console.log('📸 Handling screenshot capture:', payload);

  try {
    const { x, y, width, height } = payload;

    if (!tab?.id) {
      throw new Error('Invalid tab');
    }

    console.log('📸 Capturing visible tab:', tab.windowId);

    // 截取整个页面
    const dataUrl = await chrome.tabs.captureVisibleTab(tab.windowId, {
      format: 'png',
      quality: 100
    });

    console.log('📸 Screenshot captured, data URL length:', dataUrl.length);

    // 裁剪指定区域并进行OCR
    const croppedImageData = await cropImage(dataUrl, x, y, width, height);
    console.log('✂️ Image cropped, data URL length:', croppedImageData.length);

    const ocrResult = await performOCR(croppedImageData);
    console.log('🔍 OCR result:', ocrResult);

    return {
      success: true,
      data: {
        text: ocrResult.text,
        confidence: ocrResult.confidence,
        language: ocrResult.language
      }
    };
  } catch (error: any) {
    console.error('💥 Screenshot capture error:', error);
    return { success: false, error: error.message };
  }
}

// 只处理截图，不进行OCR
async function handleCaptureScreenshotOnly(payload: any, tab: any) {
  console.log('📸 Handling screenshot only:', payload);

  try {
    const { x, y, width, height } = payload;

    if (!tab?.id) {
      throw new Error('Invalid tab');
    }

    console.log('📸 Capturing visible tab:', tab.windowId);

    // 截取整个页面
    const dataUrl = await chrome.tabs.captureVisibleTab(tab.windowId, {
      format: 'png',
      quality: 100
    });

    console.log('📸 Screenshot captured, data URL length:', dataUrl.length);

    // 裁剪指定区域
    const croppedImageData = await cropImage(dataUrl, x, y, width, height);
    console.log('✂️ Image cropped, data URL length:', croppedImageData.length);

    return {
      success: true,
      data: {
        imageData: croppedImageData
      }
    };
  } catch (error: any) {
    console.error('💥 Screenshot capture error:', error);
    return { success: false, error: error.message };
  }
}

// 裁剪图片 - 在Service Worker中无法使用Canvas，返回原图和裁剪信息
async function cropImage(dataUrl: string, x: number, y: number, width: number, height: number): Promise<string> {
  console.log('✂️ Image cropping requested:', { x, y, width, height });
  console.log('⚠️ Service Worker环境无法进行图片裁剪，返回原图');

  // 在Service Worker环境中无法使用Canvas API
  // 返回原图，裁剪工作将在content script中完成
  return dataUrl;
}

// 执行OCR识别
async function performOCR(imageData: string): Promise<{ text: string; confidence: number; language: string }> {
  try {
    // 首先尝试调用管理后台的OCR API
    try {
      const response = await fetch('http://localhost:3000/api/ocr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image: imageData,
          language: 'auto'
        })
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          return data.data;
        }
      }
    } catch (apiError) {
      console.warn('OCR API failed, falling back to client-side OCR:', apiError);
    }

    // 回退到客户端OCR（使用Tesseract.js）
    return await performClientSideOCR(imageData);
  } catch (error: any) {
    console.error('OCR failed:', error);
    throw new Error('OCR识别失败');
  }
}

// 客户端OCR（使用在线OCR服务）
async function performClientSideOCR(imageData: string): Promise<{ text: string; confidence: number; language: string }> {
  console.log('🔍 Performing online OCR...');

  try {
    // 尝试使用免费的OCR API
    const base64Data = imageData.split(',')[1]; // 移除data:image/png;base64,前缀

    // 尝试使用OCR.space API
    const formData = new FormData();
    formData.append('base64Image', `data:image/png;base64,${base64Data}`);
    formData.append('language', 'chs');
    formData.append('isOverlayRequired', 'false');
    formData.append('detectOrientation', 'true');
    formData.append('scale', 'true');
    formData.append('OCREngine', '2');

    const response = await fetch('https://api.ocr.space/parse/image', {
      method: 'POST',
      headers: {
        'apikey': 'helloworld'
      },
      body: formData
    });

    if (response.ok) {
      const result = await response.json();
      console.log('🔍 OCR.space result:', result);

      if (result.ParsedResults && result.ParsedResults.length > 0) {
        const parsedText = result.ParsedResults[0].ParsedText;
        if (parsedText && parsedText.trim()) {
          // 清理识别结果
          const cleanText = parsedText
            .replace(/\r\n/g, ' ')
            .replace(/\n/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();

          return {
            text: cleanText,
            confidence: 0.8,
            language: 'auto'
          };
        }
      }

      if (result.ErrorMessage) {
        console.warn('OCR.space error:', result.ErrorMessage);
      }
    }
  } catch (error) {
    console.warn('OCR.space API failed:', error);
  }

  // 如果在线OCR失败，提供手动输入选项
  return {
    text: '[截图翻译] 自动OCR识别失败。您可以：\n1. 尝试选择更清晰的文字区域\n2. 使用划词翻译功能\n3. 手动复制文字进行翻译',
    confidence: 0.0,
    language: 'zh'
  };
}

// OpenAI翻译
async function callOpenAITranslate(endpoint: string, apiKey: string, text: string, from: string, to: string) {
  console.log('🤖 Calling OpenAI translate');

  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: `You are a professional translator. Translate the given text from ${from} to ${to}. Only return the translated text, no explanations.`
        },
        {
          role: 'user',
          content: text
        }
      ],
      temperature: 0.3
    })
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  const data = await response.json();
  const translatedText = data.choices[0]?.message?.content || '';

  return {
    translatedText,
    sourceLanguage: from,
    targetLanguage: to,
    model_used: 'OpenAI GPT-4o-mini'
  };
}

// Kimi翻译
async function callKimiTranslate(endpoint: string, apiKey: string, text: string, from: string, to: string) {
  console.log('🌙 Calling Kimi translate');

  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: 'moonshot-v1-8k',
      messages: [
        {
          role: 'system',
          content: `You are a professional translator. Translate the given text from ${from} to ${to}. Only return the translated text, no explanations.`
        },
        {
          role: 'user',
          content: text
        }
      ],
      temperature: 0.3
    })
  });

  if (!response.ok) {
    throw new Error(`Kimi API error: ${response.status}`);
  }

  const data = await response.json();
  const translatedText = data.choices[0]?.message?.content || '';

  return {
    translatedText,
    sourceLanguage: from,
    targetLanguage: to,
    model_used: 'Kimi Moonshot-v1-8k'
  };
}
