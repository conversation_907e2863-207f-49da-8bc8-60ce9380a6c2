<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeTranslate风格翻译测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 25px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .test-word {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            min-width: 100px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }
        
        .test-word:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .instructions {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        h2 {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        
        .expected-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .console-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .word-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-box {
            background: #e7f3ff;
            border: 1px solid #bee5eb;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .old-api {
            background-color: #fff3cd;
        }
        
        .new-api {
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <h1>🚀 EdgeTranslate风格翻译测试</h1>
    
    <div class="instructions">
        <h3>📋 测试目标</h3>
        <p>验证完全按照EdgeTranslate逻辑重构后的翻译效果：</p>
        <ol>
            <li><strong>TK令牌系统</strong>：使用EdgeTranslate的TK生成算法</li>
            <li><strong>双API策略</strong>：主API (client=gtx&dj=1) + 备用API (client=webapp)</li>
            <li><strong>结构化解析</strong>：parseBetterResult + parseFallbackResult</li>
            <li><strong>智能降级</strong>：429错误时自动切换到备用API</li>
            <li><strong>翻译准确性</strong>：与EdgeTranslate相同的翻译质量</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>🔧 EdgeTranslate核心特性实现</h2>
        
        <div class="feature-box">
            <h4>🔑 TK令牌生成系统</h4>
            <p>完全按照EdgeTranslate的算法实现：</p>
            <ul>
                <li>TKK密钥：[434217, 1534559001]</li>
                <li>复杂的TK计算算法（_magic函数）</li>
                <li>自动TKK更新机制</li>
                <li>与EdgeTranslate完全一致的令牌生成</li>
            </ul>
        </div>
        
        <div class="feature-box">
            <h4>🔄 双API策略</h4>
            <table class="comparison-table">
                <tr>
                    <th>API类型</th>
                    <th>URL配置</th>
                    <th>响应格式</th>
                    <th>解析函数</th>
                </tr>
                <tr class="new-api">
                    <td>主API</td>
                    <td>client=gtx&dj=1&dt=t&dt=at&dt=bd&dt=ex&dt=md&dt=rw&dt=ss&dt=rm</td>
                    <td>结构化JSON</td>
                    <td>parseBetterResult</td>
                </tr>
                <tr class="old-api">
                    <td>备用API</td>
                    <td>client=webapp&otf=1&ssel=0&tsel=0&kc=5&dt=...</td>
                    <td>数组格式</td>
                    <td>parseFallbackResult</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="test-container">
        <h2>🎯 翻译准确性测试</h2>
        
        <p>点击下面的单词，观察EdgeTranslate风格的翻译结果：</p>
        
        <div class="word-grid">
            <div class="test-word">supports</div>
            <div class="test-word">creates</div>
            <div class="test-word">helps</div>
            <div class="test-word">makes</div>
            <div class="test-word">uses</div>
            <div class="test-word">works</div>
            <div class="test-word">runs</div>
            <div class="test-word">starts</div>
            <div class="test-word">opens</div>
            <div class="test-word">beautiful</div>
            <div class="test-word">artificial</div>
            <div class="test-word">intelligence</div>
        </div>
        
        <div class="expected-result">
            <strong>预期EdgeTranslate风格结果：</strong>
            <ul>
                <li>✅ supports → "支持" (准确的基础词义)</li>
                <li>✅ 完整的词典信息 (detailedMeanings)</li>
                <li>✅ 词性标注 (pos: verb, noun等)</li>
                <li>✅ 例句信息 (examples)</li>
                <li>✅ 发音信息 (sPronunciation, tPronunciation)</li>
                <li>✅ 定义信息 (definitions)</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <h2>🔍 调试信息监控</h2>
        
        <div class="console-box">
            打开浏览器控制台，观察以下EdgeTranslate风格的调试信息：<br><br>
            
            <strong>TK令牌生成：</strong><br>
            🔑 TKK updated: [434217, 1534559001]<br>
            🔗 EdgeTranslate URL: https://translate.googleapis.com/translate_a/single?client=gtx&dj=1&...<br><br>
            
            <strong>API调用过程：</strong><br>
            🌐 Calling EdgeTranslate-style Google Translate<br>
            📡 EdgeTranslate response status: 200<br>
            📊 EdgeTranslate raw data: {sentences: [...], dict: [...], ...}<br><br>
            
            <strong>结构化解析：</strong><br>
            🔍 Parsing better result (structured JSON)<br>
            ✅ Sentences parsed: "支持"<br>
            📚 Dict parsed: 3 entries<br>
            📖 Definitions parsed: 2 entries<br>
            📝 Examples parsed: 5 entries<br><br>
            
            <strong>降级机制：</strong><br>
            🚫 Blocked by Google (429), falling back to old API<br>
            🔄 Falling back to old API<br>
            🔍 Parsing fallback result (array format)<br>
            🔄 Recovered from fallback
        </div>
        
        <div class="expected-result">
            <strong>验证要点：</strong>
            <ul>
                <li>✅ 使用EdgeTranslate的URL配置和参数</li>
                <li>✅ TK令牌正确生成和使用</li>
                <li>✅ 结构化JSON响应正确解析</li>
                <li>✅ 429错误时自动降级到备用API</li>
                <li>✅ 翻译结果与EdgeTranslate一致</li>
                <li>✅ 完整的词典、例句、发音信息</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <h2>📊 与原实现对比</h2>
        
        <table class="comparison-table">
            <tr>
                <th>特性</th>
                <th>原实现</th>
                <th>EdgeTranslate风格</th>
            </tr>
            <tr>
                <td>API配置</td>
                <td class="old-api">client=webapp，简单参数</td>
                <td class="new-api">client=gtx&dj=1，完整参数</td>
            </tr>
            <tr>
                <td>TK令牌</td>
                <td class="old-api">无</td>
                <td class="new-api">完整TK生成算法</td>
            </tr>
            <tr>
                <td>响应格式</td>
                <td class="old-api">数组格式</td>
                <td class="new-api">结构化JSON + 数组备用</td>
            </tr>
            <tr>
                <td>解析逻辑</td>
                <td class="old-api">简单数组解析</td>
                <td class="new-api">sentences/dict/definitions解析</td>
            </tr>
            <tr>
                <td>错误处理</td>
                <td class="old-api">基础错误处理</td>
                <td class="new-api">智能降级机制</td>
            </tr>
            <tr>
                <td>翻译质量</td>
                <td class="old-api">基础翻译</td>
                <td class="new-api">EdgeTranslate级别</td>
            </tr>
        </table>
    </div>

    <script>
        // 测试辅助功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 EdgeTranslate风格翻译测试页面已加载');
            console.log('📊 请观察以下关键信息：');
            console.log('1. TK令牌的生成和使用');
            console.log('2. EdgeTranslate风格的API调用');
            console.log('3. 结构化JSON的解析过程');
            console.log('4. 智能降级机制的触发');
            console.log('5. 翻译结果的准确性和完整性');
            
            // 添加选择事件监听
            document.addEventListener('mouseup', function() {
                const selection = window.getSelection();
                if (selection && selection.toString().trim()) {
                    const selectedText = selection.toString().trim();
                    console.log(`📝 选择了单词: "${selectedText}"`);
                    console.log('🎯 开始EdgeTranslate风格翻译测试');
                    
                    // 特别关注supports的翻译
                    if (selectedText.toLowerCase() === 'supports') {
                        console.log('🎯 正在测试"supports"的EdgeTranslate风格翻译');
                        console.log('预期结果: 使用TK令牌，结构化解析，准确翻译为"支持"');
                    }
                }
            });
            
            // 监听错误，特别是429错误的降级
            window.addEventListener('error', function(e) {
                console.error('❌ 捕获到错误:', e.error);
            });
        });
    </script>
</body>
</html>
