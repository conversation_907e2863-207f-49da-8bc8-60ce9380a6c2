-- Create user_words table for vocabulary management
CREATE TABLE IF NOT EXISTS user_words (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  word TEXT NOT NULL,
  translation TEXT NOT NULL,
  source_language VARCHAR(10) NOT NULL DEFAULT 'en',
  target_language VARCHAR(10) NOT NULL DEFAULT 'zh',
  context TEXT,
  pronunciation TEXT,
  part_of_speech VARCHAR(50),
  difficulty_level INTEGER NOT NULL DEFAULT 1 CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
  review_count INTEGER NOT NULL DEFAULT 0,
  last_reviewed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  -- Ensure unique words per user and language pair
  UNIQUE(user_id, word, source_language, target_language)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_user_words_user_id ON user_words(user_id);
CREATE INDEX IF NOT EXISTS idx_user_words_created_at ON user_words(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_words_word ON user_words(word);
CREATE INDEX IF NOT EXISTS idx_user_words_difficulty ON user_words(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_user_words_review_count ON user_words(review_count);
CREATE INDEX IF NOT EXISTS idx_user_words_last_reviewed ON user_words(last_reviewed_at);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_words_updated_at 
    BEFORE UPDATE ON user_words 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE user_words ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access their own words
CREATE POLICY "Users can view their own words" ON user_words
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own words" ON user_words
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own words" ON user_words
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own words" ON user_words
    FOR DELETE USING (auth.uid() = user_id);

-- Add comments for documentation
COMMENT ON TABLE user_words IS 'User vocabulary/word collection for spaced repetition learning';
COMMENT ON COLUMN user_words.word IS 'The original word or phrase to be learned';
COMMENT ON COLUMN user_words.translation IS 'The translated text in target language';
COMMENT ON COLUMN user_words.source_language IS 'ISO 639-1 language code of the original word';
COMMENT ON COLUMN user_words.target_language IS 'ISO 639-1 language code of the translation';
COMMENT ON COLUMN user_words.context IS 'Original context where the word was encountered';
COMMENT ON COLUMN user_words.pronunciation IS 'Phonetic pronunciation guide';
COMMENT ON COLUMN user_words.part_of_speech IS 'Grammatical category (noun, verb, adjective, etc.)';
COMMENT ON COLUMN user_words.difficulty_level IS 'User-defined difficulty level (1-5, 1=easy, 5=hard)';
COMMENT ON COLUMN user_words.review_count IS 'Number of times this word has been reviewed';
COMMENT ON COLUMN user_words.last_reviewed_at IS 'Timestamp of the last review session';
