'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';

interface User {
  id: string;
  email: string;
  provider: 'google' | 'microsoft';
  subscription_status: 'free' | 'premium';
  created_at: string;
  updated_at: string;
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editDialog, setEditDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [editForm, setEditForm] = useState({
    subscription_status: 'free' as 'free' | 'premium'
  });

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟用户数据
      const mockUsers: User[] = [
        {
          id: '1',
          email: '<EMAIL>',
          provider: 'google',
          subscription_status: 'free',
          created_at: '2025-01-15T10:30:00Z',
          updated_at: '2025-01-20T14:20:00Z'
        },
        {
          id: '2',
          email: '<EMAIL>',
          provider: 'microsoft',
          subscription_status: 'premium',
          created_at: '2025-01-10T09:15:00Z',
          updated_at: '2025-01-22T16:45:00Z'
        },
        {
          id: '3',
          email: '<EMAIL>',
          provider: 'google',
          subscription_status: 'free',
          created_at: '2025-01-20T11:00:00Z',
          updated_at: '2025-01-22T10:30:00Z'
        }
      ];
      
      setUsers(mockUsers);
    } catch (err: any) {
      setError('加载用户列表失败');
      console.error('Load users error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditForm({
      subscription_status: user.subscription_status
    });
    setEditDialog(true);
  };

  const handleSaveUser = async () => {
    if (!selectedUser) return;

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 更新本地状态
      setUsers(users.map(user => 
        user.id === selectedUser.id 
          ? { ...user, ...editForm, updated_at: new Date().toISOString() }
          : user
      ));
      
      setEditDialog(false);
      setSelectedUser(null);
    } catch (err: any) {
      setError('更新用户失败');
      console.error('Update user error:', err);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('确定要删除这个用户吗？')) return;

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setUsers(users.filter(user => user.id !== userId));
    } catch (err: any) {
      setError('删除用户失败');
      console.error('Delete user error:', err);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getProviderColor = (provider: string) => {
    switch (provider) {
      case 'google': return 'primary';
      case 'microsoft': return 'secondary';
      default: return 'default';
    }
  };

  const getSubscriptionColor = (status: string) => {
    switch (status) {
      case 'premium': return 'success';
      case 'free': return 'default';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* 页面标题 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          用户管理
        </Typography>
        <Box>
          <Button
            startIcon={<RefreshIcon />}
            onClick={loadUsers}
            sx={{ mr: 2 }}
          >
            刷新
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              // 添加用户功能
              alert('添加用户功能开发中...');
            }}
          >
            添加用户
          </Button>
        </Box>
      </Box>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* 用户统计卡片 */}
      <Box sx={{ display: 'flex', gap: 2, mb: 4 }}>
        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Typography variant="h6" color="primary.main">
              {users.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              总用户数
            </Typography>
          </CardContent>
        </Card>
        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Typography variant="h6" color="success.main">
              {users.filter(u => u.subscription_status === 'premium').length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              付费用户
            </Typography>
          </CardContent>
        </Card>
        <Card sx={{ flex: 1 }}>
          <CardContent>
            <Typography variant="h6" color="text.secondary">
              {users.filter(u => u.subscription_status === 'free').length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              免费用户
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* 用户列表 */}
      <Card>
        <CardContent>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>邮箱</TableCell>
                    <TableCell>登录方式</TableCell>
                    <TableCell>订阅状态</TableCell>
                    <TableCell>注册时间</TableCell>
                    <TableCell>最后更新</TableCell>
                    <TableCell align="right">操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id} hover>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <Chip
                          label={user.provider === 'google' ? 'Google' : 'Microsoft'}
                          color={getProviderColor(user.provider) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.subscription_status === 'premium' ? '付费版' : '免费版'}
                          color={getSubscriptionColor(user.subscription_status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{formatDate(user.created_at)}</TableCell>
                      <TableCell>{formatDate(user.updated_at)}</TableCell>
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={() => handleEditUser(user)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteUser(user.id)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* 编辑用户对话框 */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>编辑用户</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="邮箱"
              value={selectedUser?.email || ''}
              disabled
              sx={{ mb: 3 }}
            />
            <FormControl fullWidth>
              <InputLabel>订阅状态</InputLabel>
              <Select
                value={editForm.subscription_status}
                label="订阅状态"
                onChange={(e) => setEditForm({
                  ...editForm,
                  subscription_status: e.target.value as 'free' | 'premium'
                })}
              >
                <MenuItem value="free">免费版</MenuItem>
                <MenuItem value="premium">付费版</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>取消</Button>
          <Button onClick={handleSaveUser} variant="contained">保存</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
