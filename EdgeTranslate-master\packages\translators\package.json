{"name": "@edge_translate/translators", "version": "0.1.4", "description": "Collection of translators with unified interface.", "keywords": ["translate", "translator"], "scripts": {"dev": "vite build --watch", "build": "vite build && tsc --emitDeclarationOnly", "test": "jest"}, "files": ["dist"], "main": "dist/translators.umd.js", "unpkg": "dist/translators.iife.js", "module": "./dist/translators.es.js", "types": "./dist/types/index.d.ts", "devDependencies": {"@types/chrome": "^0.0.190", "@types/jest": "^28.1.3", "jest": "^28.1.1", "jest-chrome": "^0.7.2", "jest-environment-jsdom": "^28.1.1", "ts-jest": "^28.0.5", "typescript": "^4.7.4", "vite": "^2.9.13"}, "peerDependencies": {"axios": "^0.27.2"}}