import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type ThemeMode = 'light' | 'dark' | 'auto';
export type CurrentTheme = 'light' | 'dark';

interface ThemeState {
  mode: ThemeMode;
  currentTheme: CurrentTheme;
  systemTheme: CurrentTheme;
}

interface ThemeActions {
  setMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
  detectSystemTheme: () => void;
  initializeTheme: () => void;
}

const defaultState: ThemeState = {
  mode: 'auto',
  currentTheme: 'light',
  systemTheme: 'light',
};

// 检测系统主题
const getSystemTheme = (): CurrentTheme => {
  if (typeof window === 'undefined') return 'light';
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

// 计算当前应该使用的主题
const calculateCurrentTheme = (mode: ThemeMode, systemTheme: CurrentTheme): CurrentTheme => {
  switch (mode) {
    case 'light':
      return 'light';
    case 'dark':
      return 'dark';
    case 'auto':
      return systemTheme;
    default:
      return 'light';
  }
};

export const useThemeStore = create<ThemeState & ThemeActions>()(
  persist(
    (set, get) => ({
      ...defaultState,

      setMode: (mode: ThemeMode) => {
        const { systemTheme } = get();
        const currentTheme = calculateCurrentTheme(mode, systemTheme);
        
        set({ mode, currentTheme });
        
        // 应用主题到DOM
        applyThemeToDOM(currentTheme);
      },

      toggleTheme: () => {
        const { mode } = get();
        
        if (mode === 'auto') {
          // 如果当前是自动模式，切换到手动模式
          const { currentTheme } = get();
          const newMode: ThemeMode = currentTheme === 'light' ? 'dark' : 'light';
          get().setMode(newMode);
        } else {
          // 如果是手动模式，在light和dark之间切换
          const newMode: ThemeMode = mode === 'light' ? 'dark' : 'light';
          get().setMode(newMode);
        }
      },

      detectSystemTheme: () => {
        const systemTheme = getSystemTheme();
        const { mode } = get();
        const currentTheme = calculateCurrentTheme(mode, systemTheme);
        
        set({ systemTheme, currentTheme });
        
        // 如果是自动模式，应用新的主题
        if (mode === 'auto') {
          applyThemeToDOM(currentTheme);
        }
      },

      initializeTheme: () => {
        const systemTheme = getSystemTheme();
        const { mode } = get();
        const currentTheme = calculateCurrentTheme(mode, systemTheme);
        
        set({ systemTheme, currentTheme });
        
        // 应用主题到DOM
        applyThemeToDOM(currentTheme);
        
        // 监听系统主题变化
        if (typeof window !== 'undefined') {
          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
          
          const handleSystemThemeChange = (e: MediaQueryListEvent) => {
            const newSystemTheme: CurrentTheme = e.matches ? 'dark' : 'light';
            const { mode } = get();
            const newCurrentTheme = calculateCurrentTheme(mode, newSystemTheme);
            
            set({ systemTheme: newSystemTheme, currentTheme: newCurrentTheme });
            
            if (mode === 'auto') {
              applyThemeToDOM(newCurrentTheme);
            }
          };
          
          // 添加事件监听器
          if (mediaQuery.addEventListener) {
            mediaQuery.addEventListener('change', handleSystemThemeChange);
          } else {
            // 兼容旧版本浏览器
            mediaQuery.addListener(handleSystemThemeChange);
          }
        }
      },
    }),
    {
      name: 'translator-theme-store',
      partialize: (state) => ({
        mode: state.mode,
      }),
    }
  )
);

// 应用主题到DOM
const applyThemeToDOM = (theme: CurrentTheme) => {
  if (typeof document === 'undefined') return;
  
  const root = document.documentElement;
  
  // 移除之前的主题类
  root.classList.remove('theme-light', 'theme-dark');
  
  // 添加新的主题类
  root.classList.add(`theme-${theme}`);
  
  // 设置data属性，用于CSS选择器
  root.setAttribute('data-theme', theme);
  
  // 设置CSS变量
  if (theme === 'dark') {
    root.style.setProperty('--sidebar-bg', 'rgba(30, 30, 30, 0.85)');
    root.style.setProperty('--sidebar-border', 'rgba(255, 255, 255, 0.1)');
    root.style.setProperty('--sidebar-text', '#ffffff');
    root.style.setProperty('--sidebar-text-secondary', '#b0b0b0');
    root.style.setProperty('--sidebar-shadow', 'rgba(0, 0, 0, 0.3)');
    root.style.setProperty('--sidebar-hover', 'rgba(255, 255, 255, 0.05)');
    root.style.setProperty('--sidebar-active', 'rgba(255, 255, 255, 0.1)');
  } else {
    root.style.setProperty('--sidebar-bg', 'rgba(255, 255, 255, 0.85)');
    root.style.setProperty('--sidebar-border', 'rgba(0, 0, 0, 0.1)');
    root.style.setProperty('--sidebar-text', '#000000');
    root.style.setProperty('--sidebar-text-secondary', '#666666');
    root.style.setProperty('--sidebar-shadow', 'rgba(0, 0, 0, 0.15)');
    root.style.setProperty('--sidebar-hover', 'rgba(0, 0, 0, 0.05)');
    root.style.setProperty('--sidebar-active', 'rgba(0, 0, 0, 0.1)');
  }
};

// 获取主题相关的CSS样式
export const getThemeStyles = (theme: CurrentTheme) => {
  const baseStyles = {
    transition: 'all 300ms ease-in-out',
  };

  if (theme === 'dark') {
    return {
      ...baseStyles,
      backgroundColor: 'var(--sidebar-bg)',
      color: 'var(--sidebar-text)',
      borderColor: 'var(--sidebar-border)',
      boxShadow: `0 8px 32px var(--sidebar-shadow)`,
    };
  } else {
    return {
      ...baseStyles,
      backgroundColor: 'var(--sidebar-bg)',
      color: 'var(--sidebar-text)',
      borderColor: 'var(--sidebar-border)',
      boxShadow: `0 8px 32px var(--sidebar-shadow)`,
    };
  }
};

// 导出主题相关的工具函数
export const themeUtils = {
  applyThemeToDOM,
  getSystemTheme,
  calculateCurrentTheme,
  getThemeStyles,
};
