import { defineConfig } from 'wxt';

// See https://wxt.dev/api/config.html
export default defineConfig({
  modules: ['@wxt-dev/module-react'],
  manifest: {
    name: '智能翻译助手',
    description: '划词翻译、截图翻译的浏览器扩展',
    version: '1.0.0',
    permissions: [
      'storage',
      'activeTab',
      'contextMenus',
      'scripting',
      'notifications',
      'tabs'
    ],
    options_ui: {
      page: 'options.html',
      open_in_tab: true
    },
    host_permissions: ['<all_urls>'],
    content_security_policy: {
      extension_pages: "script-src 'self'; object-src 'self'"
    },
    web_accessible_resources: [
      {
        resources: [
          'content-ui.html',
          'assets/*',
          'icons/*',
          'chunks/*'
        ],
        matches: ['<all_urls>']
      }
    ]
  },
  hooks: {
    'build:manifestGenerated': (wxt, manifest) => {
      // 开发环境允许访问本地服务器
      if (wxt.config.mode === 'development') {
        manifest.content_security_policy.extension_pages += ' http://localhost:3000';
      }
    }
  },
  runner: {
    chromiumArgs: ['--disable-web-security', '--allow-running-insecure-content']
  }
});
