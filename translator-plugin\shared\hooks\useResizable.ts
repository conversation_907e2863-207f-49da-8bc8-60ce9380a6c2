import { useCallback, useEffect, useRef, RefObject } from 'react';
import { Size } from '../stores/useSidebarStore';

export interface ResizableOptions {
  minWidth: number;
  maxWidth: number;
  minHeight: number;
  maxHeight: number;
  onResize?: (size: Size) => void;
  onResizeStart?: () => void;
  onResizeEnd?: (size: Size) => void;
  disabled?: boolean;
  handles?: ResizeHandle[];
}

export type ResizeHandle = 'n' | 's' | 'e' | 'w' | 'ne' | 'nw' | 'se' | 'sw';

export interface ResizeHandlers {
  getResizeHandleProps: (handle: ResizeHandle) => {
    onMouseDown: (event: React.MouseEvent) => void;
    onTouchStart: (event: React.TouchEvent) => void;
    style: React.CSSProperties;
  };
}

export const useResizable = (
  elementRef: RefObject<HTMLElement>,
  options: ResizableOptions
): ResizeHandlers => {
  const {
    minWidth,
    maxWidth,
    minHeight,
    maxHeight,
    onResize,
    onResizeStart,
    onResizeEnd,
    disabled = false,
    handles = ['se'], // 默认只有右下角
  } = options;

  const isResizing = useRef(false);
  const resizeStart = useRef({ x: 0, y: 0 });
  const elementStart = useRef({ width: 0, height: 0, x: 0, y: 0 });
  const currentHandle = useRef<ResizeHandle>('se');

  // 约束尺寸
  const constrainSize = useCallback((width: number, height: number): Size => {
    const constrainedWidth = Math.max(minWidth, Math.min(width, maxWidth));
    const constrainedHeight = Math.max(minHeight, Math.min(height, maxHeight));
    
    return { width: constrainedWidth, height: constrainedHeight };
  }, [minWidth, maxWidth, minHeight, maxHeight]);

  // 计算新的尺寸和位置
  const calculateNewDimensions = useCallback((
    deltaX: number,
    deltaY: number,
    handle: ResizeHandle
  ) => {
    const start = elementStart.current;
    let newWidth = start.width;
    let newHeight = start.height;
    let newX = start.x;
    let newY = start.y;

    switch (handle) {
      case 'e': // 右边
        newWidth = start.width + deltaX;
        break;
      case 'w': // 左边
        newWidth = start.width - deltaX;
        newX = start.x + deltaX;
        break;
      case 's': // 下边
        newHeight = start.height + deltaY;
        break;
      case 'n': // 上边
        newHeight = start.height - deltaY;
        newY = start.y + deltaY;
        break;
      case 'se': // 右下角
        newWidth = start.width + deltaX;
        newHeight = start.height + deltaY;
        break;
      case 'sw': // 左下角
        newWidth = start.width - deltaX;
        newHeight = start.height + deltaY;
        newX = start.x + deltaX;
        break;
      case 'ne': // 右上角
        newWidth = start.width + deltaX;
        newHeight = start.height - deltaY;
        newY = start.y + deltaY;
        break;
      case 'nw': // 左上角
        newWidth = start.width - deltaX;
        newHeight = start.height - deltaY;
        newX = start.x + deltaX;
        newY = start.y + deltaY;
        break;
    }

    // 约束尺寸
    const constrainedSize = constrainSize(newWidth, newHeight);
    
    // 如果尺寸被约束，需要调整位置
    if (handle.includes('w') && constrainedSize.width !== newWidth) {
      newX = start.x + (start.width - constrainedSize.width);
    }
    if (handle.includes('n') && constrainedSize.height !== newHeight) {
      newY = start.y + (start.height - constrainedSize.height);
    }

    return {
      width: constrainedSize.width,
      height: constrainedSize.height,
      x: newX,
      y: newY,
    };
  }, [constrainSize]);

  // 鼠标移动处理
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isResizing.current || disabled) return;
    
    event.preventDefault();
    
    const deltaX = event.clientX - resizeStart.current.x;
    const deltaY = event.clientY - resizeStart.current.y;
    
    const newDimensions = calculateNewDimensions(deltaX, deltaY, currentHandle.current);
    
    // 更新元素尺寸和位置
    if (elementRef.current) {
      elementRef.current.style.width = `${newDimensions.width}px`;
      elementRef.current.style.height = `${newDimensions.height}px`;
      elementRef.current.style.left = `${newDimensions.x}px`;
      elementRef.current.style.top = `${newDimensions.y}px`;
    }
    
    // 触发调整事件
    onResize?.({
      width: newDimensions.width,
      height: newDimensions.height,
    });
  }, [disabled, calculateNewDimensions, elementRef, onResize]);

  // 鼠标释放处理
  const handleMouseUp = useCallback((event: MouseEvent) => {
    if (!isResizing.current) return;
    
    isResizing.current = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    
    const deltaX = event.clientX - resizeStart.current.x;
    const deltaY = event.clientY - resizeStart.current.y;
    
    const finalDimensions = calculateNewDimensions(deltaX, deltaY, currentHandle.current);
    
    // 触发调整结束事件
    onResizeEnd?.({
      width: finalDimensions.width,
      height: finalDimensions.height,
    });
    
    // 移除事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [calculateNewDimensions, onResizeEnd, handleMouseMove]);

  // 开始调整大小
  const startResize = useCallback((event: React.MouseEvent, handle: ResizeHandle) => {
    if (disabled) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    const element = elementRef.current;
    if (!element) return;
    
    const rect = element.getBoundingClientRect();
    
    isResizing.current = true;
    currentHandle.current = handle;
    resizeStart.current = { x: event.clientX, y: event.clientY };
    elementStart.current = {
      width: rect.width,
      height: rect.height,
      x: rect.left,
      y: rect.top,
    };
    
    // 设置调整样式
    document.body.style.cursor = getCursorForHandle(handle);
    document.body.style.userSelect = 'none';
    
    // 触发调整开始事件
    onResizeStart?.();
    
    // 添加事件监听器
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [disabled, elementRef, onResizeStart, handleMouseMove, handleMouseUp]);

  // 获取调整手柄的光标样式
  const getCursorForHandle = (handle: ResizeHandle): string => {
    const cursors: Record<ResizeHandle, string> = {
      n: 'n-resize',
      s: 's-resize',
      e: 'e-resize',
      w: 'w-resize',
      ne: 'ne-resize',
      nw: 'nw-resize',
      se: 'se-resize',
      sw: 'sw-resize',
    };
    return cursors[handle];
  };

  // 获取调整手柄的样式
  const getHandleStyle = (handle: ResizeHandle): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      backgroundColor: 'transparent',
      zIndex: 1000,
    };

    const handleSize = 8;
    const cornerSize = 12;

    switch (handle) {
      case 'n':
        return {
          ...baseStyle,
          top: -handleSize / 2,
          left: handleSize,
          right: handleSize,
          height: handleSize,
          cursor: 'n-resize',
        };
      case 's':
        return {
          ...baseStyle,
          bottom: -handleSize / 2,
          left: handleSize,
          right: handleSize,
          height: handleSize,
          cursor: 's-resize',
        };
      case 'e':
        return {
          ...baseStyle,
          right: -handleSize / 2,
          top: handleSize,
          bottom: handleSize,
          width: handleSize,
          cursor: 'e-resize',
        };
      case 'w':
        return {
          ...baseStyle,
          left: -handleSize / 2,
          top: handleSize,
          bottom: handleSize,
          width: handleSize,
          cursor: 'w-resize',
        };
      case 'ne':
        return {
          ...baseStyle,
          top: -cornerSize / 2,
          right: -cornerSize / 2,
          width: cornerSize,
          height: cornerSize,
          cursor: 'ne-resize',
        };
      case 'nw':
        return {
          ...baseStyle,
          top: -cornerSize / 2,
          left: -cornerSize / 2,
          width: cornerSize,
          height: cornerSize,
          cursor: 'nw-resize',
        };
      case 'se':
        return {
          ...baseStyle,
          bottom: -cornerSize / 2,
          right: -cornerSize / 2,
          width: cornerSize,
          height: cornerSize,
          cursor: 'se-resize',
        };
      case 'sw':
        return {
          ...baseStyle,
          bottom: -cornerSize / 2,
          left: -cornerSize / 2,
          width: cornerSize,
          height: cornerSize,
          cursor: 'sw-resize',
        };
      default:
        return baseStyle;
    }
  };

  // 获取调整手柄属性
  const getResizeHandleProps = useCallback((handle: ResizeHandle) => ({
    onMouseDown: (event: React.MouseEvent) => startResize(event, handle),
    onTouchStart: (event: React.TouchEvent) => {
      const touch = event.touches[0];
      if (!touch) return;
      
      const mouseEvent = {
        clientX: touch.clientX,
        clientY: touch.clientY,
        preventDefault: () => event.preventDefault(),
        stopPropagation: () => event.stopPropagation(),
      } as React.MouseEvent;
      
      startResize(mouseEvent, handle);
    },
    style: getHandleStyle(handle),
  }), [startResize]);

  // 清理函数
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  return {
    getResizeHandleProps,
  };
};
