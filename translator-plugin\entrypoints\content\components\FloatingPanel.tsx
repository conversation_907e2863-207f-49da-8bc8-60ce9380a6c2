import React, { useRef, useEffect, useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Box, Paper, IconButton, Typography, CircularProgress } from '@mui/material';
import { EdgeTranslateIcons } from './EdgeTranslateIcons';
import { EdgeTranslateColors, EdgeTranslateShadows, EdgeTranslateTransitions } from '../../../shared/theme/edgeTranslateTheme';

interface FloatingPanelProps {
  visible: boolean;
  position: { x: number; y: number };
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  isPinned?: boolean;
  onPin?: () => void;
  onPositionChange?: (position: { x: number; y: number }) => void;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export const FloatingPanel: React.FC<FloatingPanelProps> = ({
  visible,
  position,
  onClose,
  children,
  title = '翻译结果',
  isPinned = false,
  onPin,
  onPositionChange,
  minWidth = 300,
  minHeight = 200,
  maxWidth = 500,
  maxHeight = 600,
}) => {
  const panelRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [panelSize, setPanelSize] = useState({ width: minWidth, height: minHeight });

  // 创建面板容器
  const createPanelContainer = () => {
    let container = document.getElementById('edge-translate-panel-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'edge-translate-panel-container';
      container.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        z-index: **********;
        pointer-events: none;
      `;
      document.body.appendChild(container);
    }
    return container;
  };

  // 约束位置在视窗内
  const constrainPosition = useCallback((pos: { x: number; y: number }) => {
    const maxX = window.innerWidth - panelSize.width;
    const maxY = window.innerHeight - panelSize.height;
    
    return {
      x: Math.max(0, Math.min(pos.x, maxX)),
      y: Math.max(0, Math.min(pos.y, maxY)),
    };
  }, [panelSize]);

  // 处理拖拽开始
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!isPinned) return; // 只有固定状态下才能拖拽
    
    e.preventDefault();
    e.stopPropagation();

    const rect = panelRef.current?.getBoundingClientRect();
    if (!rect) return;

    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });
    setIsDragging(true);
  }, [isPinned]);

  // 处理拖拽
  useEffect(() => {
    if (!isDragging) return;

    const handleMouseMove = (e: MouseEvent) => {
      const newPosition = {
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y,
      };

      const constrainedPosition = constrainPosition(newPosition);
      onPositionChange?.(constrainedPosition);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset, constrainPosition, onPositionChange]);

  // 面板样式 - 使用EdgeTranslate主题
  const panelStyle: React.CSSProperties = {
    position: 'fixed',
    left: `${position.x}px`,
    top: `${position.y}px`,
    width: `${panelSize.width}px`,
    minHeight: `${minHeight}px`,
    maxHeight: `${maxHeight}px`,
    maxWidth: `${maxWidth}px`,
    zIndex: **********,
    pointerEvents: 'auto',
    opacity: visible ? 1 : 0,
    visibility: visible ? 'visible' : 'hidden',
    transform: isDragging ? 'scale(1.02)' : 'scale(1)',
    transition: isDragging ? 'none' : EdgeTranslateTransitions.common.all,
    boxShadow: isDragging ? EdgeTranslateShadows.panelDragging : EdgeTranslateShadows.panel,
    borderRadius: '12px',
    overflow: 'hidden',
    backgroundColor: EdgeTranslateColors.background.default,
    border: `1px solid ${EdgeTranslateColors.border.light}`,
  };

  // 头部样式 - 使用EdgeTranslate主题
  const headerStyle: React.CSSProperties = {
    background: `linear-gradient(135deg, ${EdgeTranslateColors.primary} 0%, ${EdgeTranslateColors.primaryDark} 100%)`,
    color: 'white',
    padding: '8px 12px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    cursor: isPinned ? 'move' : 'default',
    userSelect: 'none',
    minHeight: '40px',
  };

  // 内容样式 - 使用EdgeTranslate主题
  const contentStyle: React.CSSProperties = {
    padding: '16px',
    maxHeight: `${maxHeight - 60}px`,
    overflowY: 'auto',
    backgroundColor: EdgeTranslateColors.background.default,
  };

  if (!visible) {
    return null;
  }

  const panelElement = (
    <Paper
      ref={panelRef}
      style={panelStyle}
      elevation={0}
    >
      {/* 头部 */}
      <Box
        ref={headerRef}
        style={headerStyle}
        onMouseDown={handleMouseDown}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {isPinned && (
            <EdgeTranslateIcons.Drag size={16} color="rgba(255, 255, 255, 0.8)" />
          )}
          <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
            {title}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          {onPin && (
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                onPin();
              }}
              sx={{
                color: 'white',
                '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' }
              }}
              title={isPinned ? '取消固定' : '固定面板'}
            >
              {isPinned ?
                <EdgeTranslateIcons.Unpin size={18} color="white" /> :
                <EdgeTranslateIcons.Pin size={18} color="white" />
              }
            </IconButton>
          )}

          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            sx={{
              color: 'white',
              '&:hover': { backgroundColor: 'rgba(255, 255, 255, 0.1)' }
            }}
            title="关闭"
          >
            <EdgeTranslateIcons.Close size={18} color="white" />
          </IconButton>
        </Box>
      </Box>

      {/* 内容区域 */}
      <Box style={contentStyle}>
        {children}
      </Box>
    </Paper>
  );

  return createPortal(panelElement, createPanelContainer());
};

// 翻译结果组件
interface TranslationResultProps {
  originalText: string;
  translatedText: string;
  sourceLanguage?: string;
  targetLanguage?: string;
  isLoading?: boolean;
  error?: string;
  onSpeak?: (text: string, language: string) => void;
  onCopy?: (text: string) => void;
  onSwapLanguages?: () => void;
}

export const TranslationResult: React.FC<TranslationResultProps> = ({
  originalText,
  translatedText,
  sourceLanguage = 'auto',
  targetLanguage = 'zh-CN',
  isLoading = false,
  error,
  onSpeak,
  onCopy,
  onSwapLanguages,
}) => {
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
        <EdgeTranslateIcons.Loading size={24} color={EdgeTranslateColors.primary} />
        <Typography variant="body2" sx={{ ml: 2, color: EdgeTranslateColors.text.secondary }}>
          翻译中...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ py: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
        <EdgeTranslateIcons.Error size={20} color={EdgeTranslateColors.error} />
        <Typography variant="body2" sx={{ color: EdgeTranslateColors.error }}>
          翻译失败: {error}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* 原文 */}
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="caption" color="text.secondary">
            原文 ({sourceLanguage})
          </Typography>
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {onSpeak && (
              <IconButton
                size="small"
                onClick={() => onSpeak(originalText, sourceLanguage)}
                title="朗读原文"
                sx={{ color: EdgeTranslateColors.text.secondary }}
              >
                <EdgeTranslateIcons.Speak size={16} />
              </IconButton>
            )}
            {onCopy && (
              <IconButton
                size="small"
                onClick={() => onCopy(originalText)}
                title="复制原文"
                sx={{ color: EdgeTranslateColors.text.secondary }}
              >
                <EdgeTranslateIcons.Copy size={16} />
              </IconButton>
            )}
          </Box>
        </Box>
        <Typography variant="body2" sx={{
          p: 1.5,
          backgroundColor: EdgeTranslateColors.background.grey,
          borderRadius: 1,
          border: '1px solid',
          borderColor: EdgeTranslateColors.border.light,
          color: EdgeTranslateColors.text.primary
        }}>
          {originalText}
        </Typography>
      </Box>

      {/* 语言交换按钮 */}
      {onSwapLanguages && (
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <IconButton
            size="small"
            onClick={onSwapLanguages}
            sx={{
              color: EdgeTranslateColors.primary,
              '&:hover': { backgroundColor: EdgeTranslateColors.secondary }
            }}
            title="交换语言"
          >
            <EdgeTranslateIcons.Exchange size={20} />
          </IconButton>
        </Box>
      )}

      {/* 译文 */}
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="caption" color="text.secondary">
            译文 ({targetLanguage})
          </Typography>
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            {onSpeak && (
              <IconButton
                size="small"
                onClick={() => onSpeak(translatedText, targetLanguage)}
                title="朗读译文"
                sx={{ color: EdgeTranslateColors.text.secondary }}
              >
                <EdgeTranslateIcons.Speak size={16} />
              </IconButton>
            )}
            {onCopy && (
              <IconButton
                size="small"
                onClick={() => onCopy(translatedText)}
                title="复制译文"
                sx={{ color: EdgeTranslateColors.text.secondary }}
              >
                <EdgeTranslateIcons.Copy size={16} />
              </IconButton>
            )}
          </Box>
        </Box>
        <Typography variant="body2" sx={{
          p: 1.5,
          backgroundColor: EdgeTranslateColors.secondary,
          borderRadius: 1,
          border: '1px solid',
          borderColor: EdgeTranslateColors.primary + '33', // 20% opacity
          color: EdgeTranslateColors.primaryDark
        }}>
          {translatedText}
        </Typography>
      </Box>
    </Box>
  );
};
