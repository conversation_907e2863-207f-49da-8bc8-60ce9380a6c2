'use client';

import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>ack,
  Divider
} from '@mui/material';
import {
  Google as GoogleIcon,
  Microsoft as MicrosoftIcon,
  Translate as TranslateIcon
} from '@mui/icons-material';
import { createClient } from '@/lib/supabase';

export default function LoginPage() {
  const supabase = createClient();

  const handleGoogleLogin = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });
      
      if (error) {
        console.error('Google login error:', error);
        alert('Google登录失败，请重试');
      }
    } catch (error) {
      console.error('Google login error:', error);
      alert('Google登录失败，请重试');
    }
  };

  const handleMicrosoftLogin = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'azure',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });
      
      if (error) {
        console.error('Microsoft login error:', error);
        alert('Microsoft登录失败，请重试');
      }
    } catch (error) {
      console.error('Microsoft login error:', error);
      alert('Microsoft登录失败，请重试');
    }
  };

  return (
    <Container maxWidth="sm">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          py: 4
        }}
      >
        <Card sx={{ width: '100%', maxWidth: 400 }}>
          <CardContent sx={{ p: 4 }}>
            {/* Logo和标题 */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <TranslateIcon 
                sx={{ 
                  fontSize: 48, 
                  color: 'primary.main', 
                  mb: 2 
                }} 
              />
              <Typography variant="h4" component="h1" gutterBottom>
                智能翻译助手
              </Typography>
              <Typography variant="body1" color="text.secondary">
                管理后台
              </Typography>
            </Box>

            {/* 登录按钮 */}
            <Stack spacing={2}>
              <Button
                variant="contained"
                size="large"
                startIcon={<GoogleIcon />}
                onClick={handleGoogleLogin}
                sx={{
                  py: 1.5,
                  backgroundColor: '#4285f4',
                  '&:hover': {
                    backgroundColor: '#3367d6'
                  }
                }}
              >
                使用 Google 登录
              </Button>

              <Button
                variant="contained"
                size="large"
                startIcon={<MicrosoftIcon />}
                onClick={handleMicrosoftLogin}
                sx={{
                  py: 1.5,
                  backgroundColor: '#0078d4',
                  '&:hover': {
                    backgroundColor: '#106ebe'
                  }
                }}
              >
                使用 Microsoft 登录
              </Button>
            </Stack>

            <Divider sx={{ my: 3 }} />

            {/* 说明文字 */}
            <Typography 
              variant="body2" 
              color="text.secondary" 
              textAlign="center"
            >
              登录后可以管理AI模型、查看用量统计和配置系统设置
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
}
