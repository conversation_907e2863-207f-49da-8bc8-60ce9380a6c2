### 項目結構
```
+-- config
| +-- webpack.base.config.js
| +-- webpack.dev.config.js
| +-- webpack.prod.config.js
+-- src
| +-- contents
| | +-- pdf.js
| | +-- select.js
| +-- display
| | +-- display.js
| | +-- engine.js
| | +-- template.js
| +-- options
| +-- popup
| +-- background.js
| +-- translate.js
+-- static
| +-- _locales
| +-- icon
| +-- pdf
```
#### config
存放 webpack的三個配置文件，
+ webpack.base.config.js 基礎公共配置
+ webpack.dev.config.js 開發環境配置
+ webpack.prod.config.js 生產環境配置

#### contents
其中包含插件的兩個content scripts
+ pdf.js 用於在瀏覽器讀取.pdf文件時，自動跳轉到自帶的pdf閱讀器(引入了[pdf.js](https://github.com/mozilla/pdf.js))
+ select.js 用於實現劃詞翻譯的功能
  + 劃詞翻譯的邏輯示意圖![diagram](../../images/selecting_translate_diagram.jpg)

#### display
負責在當前窗口中生成側邊欄，展示翻譯結果
在這一模塊中，使用了自己編寫的簡易模板渲染引擎，負責將翻譯結果的結構化的數據和靜態頁面進行渲染，生成用於展示的html內容
+ engine.js
 簡易渲染引擎(render函數)
+ tempalte.js
 存放模板內容
+ display.js
 將獲得的翻譯結果對象使用render()函數渲染出側邊欄中的頁面
 負責生成側邊欄，彈出側邊欄，收回側邊欄

#### options
負責設置界面

#### popup
負責彈出界面
包括'翻譯輸入框'，源語言和目標語言的選項

#### background.js
負責在插件剛安裝時，初始化所有設置項的默認設定，以及運行過程中插件各部分之間的消息轉發。

#### translate.js
負責主要的翻譯功能
+ 發送翻譯請求
+ 處理返回的數據，將其轉換為規整的數據結構
+ 將翻譯結果對像傳給display模塊，由display模塊展示翻譯結果

#### _locales
負責插件的國際化，使插件支持多種語言
目前支持中文簡體，中文繁體，英語

#### icon
存放圖標文件

#### pdf
存放引入的 [pdf.js](https://github.com/mozilla/pdf.js)的文件，在打包時當作靜態文件處理