import React from 'react';
import { 
  <PERSON>, 
  Typography, 
  CircularP<PERSON>ress, 
  <PERSON><PERSON>,
  Chip,
  Icon<PERSON>utton,
  Divider
} from '@mui/material';
import {
  ContentCopy as CopyIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  VolumeUp as VolumeUpIcon
} from '@mui/icons-material';
import { useTranslationStore } from '../../../shared/stores/useTranslationStore';
import { useThemeStore } from '../../../shared/stores/useThemeStore';

interface TranslationResultProps {
  selectedEngine: 'google' | 'openai' | 'kimi';
}

export const TranslationResult: React.FC<TranslationResultProps> = ({ selectedEngine }) => {
  const { currentText, results, isLoading, errors } = useTranslationStore();
  const { currentTheme } = useThemeStore();
  
  const currentResult = results.get(selectedEngine);
  const currentLoading = isLoading.get(selectedEngine);
  const currentError = errors.get(selectedEngine);

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const handleSpeak = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'zh-CN';
      speechSynthesis.speak(utterance);
    }
  };

  if (!currentText) {
    return (
      <Box sx={{ 
        p: 3, 
        textAlign: 'center',
        color: 'text.secondary'
      }}>
        <Typography variant="body2">
          选择文本开始翻译
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }} data-translation-content="true">
      {/* 简化的原文显示 */}
      <Box sx={{ mb: 1.5 }}>
        <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 'medium' }}>
          "{currentText}"
        </Typography>
      </Box>

      {/* 翻译结果 */}
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="caption" color="text.secondary">
            翻译结果
          </Typography>
          <Chip
            label={selectedEngine === 'google' ? 'Google' : selectedEngine === 'openai' ? 'GPT-4o-mini' : 'Kimi'}
            size="small"
            variant="outlined"
            sx={{ fontSize: '0.7rem' }}
          />
        </Box>

        {currentLoading && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 3 }}>
            <CircularProgress size={24} sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary">
              翻译中...
            </Typography>
          </Box>
        )}

        {currentError && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {currentError}
          </Alert>
        )}

        {currentResult && !currentLoading && (
          <Box sx={{
            p: 1.5,
            backgroundColor: currentTheme === 'dark' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(76, 175, 80, 0.05)',
            borderRadius: 1,
            border: '1px solid',
            borderColor: currentTheme === 'dark' ? 'rgba(76, 175, 80, 0.3)' : 'rgba(76, 175, 80, 0.2)',
          }}>
            <Typography variant="body2" sx={{ lineHeight: 1.6, mb: 1 }}>
              {currentResult.translatedText}
            </Typography>
            
            {/* 操作按钮 */}
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <IconButton 
                  size="small" 
                  onClick={() => handleCopy(currentResult.translatedText)}
                  title="复制翻译"
                >
                  <CopyIcon sx={{ fontSize: 16 }} />
                </IconButton>
                <IconButton 
                  size="small" 
                  onClick={() => handleSpeak(currentResult.translatedText)}
                  title="朗读翻译"
                >
                  <VolumeUpIcon sx={{ fontSize: 16 }} />
                </IconButton>
                <IconButton 
                  size="small" 
                  title="收藏"
                >
                  <StarBorderIcon sx={{ fontSize: 16 }} />
                </IconButton>
              </Box>
              

            </Box>
          </Box>
        )}
      </Box>


    </Box>
  );
};
