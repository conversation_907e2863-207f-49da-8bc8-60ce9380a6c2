<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位置和准确性修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 25px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .test-word {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
            min-width: 100px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }
        
        .test-word:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .instructions {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        h2 {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        
        .expected-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .issue-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .fix-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .console-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 15px 0;
        }
        
        .word-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .position-test {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 位置和准确性修复测试</h1>
    
    <div class="instructions">
        <h3>📋 测试目标</h3>
        <p>验证两个关键问题的修复效果：</p>
        <ol>
            <li><strong>下拉选择器位置</strong>：确保下拉菜单正确锚定到头部选择器下方</li>
            <li><strong>翻译准确性</strong>：确保单词翻译结果准确，如"supports"应该翻译为"支持"而不是"支持部门"</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>📍 问题1：下拉选择器位置修复</h2>
        
        <div class="issue-box">
            <strong>原问题：</strong>下拉菜单出现在侧边栏外部的右下角，而不是在头部选择器下方
        </div>
        
        <div class="fix-box">
            <strong>修复方案：</strong>
            <ul>
                <li>恢复disablePortal: false，让菜单渲染到body</li>
                <li>使用更高的z-index (2147483648) 确保在最顶层</li>
                <li>移除可能影响定位的isolation样式</li>
                <li>添加getContentAnchorEl: null确保正确锚定</li>
            </ul>
        </div>
        
        <div class="position-test">
            <h4>🎯 位置测试步骤：</h4>
            <ol>
                <li>选择下面的任意单词</li>
                <li>观察侧边栏出现的位置</li>
                <li><strong>立即点击头部的引擎下拉选择器</strong></li>
                <li>检查下拉菜单是否出现在选择器正下方</li>
                <li>下拉菜单不应该出现在侧边栏外部</li>
            </ol>
        </div>
        
        <div class="test-word">supports</div>
        
        <div class="expected-result">
            <strong>预期结果：</strong>
            <ul>
                <li>✅ 下拉菜单出现在头部选择器正下方</li>
                <li>✅ 下拉菜单不会错位到侧边栏外部</li>
                <li>✅ 菜单选项清晰可见且可点击</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <h2>🎯 问题2：翻译准确性优化</h2>
        
        <div class="issue-box">
            <strong>原问题：</strong>"supports"翻译成"支持部门"而不是"支持"，翻译结果不够准确
        </div>
        
        <div class="fix-box">
            <strong>修复方案：</strong>
            <ul>
                <li>添加selectBestSingleWordTranslation函数，智能选择最佳翻译</li>
                <li>实现评分系统，优先选择简短、基础的翻译</li>
                <li>过滤掉包含"部门"、"机构"等组织词汇的翻译</li>
                <li>优化词典释义解析，使用最准确的释义替代主翻译</li>
                <li>添加常见词汇的优先翻译映射</li>
            </ul>
        </div>
        
        <h3>🧪 单词翻译测试</h3>
        <p>点击下面的单词，观察翻译结果是否准确：</p>
        
        <div class="word-grid">
            <div class="test-word">supports</div>
            <div class="test-word">creates</div>
            <div class="test-word">helps</div>
            <div class="test-word">makes</div>
            <div class="test-word">uses</div>
            <div class="test-word">works</div>
            <div class="test-word">runs</div>
            <div class="test-word">starts</div>
            <div class="test-word">opens</div>
            <div class="test-word">saves</div>
        </div>
        
        <div class="console-box">
            预期控制台日志：<br>
            🎯 Selecting best translation for "supports" from: [...]<br>
            📊 Translation "支持" scored: 65<br>
            📊 Translation "支持部门" scored: 25<br>
            🏆 Best translation selected: "支持" (score: 65)<br>
            📚 Found meanings for verb: ["支持", "支撑", ...]<br>
            🔄 Using dictionary meaning instead: "支持"
        </div>
        
        <div class="expected-result">
            <strong>预期翻译结果：</strong>
            <ul>
                <li>✅ supports → "支持" (不是"支持部门")</li>
                <li>✅ creates → "创建" (不是"创建系统")</li>
                <li>✅ helps → "帮助" (不是"帮助功能")</li>
                <li>✅ makes → "制作" 或 "做"</li>
                <li>✅ uses → "使用" (不是"使用方法")</li>
                <li>✅ 翻译结果简洁准确，避免过于复杂的组合词</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <h2>🔍 调试信息验证</h2>
        
        <div class="console-box">
            打开浏览器控制台，观察以下调试信息：<br><br>
            
            <strong>翻译选择过程：</strong><br>
            📝 All translation options: ["支持", "支持部门", "支撑"]<br>
            🎯 Selecting best translation for "supports"<br>
            🏆 Best translation selected: "支持"<br><br>
            
            <strong>词典解析过程：</strong><br>
            📚 Found meanings for verb: ["支持", "支撑"]<br>
            🔄 Using dictionary meaning instead: "支持"<br><br>
            
            <strong>下拉选择器交互：</strong><br>
            🖱️ Select clicked<br>
            📝 Select onChange triggered: openai<br>
            🔄 Engine change requested: openai
        </div>
        
        <div class="expected-result">
            <strong>验证要点：</strong>
            <ul>
                <li>✅ 控制台显示翻译选择的评分过程</li>
                <li>✅ 最终选择的翻译是最简洁准确的</li>
                <li>✅ 下拉选择器交互日志正常</li>
                <li>✅ 没有位置相关的错误信息</li>
            </ul>
        </div>
    </div>

    <script>
        // 测试辅助功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 位置和准确性测试页面已加载');
            console.log('📊 请观察以下关键信息：');
            console.log('1. 下拉选择器的位置是否正确');
            console.log('2. 单词翻译的准确性');
            console.log('3. 翻译选择的评分过程');
            
            // 添加选择事件监听
            document.addEventListener('mouseup', function() {
                const selection = window.getSelection();
                if (selection && selection.toString().trim()) {
                    const selectedText = selection.toString().trim();
                    console.log(`📝 选择了单词: "${selectedText}"`);
                    
                    // 特别关注supports的翻译
                    if (selectedText.toLowerCase() === 'supports') {
                        console.log('🎯 正在测试"supports"的翻译准确性');
                        console.log('预期结果: "支持" (不是"支持部门")');
                    }
                }
            });
        });
    </script>
</body>
</html>
