{"private": true, "description": "A translator extension.", "keywords": ["Edge Translate", "侧边翻译"], "contributors": [{"name": "nickyc975", "email": "<EMAIL>", "url": "https://github.com/nickyc975"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/<PERSON>-<PERSON>"}], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/EdgeTranslate/EdgeTranslate.git"}, "scripts": {"build": "turbo run build", "test": "turbo run test"}, "devDependencies": {"@commitlint/cli": "^13.2.1", "@commitlint/config-conventional": "^13.2.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^7.32.0", "eslint-config-preact": "^1.3.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-html": "^6.2.0", "eslint-plugin-prettier": "^3.4.1", "husky": "^4.3.8", "jest": "^27.5.1", "prettier": "^2.6.0", "turbo": "^1.2.16"}, "workspaces": ["packages/*"], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}