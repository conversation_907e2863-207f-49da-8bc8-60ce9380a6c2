import React from 'react';
import ReactDOM from 'react-dom/client';
import { performOCR, terminateOCRWorker } from './content/ocr-worker';
import { cropImage, convertScreenToImageCoords } from './content/image-cropper';
import { TranslatorApp } from './content/TranslatorApp';
import { TranslationButton } from './content/components/TranslationButton';
import { FloatingPanel, TranslationResult } from './content/components/FloatingPanel';
import { useSidebarStore } from '../shared/stores/useSidebarStore';
import { useTranslationStore } from '../shared/stores/useTranslationStore';
import { useThemeStore } from '../shared/stores/useThemeStore';
import { useHotkeys, commonHotkeys } from '../shared/hooks/useHotkeys';
import { useTextSelection } from '../shared/hooks/useTextSelection';
import {
  createEdgeTranslateHotkeyManager,
  EdgeTranslateHotkeyActions,
  loadHotkeysFromStorage
} from '../shared/hooks/useEdgeTranslateHotkeys';

export default defineContentScript({
  matches: ['<all_urls>'],

  main(ctx) {
    console.log('🌐 Translator plugin content script loaded on:', window.location.href);

    let selectionTimeout: NodeJS.Timeout | null = null;
    let screenshotMode = false;
    let screenshotOverlay: HTMLElement | null = null;

    // 创建React根容器
    let reactRoot: any = null;
    let containerElement: HTMLDivElement | null = null;

    // EdgeTranslate风格的翻译状态
    let translationButtonRoot: any = null;
    let floatingPanelRoot: any = null;
    let currentTranslation: any = null;
    let hotkeyManager: any = null;

    // 创建容器元素
    const createContainer = () => {
      if (containerElement) return containerElement;

      containerElement = document.createElement('div');
      containerElement.id = 'translator-sidebar-root';
      containerElement.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        z-index: 2147483647;
        pointer-events: none;
      `;
      document.body.appendChild(containerElement);
      return containerElement;
    };

    // 挂载React应用
    const mountReactApp = () => {
      try {
        const container = createContainer();
        console.log('🎯 Mounting React translator app to container:', container);

        reactRoot = ReactDOM.createRoot(container);
        reactRoot.render(<TranslatorApp />);
        console.log('✅ React app mounted successfully');

        return true;
      } catch (error) {
        console.error('❌ Failed to mount React app:', error);
        return false;
      }
    };

    // 卸载React应用
    const unmountReactApp = () => {
      try {
        if (reactRoot) {
          reactRoot.unmount();
          reactRoot = null;
          console.log('✅ React app unmounted successfully');
        }
        if (containerElement) {
          containerElement.remove();
          containerElement = null;
        }
      } catch (error) {
        console.error('❌ Failed to unmount React app:', error);
      }
    };

    // 初始化插件
    initializePlugin();

    function initializePlugin() {
      console.log('🚀 Plugin initialized on:', document.title);

      // 初始化主题系统
      useThemeStore.getState().initializeTheme();

      // 挂载React应用
      const mounted = mountReactApp();
      if (!mounted) {
        console.error('❌ Failed to initialize translator plugin');
        return;
      }

      // 初始化EdgeTranslate风格的选中翻译 - 已禁用，使用新的侧边栏系统
      // const edgeTranslateFeatures = initializeEdgeTranslateFeatures();

      // 初始化快捷键监听
      initializeHotkeys();

      // 扩展右键菜单
      extendContextMenu();
    }

    // 初始化EdgeTranslate风格的功能 - 已废弃，使用新的侧边栏系统
    function initializeEdgeTranslateFeatures() {
      console.log('⚠️ DEPRECATED: initializeEdgeTranslateFeatures() is deprecated, using new sidebar system instead');
      return; // 早期返回，不执行任何功能

      // 创建翻译按钮容器
      const createButtonContainer = () => {
        let container = document.getElementById('edge-translate-button-root');
        if (!container) {
          container = document.createElement('div');
          container.id = 'edge-translate-button-root';
          document.body.appendChild(container);
        }
        return container;
      };

      // 创建浮动面板容器
      const createPanelContainer = () => {
        let container = document.getElementById('edge-translate-panel-root');
        if (!container) {
          container = document.createElement('div');
          container.id = 'edge-translate-panel-root';
          document.body.appendChild(container);
        }
        return container;
      };

      // 翻译按钮状态
      let buttonVisible = false;
      let buttonPosition = { x: 0, y: 0 };
      let selectedText = '';

      // 浮动面板状态
      let panelVisible = false;
      let panelPosition = { x: 100, y: 100 };
      let panelPinned = false;

      // 渲染翻译按钮
      const renderTranslationButton = () => {
        const container = createButtonContainer();

        if (!translationButtonRoot) {
          translationButtonRoot = ReactDOM.createRoot(container);
        }

        translationButtonRoot.render(
          <TranslationButton
            visible={buttonVisible}
            position={buttonPosition}
            onTranslate={handleTranslateClick}
            onClose={hideTranslationButton}
          />
        );
      };

      // 渲染浮动面板
      const renderFloatingPanel = () => {
        const container = createPanelContainer();

        if (!floatingPanelRoot) {
          floatingPanelRoot = ReactDOM.createRoot(container);
        }

        floatingPanelRoot.render(
          <FloatingPanel
            visible={panelVisible}
            position={panelPosition}
            isPinned={panelPinned}
            onClose={hideFloatingPanel}
            onPin={togglePanelPin}
            onPositionChange={updatePanelPosition}
            title="EdgeTranslate"
          >
            {currentTranslation ? (
              <TranslationResult
                originalText={currentTranslation.originalText}
                translatedText={currentTranslation.translatedText}
                sourceLanguage={currentTranslation.sourceLanguage}
                targetLanguage={currentTranslation.targetLanguage}
                isLoading={currentTranslation.isLoading}
                error={currentTranslation.error}
                onSpeak={handleSpeak}
                onCopy={handleCopy}
                onSwapLanguages={handleSwapLanguages}
              />
            ) : (
              <div>请选择文本进行翻译</div>
            )}
          </FloatingPanel>
        );
      };

      // 显示翻译按钮
      const showTranslationButton = (text: string, position: { x: number; y: number }) => {
        selectedText = text;
        buttonPosition = position;
        buttonVisible = true;
        renderTranslationButton();
      };

      // 隐藏翻译按钮
      const hideTranslationButton = () => {
        buttonVisible = false;
        renderTranslationButton();
      };

      // 显示浮动面板
      const showFloatingPanel = () => {
        panelVisible = true;
        renderFloatingPanel();
      };

      // 隐藏浮动面板
      const hideFloatingPanel = () => {
        panelVisible = false;
        renderFloatingPanel();
      };

      // 切换面板固定状态
      const togglePanelPin = () => {
        panelPinned = !panelPinned;
        renderFloatingPanel();
      };

      // 更新面板位置
      const updatePanelPosition = (newPosition: { x: number; y: number }) => {
        panelPosition = newPosition;
        renderFloatingPanel();
      };

      // 处理翻译按钮点击
      const handleTranslateClick = async () => {
        if (!selectedText) return;

        hideTranslationButton();

        // 设置加载状态
        currentTranslation = {
          originalText: selectedText,
          translatedText: '',
          sourceLanguage: 'auto',
          targetLanguage: 'zh-CN',
          isLoading: true,
          error: null,
        };

        showFloatingPanel();

        try {
          // 调用翻译API
          const response = await chrome.runtime.sendMessage({
            type: 'TRANSLATE_TEXT',
            payload: {
              text: selectedText,
              from: 'auto',
              to: 'zh-CN'
            }
          });

          if (response.success) {
            currentTranslation = {
              originalText: selectedText,
              translatedText: response.data.translatedText,
              sourceLanguage: response.data.sourceLanguage || 'auto',
              targetLanguage: response.data.targetLanguage || 'zh-CN',
              isLoading: false,
              error: null,
            };
          } else {
            currentTranslation = {
              ...currentTranslation,
              isLoading: false,
              error: response.error || '翻译失败',
            };
          }
        } catch (error) {
          currentTranslation = {
            ...currentTranslation,
            isLoading: false,
            error: '翻译服务不可用',
          };
        }

        renderFloatingPanel();
      };

      // 处理语音播放
      const handleSpeak = (text: string, language: string) => {
        if ('speechSynthesis' in window) {
          const utterance = new SpeechSynthesisUtterance(text);
          utterance.lang = language === 'zh-CN' ? 'zh-CN' : 'en-US';
          speechSynthesis.speak(utterance);
        }
      };

      // 处理复制
      const handleCopy = async (text: string) => {
        try {
          await navigator.clipboard.writeText(text);
          console.log('Text copied to clipboard');
        } catch (error) {
          console.error('Failed to copy text:', error);
        }
      };

      // 处理语言交换
      const handleSwapLanguages = () => {
        // TODO: 实现语言交换逻辑
        console.log('Swap languages clicked');
      };

      // 初始化文本选择监听
      initializeTextSelection();

      function initializeTextSelection() {
        let selectionTimeout: NodeJS.Timeout | null = null;
        let lastSelectedText = '';

        // 获取选中文本和位置
        const getSelectionInfo = () => {
          const selection = window.getSelection();
          if (!selection || selection.rangeCount === 0) {
            return null;
          }

          const text = selection.toString().trim();

          // 检查文本长度
          if (text.length < 1 || text.length > 1000) {
            return null;
          }

          // 检查是否是PDF.js环境
          const isPDFjs = window.location.href.includes('pdf') ||
                          document.querySelector('#viewer') !== null ||
                          document.querySelector('.page') !== null;

          const cleanText = isPDFjs ? text.replace(/\n/g, ' ') : text;

          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();

          // 计算按钮位置 (TopRight)
          let buttonX = rect.right + 10;
          let buttonY = rect.top - 10;

          // 确保按钮不会超出视窗边界
          const maxX = window.innerWidth - 50;
          const maxY = window.innerHeight - 50;

          buttonX = Math.max(10, Math.min(buttonX, maxX));
          buttonY = Math.max(10, Math.min(buttonY, maxY));

          return {
            text: cleanText,
            position: { x: buttonX, y: buttonY },
            rect,
          };
        };

        // 处理文本选择
        const handleTextSelection = () => {
          if (selectionTimeout) {
            clearTimeout(selectionTimeout);
          }

          selectionTimeout = setTimeout(() => {
            const selectionInfo = getSelectionInfo();

            if (selectionInfo && selectionInfo.text !== lastSelectedText) {
              lastSelectedText = selectionInfo.text;
              console.log('📝 Text selected:', selectionInfo.text);
              showTranslationButton(selectionInfo.text, selectionInfo.position);
            } else if (!selectionInfo) {
              lastSelectedText = '';
              hideTranslationButton();
            }
          }, 100);
        };

        // 检查是否应该忽略选择
        const shouldIgnoreSelection = (target: EventTarget | null): boolean => {
          if (!target || !(target instanceof Element)) return false;

          const ignoredElements = ['input', 'textarea', 'select', 'button'];
          const tagName = target.tagName.toLowerCase();
          if (ignoredElements.includes(tagName)) return true;

          if (target.isContentEditable) return true;

          const ignoredClasses = [
            'translator-sidebar',
            'translation-button',
            'edge-translate-button',
          ];

          for (const className of ignoredClasses) {
            if (target.classList.contains(className) || target.closest(`.${className}`)) {
              return true;
            }
          }

          return false;
        };

        // 事件监听器
        const handleMouseUp = (event: MouseEvent) => {
          if (shouldIgnoreSelection(event.target)) return;
          handleTextSelection();
        };

        const handleDoubleClick = (event: MouseEvent) => {
          if (shouldIgnoreSelection(event.target)) return;
          setTimeout(() => handleTextSelection(), 50);
        };

        const handleTripleClick = (event: MouseEvent) => {
          if (shouldIgnoreSelection(event.target)) return;
          if ((event as any).detail === 3) {
            setTimeout(() => handleTextSelection(), 50);
          }
        };

        const handleKeyDown = (event: KeyboardEvent) => {
          if (event.key === 'Escape') {
            hideTranslationButton();
          }
        };

        const handleScroll = () => {
          hideTranslationButton();
        };

        // 添加事件监听器
        document.addEventListener('mouseup', handleMouseUp);
        document.addEventListener('dblclick', handleDoubleClick);
        document.addEventListener('click', handleTripleClick);
        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('scroll', handleScroll, true);

        // 返回清理函数
        return () => {
          document.removeEventListener('mouseup', handleMouseUp);
          document.removeEventListener('dblclick', handleDoubleClick);
          document.removeEventListener('click', handleTripleClick);
          document.removeEventListener('keydown', handleKeyDown);
          document.removeEventListener('scroll', handleScroll, true);

          if (selectionTimeout) {
            clearTimeout(selectionTimeout);
          }
        };
      }

      // 初始化EdgeTranslate快捷键系统
      const initializeHotkeys = async () => {
        const hotkeyActions: EdgeTranslateHotkeyActions = {
          togglePanel: () => {
            if (panelVisible) {
              hideFloatingPanel();
            } else {
              showFloatingPanel();
            }
          },
          pinPanel: () => {
            togglePanelPin();
          },
          closePanel: () => {
            hideFloatingPanel();
            hideTranslationButton();
          },
          swapLanguages: () => {
            handleSwapLanguages();
          },
          translateSelectedText: () => {
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
              const text = selection.toString().trim();
              const range = selection.getRangeAt(0);
              const rect = range.getBoundingClientRect();

              showTranslationButton(text, {
                x: rect.right + 10,
                y: rect.top - 10
              });
            }
          },
          showSettings: () => {
            // 打开设置页面
            chrome.runtime.sendMessage({ type: 'OPEN_OPTIONS' });
          },
          toggleSidebar: () => {
            // 切换侧边栏显示
            const sidebarStore = useSidebarStore.getState();
            sidebarStore.toggleSidebar();
          },
          focusInput: () => {
            // 聚焦到输入框 - 如果面板可见
            if (panelVisible) {
              const input = document.querySelector('.translation-input') as HTMLInputElement;
              if (input) {
                input.focus();
              }
            }
          },
          clearInput: () => {
            // 清空输入框
            const input = document.querySelector('.translation-input') as HTMLInputElement;
            if (input) {
              input.value = '';
              input.dispatchEvent(new Event('input', { bubbles: true }));
            }
          },
          copyResult: () => {
            // 复制翻译结果
            if (currentTranslation && currentTranslation.translatedText) {
              handleCopy(currentTranslation.translatedText);
            }
          },
          speakOriginal: () => {
            // 朗读原文
            if (currentTranslation && currentTranslation.originalText) {
              handleSpeak(currentTranslation.originalText, currentTranslation.sourceLanguage);
            }
          },
          speakTranslation: () => {
            // 朗读译文
            if (currentTranslation && currentTranslation.translatedText) {
              handleSpeak(currentTranslation.translatedText, currentTranslation.targetLanguage);
            }
          },
        };

        try {
          const hotkeys = await loadHotkeysFromStorage();
          hotkeyManager = createEdgeTranslateHotkeyManager(hotkeyActions, hotkeys);
          console.log('🎹 EdgeTranslate hotkeys initialized');
        } catch (error) {
          console.error('Failed to initialize hotkeys:', error);
        }
      };

      // 初始渲染
      renderTranslationButton();
      renderFloatingPanel();

      // 初始化快捷键
      initializeHotkeys();

      return {
        showTranslationButton,
        hideTranslationButton,
        showFloatingPanel,
        hideFloatingPanel,
        hotkeyManager,
      };
    }

    // 初始化快捷键监听
    function initializeHotkeys() {
      // 使用新的useHotkeys Hook
      const hotkeyConfigs = [
        {
          ...commonHotkeys.TOGGLE_SIDEBAR,
          handler: () => {
            console.log('🔥 Alt+Q pressed, toggling sidebar');
            useSidebarStore.getState().toggleVisibility();
          },
        },
        {
          ...commonHotkeys.TOGGLE_PIN,
          handler: () => {
            console.log('🔥 Ctrl+Shift+P pressed, toggling pin');
            useSidebarStore.getState().togglePin();
          },
        },
        {
          ...commonHotkeys.TOGGLE_THEME,
          handler: () => {
            console.log('🔥 Ctrl+Shift+T pressed, toggling theme');
            useThemeStore.getState().toggleTheme();
          },
        },
      ];

      // 注册快捷键
      hotkeyConfigs.forEach((config, index) => {
        const handleKeyDown = (event: KeyboardEvent) => {
          // 检查快捷键匹配
          const matches =
            event.key.toLowerCase() === config.key.toLowerCase() &&
            event.ctrlKey === config.modifiers.includes('ctrl') &&
            event.altKey === config.modifiers.includes('alt') &&
            event.shiftKey === config.modifiers.includes('shift') &&
            event.metaKey === config.modifiers.includes('meta');

          if (matches) {
            // 检查是否在输入框中
            const activeElement = document.activeElement;
            const isInInput = activeElement && (
              activeElement.tagName === 'INPUT' ||
              activeElement.tagName === 'TEXTAREA' ||
              activeElement.contentEditable === 'true'
            );

            if (!isInInput) {
              event.preventDefault();
              event.stopPropagation();
              config.handler(event);
            }
          }
        };

        document.addEventListener('keydown', handleKeyDown);

        // 清理函数
        ctx.onInvalidated(() => {
          document.removeEventListener('keydown', handleKeyDown);
        });
      });
    }

    // 扩展右键菜单
    function extendContextMenu() {
      // 监听来自background script和popup的消息
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('📨 Content script received message:', message);

        try {
          switch (message.type) {
            case 'SIDEBAR_TRANSLATE':
              if (message.payload?.text) {
                handleSidebarTranslate(message.payload.text);
              }
              sendResponse({ success: true });
              break;

            case 'TOGGLE_SIDEBAR':
              console.log('🔄 Toggling sidebar visibility');
              useSidebarStore.getState().toggleVisibility();
              sendResponse({ success: true });
              break;

            case 'START_SCREENSHOT_TRANSLATION':
              console.log('🎯 Starting screenshot mode');
              startScreenshotMode();
              sendResponse({ success: true });
              break;

            case 'SHOW_TRANSLATION':
              console.log('🎯 Showing translation for:', message.payload?.text);
              if (message.payload?.text) {
                showTranslatorSidebar(message.payload.text, { x: 100, y: 100 });
              }
              sendResponse({ success: true });
              break;

            default:
              console.log('❓ Unknown message type:', message.type);
              sendResponse({ success: false, error: 'Unknown message type' });
              return false;
          }
        } catch (error) {
          console.error('💥 Message handling error:', error);
          sendResponse({ success: false, error: (error as Error).message });
        }

        return true; // 保持消息通道开放
      });

      console.log('🖱️ Context menu extension initialized');
    }

    // 处理侧边栏翻译
    function handleSidebarTranslate(text: string) {
      console.log('🎯 Handling sidebar translate for text:', text);

      // 显示侧边栏
      const sidebarStore = useSidebarStore.getState();
      const translationStore = useTranslationStore.getState();

      // 如果侧边栏未显示，则显示它
      if (!sidebarStore.isVisible) {
        sidebarStore.toggleVisibility();
      }

      // 开始翻译
      translationStore.translateText(text, ['google', 'openai', 'kimi']);
    }

    // 划词检测
    const handleMouseUp = (event: MouseEvent) => {
      console.log('🖱️ Mouse up detected');

      // 清除之前的定时器
      if (selectionTimeout) {
        clearTimeout(selectionTimeout);
      }

      // 400ms延迟触发
      selectionTimeout = setTimeout(() => {
        checkSelection(event);
      }, 400);
    };

    const checkSelection = async (event: MouseEvent) => {
      // 简化逻辑：只阻止翻译结果内容区域的文本选择
      const target = event.target as Element;
      const sidebarContainer = document.getElementById('translator-sidebar-root');

      if (sidebarContainer && sidebarContainer.contains(target)) {
        // 检查是否在翻译结果内容区域
        const isContentArea = target.closest('[data-translation-content="true"]');

        if (isContentArea) {
          console.log('🚫 Click inside translation content, ignoring selection');
          return;
        }

        // 其他区域（头部、按钮等）都允许正常处理
        console.log('✅ Click in sidebar interactive area, allowing');
      }

      const selection = window.getSelection();
      console.log('🔍 Checking selection:', selection);

      if (!selection || selection.rangeCount === 0) {
        console.log('❌ No selection found');
        return;
      }

      const selectedText = selection.toString().trim();
      console.log('📝 Selected text:', selectedText);

      if (selectedText.length === 0) {
        console.log('❌ Empty selection');
        return;
      }

      if (selectedText.length > 1000) {
        console.log('❌ Text too long:', selectedText.length);
        return;
      }

      // 获取选择区域的位置
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      console.log('📍 Selection position:', rect);

      // 使用新的React组件显示翻译侧边栏
      showTranslatorSidebar(selectedText, {
        x: rect.right + 10,
        y: rect.top
      });
    };

    const showTranslatorSidebar = (text: string, position: { x: number; y: number }) => {
      console.log('🎯 Showing translator sidebar for text:', text);
      console.log('📍 Position:', position);

      // 计算侧边栏位置，确保不超出视口
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const sidebarWidth = 300;
      const sidebarHeight = 500;

      let finalX = position.x;
      let finalY = position.y;

      // 如果超出右边界，显示在左侧
      if (finalX + sidebarWidth > viewportWidth) {
        finalX = position.x - sidebarWidth - 20;
      }

      // 如果超出下边界，向上调整
      if (finalY + sidebarHeight > viewportHeight) {
        finalY = viewportHeight - sidebarHeight - 20;
      }

      // 确保不超出上边界和左边界
      finalX = Math.max(20, finalX);
      finalY = Math.max(20, finalY);

      console.log('📍 Calculated position:', {
        original: position,
        final: { x: finalX, y: finalY },
        viewport: { width: viewportWidth, height: viewportHeight }
      });

      // 更新store状态
      const sidebarStore = useSidebarStore.getState();
      const translationStore = useTranslationStore.getState();

      // 只有在未钉住状态下才更新位置
      if (!sidebarStore.isPinned) {
        sidebarStore.updatePosition({ x: finalX, y: finalY });
      }

      // 确保侧边栏显示（而不是切换）
      if (!sidebarStore.isVisible) {
        sidebarStore.toggleVisibility();
      }

      // 开始翻译
      console.log('🚀 Starting translation for text:', text);
      translationStore.translateText(text, ['google', 'openai', 'kimi']);

      // 添加调试信息
      console.log('📊 Current sidebar state:', {
        isVisible: sidebarStore.isVisible,
        position: sidebarStore.position,
        size: sidebarStore.size
      });
    };






    // 截图翻译功能
    const startScreenshotMode = () => {
      screenshotMode = true;
      document.body.style.cursor = 'crosshair';

      // 创建截图覆盖层
      screenshotOverlay = document.createElement('div');
      screenshotOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        z-index: 9999;
        cursor: crosshair;
        user-select: none;
      `;

      // 添加提示文字
      const hint = document.createElement('div');
      hint.style.cssText = `
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        z-index: 10000;
      `;
      hint.textContent = '拖拽选择要翻译的区域，按ESC取消';
      screenshotOverlay.appendChild(hint);

      document.body.appendChild(screenshotOverlay);

      // 绑定截图事件
      let isDrawing = false;
      let startX = 0, startY = 0;
      let selectionBox: HTMLElement | null = null;

      const handleMouseDown = (e: MouseEvent) => {
        if (!screenshotMode) return;
        isDrawing = true;
        startX = e.clientX;
        startY = e.clientY;

        // 创建选择框
        selectionBox = document.createElement('div');
        selectionBox.style.cssText = `
          position: fixed;
          border: 2px solid #1976d2;
          background: rgba(25, 118, 210, 0.1);
          z-index: 10001;
          pointer-events: none;
        `;
        document.body.appendChild(selectionBox);
      };

      const handleMouseMove = (e: MouseEvent) => {
        if (!screenshotMode || !isDrawing || !selectionBox) return;

        const currentX = e.clientX;
        const currentY = e.clientY;

        const left = Math.min(startX, currentX);
        const top = Math.min(startY, currentY);
        const width = Math.abs(currentX - startX);
        const height = Math.abs(currentY - startY);

        selectionBox.style.left = left + 'px';
        selectionBox.style.top = top + 'px';
        selectionBox.style.width = width + 'px';
        selectionBox.style.height = height + 'px';
      };

      const handleMouseUp = async (e: MouseEvent) => {
        if (!screenshotMode || !isDrawing || !selectionBox) return;

        isDrawing = false;
        const rect = selectionBox.getBoundingClientRect();

        console.log('📐 Selection area:', rect);
        console.log('📏 Selection size:', { width: rect.width, height: rect.height });

        // 检查选择区域大小（降低最小尺寸要求）
        if (rect.width < 10 || rect.height < 10) {
          console.log('❌ Selection too small, minimum 10x10 pixels required');
          exitScreenshotMode();
          return;
        }

        console.log('✅ Selection size valid, starting capture...');

        // 截图并OCR
        try {
          await captureAndTranslate(rect);
        } catch (error) {
          console.error('💥 Screenshot translation failed:', error);
          showError('截图翻译失败: ' + (error as Error).message);
        } finally {
          // 确保清理UI
          exitScreenshotMode();
        }
      };

      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          exitScreenshotMode();
        }
      };

      screenshotOverlay.addEventListener('mousedown', handleMouseDown);
      screenshotOverlay.addEventListener('mousemove', handleMouseMove);
      screenshotOverlay.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('keydown', handleKeyDown);

      // 存储事件处理器以便清理
      (screenshotOverlay as any)._handlers = {
        mousedown: handleMouseDown,
        mousemove: handleMouseMove,
        mouseup: handleMouseUp,
        keydown: handleKeyDown
      };
    };

    const exitScreenshotMode = () => {
      console.log('🚪 Exiting screenshot mode');

      screenshotMode = false;
      document.body.style.cursor = '';

      if (screenshotOverlay) {
        const handlers = (screenshotOverlay as any)._handlers;
        if (handlers) {
          screenshotOverlay.removeEventListener('mousedown', handlers.mousedown);
          screenshotOverlay.removeEventListener('mousemove', handlers.mousemove);
          screenshotOverlay.removeEventListener('mouseup', handlers.mouseup);
          document.removeEventListener('keydown', handlers.keydown);
        }
        screenshotOverlay.remove();
        screenshotOverlay = null;
      }

      // 清理所有选择框（但不包括翻译侧边栏）
      const selectionBoxes = document.querySelectorAll('div[style*="border: 2px solid #1976d2"][style*="position: fixed"]');
      console.log('🧹 Cleaning up selection boxes:', selectionBoxes.length);
      selectionBoxes.forEach(box => {
        // 确保不删除翻译侧边栏内的元素
        if (!box.closest('#translator-sidebar')) {
          console.log('🗑️ Removing selection box:', box);
          box.remove();
        }
      });
    };

    const captureAndTranslate = async (rect: DOMRect) => {
      console.log('📸 Starting screenshot capture:', rect);

      try {
        // 第一步：截图
        const screenshotResult = await chrome.runtime.sendMessage({
          type: 'CAPTURE_SCREENSHOT_ONLY',
          payload: {
            x: rect.left,
            y: rect.top,
            width: rect.width,
            height: rect.height
          }
        });

        console.log('📸 Screenshot captured:', screenshotResult?.success);

        if (screenshotResult && screenshotResult.success && screenshotResult.data) {
          // 第二步：在content script中进行图片裁剪
          console.log('✂️ Cropping image in content script...');
          try {
            const croppedImage = await cropImage(
              screenshotResult.data.imageData,
              rect.left,
              rect.top,
              rect.width,
              rect.height
            );

            console.log('✅ Image cropped successfully');

            // 第三步：在content script中进行OCR
            console.log('🔍 Starting OCR in content script...');
            const ocrResult = await performOCR(croppedImage);

            console.log('✅ OCR completed:', ocrResult);

            if (ocrResult.text && ocrResult.text.trim() && !ocrResult.text.includes('[OCR')) {
              console.log('✅ OCR识别成功，文本长度:', ocrResult.text.length);
              console.log('📝 OCR识别文本:', ocrResult.text.substring(0, 200) + '...');

              // 检查文本长度，如果过长则截取前1000字符
              let textToTranslate = ocrResult.text;
              if (textToTranslate.length > 1000) {
                textToTranslate = textToTranslate.substring(0, 1000);
                console.log('⚠️ 文本过长，截取前1000字符进行翻译');
              }

              // 显示翻译结果
              showTranslatorSidebar(textToTranslate, {
                x: rect.right + 10,
                y: rect.top
              });
            } else {
              showError(ocrResult.text || 'OCR识别失败，请重试');
            }
          } catch (cropError) {
            console.error('💥 Image cropping failed:', cropError);
            showError('图片裁剪失败: ' + (cropError as Error).message);
          }
        } else {
          console.log('❌ Screenshot failed:', screenshotResult);
          showError('截图失败，请重试');
        }
      } catch (error) {
        console.error('💥 Capture and translate error:', error);
        showError('截图翻译失败: ' + (error as Error).message);
      }
    };

    const showError = (message: string) => {
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f44336;
        color: white;
        padding: 12px 16px;
        border-radius: 4px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        z-index: 10000;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      `;
      errorDiv.textContent = message;
      document.body.appendChild(errorDiv);

      setTimeout(() => {
        errorDiv.remove();
      }, 3000);
    };

    // 统一的消息监听器（已在extendContextMenu中处理）
    // 这里不需要重复的监听器

    // 添加事件监听器
    document.addEventListener('mouseup', handleMouseUp);

    // 清理函数
    ctx.onInvalidated(() => {
      console.log('🧹 Cleaning up translator plugin');

      document.removeEventListener('mouseup', handleMouseUp);

      if (selectionTimeout) {
        clearTimeout(selectionTimeout);
      }

      if (currentSidebar) {
        currentSidebar.remove();
      }

      if (screenshotOverlay) {
        exitScreenshotMode();
      }

      // 卸载React应用
      unmountReactApp();
    });
  },
});
