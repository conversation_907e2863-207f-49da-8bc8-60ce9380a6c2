import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../lib/supabase';

// GET /api/words - 获取用户生词本
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    
    // 获取查询参数
    const userId = searchParams.get('user_id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sort_by') || 'created_at';
    const sortOrder = searchParams.get('sort_order') || 'desc';
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // 构建查询
    let query = supabase
      .from('user_words')
      .select('*', { count: 'exact' })
      .eq('user_id', userId);

    // 搜索过滤
    if (search) {
      query = query.or(`word.ilike.%${search}%,translation.ilike.%${search}%`);
    }

    // 排序
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // 分页
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data: words, error, count } = await query;

    if (error) {
      console.error('Get words error:', error);
      return NextResponse.json({ error: 'Failed to fetch words' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: {
        words: words || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/words - 添加生词
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const body = await request.json();
    
    const {
      user_id,
      word,
      translation,
      source_language = 'en',
      target_language = 'zh',
      context,
      pronunciation,
      part_of_speech,
      difficulty_level = 1,
    } = body;

    // 验证必需字段
    if (!user_id || !word || !translation) {
      return NextResponse.json(
        { error: 'User ID, word, and translation are required' },
        { status: 400 }
      );
    }

    // 检查是否已存在相同的生词
    const { data: existingWord } = await supabase
      .from('user_words')
      .select('id')
      .eq('user_id', user_id)
      .eq('word', word.toLowerCase())
      .eq('source_language', source_language)
      .eq('target_language', target_language)
      .single();

    if (existingWord) {
      return NextResponse.json(
        { error: 'Word already exists in vocabulary' },
        { status: 409 }
      );
    }

    // 插入新生词
    const { data: newWord, error } = await supabase
      .from('user_words')
      .insert({
        user_id,
        word: word.toLowerCase(),
        translation,
        source_language,
        target_language,
        context,
        pronunciation,
        part_of_speech,
        difficulty_level,
        review_count: 0,
      })
      .select()
      .single();

    if (error) {
      console.error('Create word error:', error);
      return NextResponse.json({ error: 'Failed to create word' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: newWord }, { status: 201 });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/words - 删除生词
export async function DELETE(request: NextRequest) {
  try {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    
    const wordId = searchParams.get('id');
    const userId = searchParams.get('user_id');

    if (!wordId || !userId) {
      return NextResponse.json(
        { error: 'Word ID and User ID are required' },
        { status: 400 }
      );
    }

    // 删除生词（确保只能删除自己的生词）
    const { error } = await supabase
      .from('user_words')
      .delete()
      .eq('id', wordId)
      .eq('user_id', userId);

    if (error) {
      console.error('Delete word error:', error);
      return NextResponse.json({ error: 'Failed to delete word' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Word deleted successfully' });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
