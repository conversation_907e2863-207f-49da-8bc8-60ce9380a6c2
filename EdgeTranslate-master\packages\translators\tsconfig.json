{"compilerOptions": {"rootDir": "src", "outDir": "dist", "declaration": true, "declarationDir": "dist/types", "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM"], "moduleResolution": "Node", "strict": true, "sourceMap": false, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "skipLibCheck": true}, "exclude": ["vite.config.ts", "test"]}