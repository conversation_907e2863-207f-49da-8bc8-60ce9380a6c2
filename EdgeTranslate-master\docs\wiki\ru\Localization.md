### Локализация

Мы разработали это расширение, чтобы помочь людям в преодолении языкового барьера. Но в мире очень много языков, а мы говорим только на английском и китайском. И поэтому пока наше программное обеспечение поддерживает только следующие языки:

- Английский
  
- Китайский упрощенный
  
- Китайский традиционный

- Русский (спасибо за это [ElectroLom](https://github.com/electrolom42) и [Viktor](https://github.com/ViktorOn))

- Японский (спасибо за это [ykyuki](https://github.com/ykyuki))

Если вашего родного языка нет в списке выше, а вы бы хотели добавить его поддержку в приложение, то мы будем вам очень благодарны за помощь.

### Как добавить поддержку языка

1. Сделайте форк нашего [проекта](https://github.com/EdgeTranslate/EdgeTranslate).

2. Добавьте папку с названием `localeCode` по этому пути: `static/_locales`, где `localeCode` — это код, такой как `en` для английского и `ru` для русского. Вы можете найти коды большинства языков [здесь](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/src/popup/languages.js).

3. Скопируйте файл `static/_locales/en/messages.json` в новую папку `localeCode`.

4. Переведите содержимое `messages.json`.

Например, в `AppName` есть `"​message​"​: ​"​Edge Translate"` и `"​description​"​: ​"​App Name​"`. Вам нужно перевести `Edge Translate` и `App Name` на ваш язык. И ещё, перевод названий языков, которые начинаются с `Afrikaans`, делать совсем не обязательно.

5. Зафиксируйте изменения и отправьте их в проект, форк которого вы сделали ранее.

6. Создайте новый [PR](https://github.com/EdgeTranslate/EdgeTranslate/pulls) в нашем репозитории. И в скором времени мы рассмотрим ваш запрос на слияние (Pull Request).

### Больше информации

Дополнительные сведения читайте в [документации Chrome для разработчиков](https://developer.chrome.com/extensions/i18n).
