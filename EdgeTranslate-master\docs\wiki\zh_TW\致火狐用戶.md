## 各位火狐瀏覽器用戶你們好

我是側邊翻譯的開發者之一，主要負責側邊翻譯在火狐瀏覽器上的上架管理。

首先感謝各位對於側邊翻譯的喜愛，正是因為有你們的支持，側邊翻譯才能走到今天，真的很謝謝你們！

但是我很抱歉的通知各位，我們將在火狐瀏覽器版的側邊翻譯中刪除網頁翻譯相關功能（其他平台的版本不受影響），我們對此感到很遺憾，但是我們別無選擇。

簡單來說，為了實現網頁翻譯功能，我們需要使用谷歌的網頁翻譯組件，但這些組件都是私有的，我們無法獲得它們的源碼。而現在，火狐要求我們提供我們所用到的所有第三方組件的源碼，否則我們的擴展就會被從商店中移除。審核人員曾建議我們找找開源的組件，但我們找不到，所以只能刪除這個功能以求通過審核。

這個插件是我們利用空閒時間開發的作品，凝聚了我們很多的心血，被迫刪除這個辛苦實現的功能讓我們很痛心，加上火狐瀏覽器自身的許許多多的問題（如不支持使用擴展內置的 pdf 閱讀器打開 pdf 文件，導致側邊翻譯的 pdf 劃詞功能失效；不支持在通知中添加按鈕，導致我們需要花額外的時間去兼容等等等等）帶來的額外的開發成本，我們以後可能不會再為火狐版的側邊翻譯增加新功能。我們強烈建議喜歡側邊翻譯的用戶嘗試使用 Chromium 系的瀏覽器，如 Chrome，新版 Edge，開源的 Chromium 等。

如果你真的需要網頁翻譯功能，可以考慮如下兩種方案：

1. 在可能的情況下，我們會提供擁有測試簽名的安裝包，可以直接下載安裝，步驟如下：

    1. 前往 [Releases 頁面](https://github.com/EdgeTranslate/EdgeTranslate/releases/latest)

    2. 如果頁面最下方的下載選項中有文件名格式為`EdgeTranslate_vx.x.x.x_firefox.xpi`的文件（注意是 **4** 位版本號），那麽直接下載安裝該文件即可

    3. 如果沒有這樣的文件，説明我們無法提供安裝包了，此時請遵循下一種方案

2. 手動下載安裝未簽名的完整版側邊翻譯，具體步驟如下：

    1. 下載安裝 Firefox 的[延長支持版（ESR）](//www.mozilla.org/firefox/organizations/)、[開發者版](//www.mozilla.org/firefox/developer/)或[Nightly 版](//nightly.mozilla.org/)，三者任選其一，推薦程度：延長支持版 > 開發者版 > Nightly 版

    2. 前往 [Releases 页面](https://github.com/EdgeTranslate/EdgeTranslate/releases/latest)下載最新版的，帶有網頁翻譯功能的火狐版側邊翻譯安裝包，包名格式：`EdgeTranslate_vx.x.x_firefox.zip`

    3. 打開 Firefox 配置編輯器（在地址欄輸入`about:config` 然後按回車），搜索 `xpinstall.signatures.required` 并將值設置爲 `false`（點最後的雙向箭頭切換）

    4. 打開 Firefox 附加組件管理器 （在地址欄輸入`about:addons`然後按回車），點擊右上角設置菜單，選擇“從文件安裝附加組件”，選擇下載的 zip 文件即可

感謝大家一直以來的支持！
