## PLEASE REFRESH THE PAGE NEED TRANSLATING AFTER INSTALLATION OR UPDATE! ! !

## PLEASE READ THE [INSTRUCTIONS](./Instructions.md) BEFORE USING! ! !

## PLEASE READ THE [Q & A](./Precautions.md) WHEN YOU ENCOUNTERED PROBLEMS! ! !

## Introduction

Welcome to Edge Translate!

Edge Translate is a simple and practical translation plugin that supports mainstream browsers such as [Chrome](https://chrome.google.com/webstore/detail/bocbaocobfecmglnmeaeppambideimao), [Firefox](https://addons.mozilla.org/en-US/firefox/addon/edge_translate), and [360 Secure Browser](https://ext.se.360.cn/webstore/detail/jkhojcaggkaojlhfddocjkkphfdkejeg) . The main purpose of our plugin is to assist users in reading foreign literature. To this end, we have followed the principle of the user's reading experience first, and made the following efforts:

-   **Google Translate API**

    We use the API provided by Google Translate to translate words and sentences, which guarantees the accuracy of translation results to a certain extent;

-   **PDF File Supported**

    We support the translation of wording in PDF files, which breaks the dyslexia of many users when reading PDF documents (due to the Firefox browser's bug, this feature is temporarily unavailable on Firefox browser);

-   **Reading Friendly Result Displaying**

    We chose the friendly side pop-up to show the translation results. The pop-up display bar will push the user reading content to avoid blocking the content from affecting the reading;

-   **Simple and Clear Look**

    We designed a simple and clear translation result display column, highlighting important content, and ensuring that the user's attention is focused on the content displayed rather than the insignificant things such as the display box;

-   **Fix The Result Frame**

    If the frequency of the your translation is relatively high, you can choose to fix the display bar to avoid frequent pop-ups affecting reading.

-   **Fully Customizable**

    We allow users to decide for themselves which content in the translation results they need to display. For example, if you just want to know the meaning of the word, you can choose to view only the common meaning of the word. If you also want to learn the specific usage of a word, we also provide the pronunciation, definition, detailed explanation, example sentence, etc. of the word in more detail. Content for you to view;

-   **Efficient Shortcuts**

    We have provided a wealth of shortcut keys to make the operation efficiency greatly improved. Now you only need to use the keyboard to complete the translation of selected words, expand the search panel, fix and unpin the translation results;

-   **Website Blacklist**

    We provide a useful blacklist feature that allows you to easily add a page you are browsing to a blacklist to disable wording translations and double-click translations on the page, or to remove the page being viewed from the blacklist to re-enable word translation and double click translation on this page.

-   **Whole Page Translating**

    We support web page translation and can translate the entire web page directly into the language you need. **Firefox users please read [this](./ToFirefoxUsers.md)**

The birth of Edge Translate is inseparable from the open source community. In the development process of Edge Translate, we used [Mozilla](https://github.com/mozilla)'s [pdf.js](https://github.com/mozilla/pdf.js) as a built-in PDF reader to support word translation in PDF files. We refered to [crimx](https://github.com/crimx)'s [saladict](https://github.com/crimx/ext-saladict) for solutions of some problems. We also used [gulp](https://github.com/gulpjs/gulp), [webpack](https://github.com/webpack/webpack) and other excellent open source tools to assist in development. And we would like to express our gratitude.

We are also grateful to users who have used Edge Translate since 0.2.0 and provided us with feedback. You have provided us with a lot of very good comments and suggestions. Without you, there may no be the current Edge Translate.

## Instructions

[Edge Translate instructions](./Instructions.md)

## Precautions

[Edge Translate precautions](./Precautions.md)

## Privacy Policy

[Edge Translate privacy policy](./PrivacyPolicy.md)

## Questions and Feedback

Edge Translate were developed by us ( [nickyc975](https://github.com/nickyc975) and [Mark Fenng](https://github.com/Mark-Fenng) ) in our spare time, and there are inevitably some problems. If you have any comments or suggestions, please feel free to give us feedback at the first time and help us to make it better.

-   Feedback link：[Edge Translate issue](https://github.com/EdgeTranslate/EdgeTranslate/issues/new/choose)

-   Email: [nickyc975](mailto:<EMAIL>), [Mark Fenng](mailto:<EMAIL>)

-   Chrome App Store: [Edge Translate](https://chrome.google.com/webstore/detail/bocbaocobfecmglnmeaeppambideimao/reviews)

-   Firefox Add-ons Store: [Edge Translate](https://addons.mozilla.org/en-US/firefox/addon/edge_translate/reviews)

In addition, developers are welcome to give us issues and pull requests. If you like this project, welcome to fork & star.

## Sponsor

It took us much time and energy to develop this project. If it truly helped you in some way, you could reward us with cans of Coke to support us to keep improving it: [PayPal](https://paypal.me/EdgeTranslate).

But, this is completely **voluntary**. Sponsoring won't bring any special treatment and you can still use Edge Translate freely without sponsoring. Do it according to your capability!
