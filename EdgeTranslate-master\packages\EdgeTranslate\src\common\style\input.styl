color-primary = #4a8cf7;

scale = 1.3;
.radio
    position: relative;
    width: (16px * scale);
    height: (16px * scale);
    background-clip: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: -.15px .6px 0 0;
    vertical-align: text-bottom;
    border-radius: 50%;
    background-color: #fff;
    border: (1px * scale) solid #d7d7d7;
    &:before
        content: '';
        display: block;
        height: 0;
        width: 0;
        -webkit-transition: width .25s, height .25s;
        transition: width .25s, height .25s
    &:disabled
        opacity: .65
    &:checked
        border: (1px * scale) solid color-primary;
        background-color: #fff;
        &:before
            height: (8px * scale);
            width: (8px * scale);
            border-radius: 50%;
            margin: (3px * scale) 0 0 (3px * scale);
            background-color: color-primary
    &:focus
        outline: none;
        box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.075), 0 0 2px #38a7ff;


.checkbox
    position: relative;
    width: (16px * scale);
    height: (16px * scale);
    background-clip: border-box;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: -.15px .6px 0 0;
    vertical-align: text-bottom;
    border-radius: 3px;
    -webkit-transition: background-color .25s;
    transition: background-color .25s;
    background-color: #fff;
    border: 1px solid #d7d7d7;
    &:checked
        background-color: color-primary;
        border-color: color-primary;
        &:after
            content: '';
            display: block;
            height: (4px * scale);
            width: (7px * scale);
            border: 0 solid #fff;
            border-width: 0 0 2px 2px;
            -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
            position: absolute;
            top: (3px * scale);
            left: (3px * scale);
    &:disabled
        opacity: .65
    &:focus
        outline: none;
        box-shadow: inset 0 1px 1px rgba(255, 255, 255, 0.075), 0 0 2px #38a7ff

.switch
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    position: relative;
    width: 41px;
    height: 24px;
    border: 1px solid #dfdfdf;
    outline: 0;
    border-radius: 16px;
    box-sizing: border-box;
    background: #dfdfdf;
    &:before
    &:after
        content: " ";
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 15px;
        -webkit-transition: -webkit-transform 0.3s;
        transition: -webkit-transform 0.3s;
        transition: transform 0.3s;
        transition: transform 0.3s, -webkit-transform 0.3s;
    &:before
        width: 39px;
        height: 22px;
        background-color: lightgray;
    &:after
        width: 22px;
        height: 22px;
        background-color: #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.4);
    &:checked
        border-color: color-primary;
        background-color: color-primary;
        &:before
            -webkit-transform: scale(0);
            transform: scale(0);
        &:after
            -webkit-transform: translateX(17px);
            transform: translateX(17px);
        &:focus
            outline: 0;
    &:disabled
        opacity: .65;