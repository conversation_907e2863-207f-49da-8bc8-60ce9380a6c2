// API callback
callback({
  "sourceLanguages": [
    {
      "language": "auto",
      "name": "Hauteman hizkuntza"
    },
    {
      "language": "af",
      "name": "afrikaansa"
    },
    {
      "language": "ay",
      "name": "aimara"
    },
    {
      "language": "sq",
      "name": "albaniera"
    },
    {
      "language": "de",
      "name": "alemana"
    },
    {
      "language": "am",
      "name": "amharera"
    },
    {
      "language": "ar",
      "name": "arabiera"
    },
    {
      "language": "hy",
      "name": "armeniera"
    },
    {
      "language": "as",
      "name": "assamera"
    },
    {
      "language": "az",
      "name": "azerbaijanera"
    },
    {
      "language": "bm",
      "name": "bambarera"
    },
    {
      "language": "bn",
      "name": "bengalera"
    },
    {
      "language": "bho",
      "name": "bhojpurera"
    },
    {
      "language": "be",
      "name": "bielorrusiera"
    },
    {
      "language": "my",
      "name": "birmaniera"
    },
    {
      "language": "bs",
      "name": "bosniera"
    },
    {
      "language": "bg",
      "name": "bulgariera"
    },
    {
      "language": "ceb",
      "name": "cebuanoera"
    },
    {
      "language": "ny",
      "name": "chewera"
    },
    {
      "language": "da",
      "name": "daniera"
    },
    {
      "language": "dv",
      "name": "dhivehia"
    },
    {
      "language": "doi",
      "name": "dogria"
    },
    {
      "language": "ro",
      "name": "errumaniera"
    },
    {
      "language": "ru",
      "name": "errusiera"
    },
    {
      "language": "gd",
      "name": "Eskoziako gaelikoa"
    },
    {
      "language": "sk",
      "name": "eslovakiera"
    },
    {
      "language": "sl",
      "name": "esloveniera"
    },
    {
      "language": "eo",
      "name": "esperantoa"
    },
    {
      "language": "et",
      "name": "estoniera"
    },
    {
      "language": "eu",
      "name": "euskara"
    },
    {
      "language": "ee",
      "name": "eweera"
    },
    {
      "language": "tl",
      "name": "filipinera"
    },
    {
      "language": "fi",
      "name": "finlandiera"
    },
    {
      "language": "fr",
      "name": "frantsesa"
    },
    {
      "language": "fy",
      "name": "frisiera"
    },
    {
      "language": "cy",
      "name": "galesa"
    },
    {
      "language": "gl",
      "name": "galiziera"
    },
    {
      "language": "es",
      "name": "gaztelania"
    },
    {
      "language": "ka",
      "name": "georgiera"
    },
    {
      "language": "el",
      "name": "greziera"
    },
    {
      "language": "gn",
      "name": "guaraniera"
    },
    {
      "language": "gu",
      "name": "gujaratera"
    },
    {
      "language": "ha",
      "name": "hausa"
    },
    {
      "language": "haw",
      "name": "hawaiiera"
    },
    {
      "language": "iw",
      "name": "hebreera"
    },
    {
      "language": "hi",
      "name": "hindia"
    },
    {
      "language": "hmn",
      "name": "hmonga"
    },
    {
      "language": "hu",
      "name": "hungariera"
    },
    {
      "language": "ig",
      "name": "igboera"
    },
    {
      "language": "ilo",
      "name": "ilocanoera"
    },
    {
      "language": "id",
      "name": "indonesiera"
    },
    {
      "language": "en",
      "name": "ingelesa"
    },
    {
      "language": "ga",
      "name": "irlandera"
    },
    {
      "language": "is",
      "name": "islandiera"
    },
    {
      "language": "it",
      "name": "italiera"
    },
    {
      "language": "ja",
      "name": "japoniera"
    },
    {
      "language": "jw",
      "name": "javera"
    },
    {
      "language": "yo",
      "name": "jorubera"
    },
    {
      "language": "kn",
      "name": "kannada"
    },
    {
      "language": "ca",
      "name": "katalana"
    },
    {
      "language": "kk",
      "name": "kazakhera"
    },
    {
      "language": "km",
      "name": "khmerera"
    },
    {
      "language": "rw",
      "name": "kinyaruanda"
    },
    {
      "language": "ky",
      "name": "kirgizera"
    },
    {
      "language": "qu",
      "name": "kitxua"
    },
    {
      "language": "gom",
      "name": "konkanera"
    },
    {
      "language": "ko",
      "name": "koreera"
    },
    {
      "language": "co",
      "name": "korsikera"
    },
    {
      "language": "ht",
      "name": "kreolera (Haiti)"
    },
    {
      "language": "kri",
      "name": "krioera"
    },
    {
      "language": "hr",
      "name": "kroaziera"
    },
    {
      "language": "ku",
      "name": "kurduera (Kurmanji)"
    },
    {
      "language": "ckb",
      "name": "kurduera (sorania)"
    },
    {
      "language": "lo",
      "name": "laosera"
    },
    {
      "language": "la",
      "name": "latina"
    },
    {
      "language": "lv",
      "name": "letoniera"
    },
    {
      "language": "ln",
      "name": "lingala"
    },
    {
      "language": "lt",
      "name": "lituaniera"
    },
    {
      "language": "lg",
      "name": "luganda"
    },
    {
      "language": "lb",
      "name": "luxenburgera"
    },
    {
      "language": "mai",
      "name": "maithilia"
    },
    {
      "language": "ml",
      "name": "malabarera"
    },
    {
      "language": "ms",
      "name": "malaysiera"
    },
    {
      "language": "mg",
      "name": "malgaxea"
    },
    {
      "language": "mt",
      "name": "maltera"
    },
    {
      "language": "mi",
      "name": "maoriera"
    },
    {
      "language": "mr",
      "name": "marathera"
    },
    {
      "language": "mk",
      "name": "mazedoniera"
    },
    {
      "language": "mni-Mtei",
      "name": "meiteilon (manipurera)"
    },
    {
      "language": "lus",
      "name": "mizoa"
    },
    {
      "language": "mn",
      "name": "mongoliera"
    },
    {
      "language": "nl",
      "name": "nederlandera"
    },
    {
      "language": "ne",
      "name": "nepalera"
    },
    {
      "language": "no",
      "name": "norvegiera"
    },
    {
      "language": "or",
      "name": "odia"
    },
    {
      "language": "om",
      "name": "oromoera"
    },
    {
      "language": "ps",
      "name": "paxtunera"
    },
    {
      "language": "fa",
      "name": "persiera"
    },
    {
      "language": "pl",
      "name": "poloniera"
    },
    {
      "language": "pt",
      "name": "portugesa"
    },
    {
      "language": "pa",
      "name": "punjabera"
    },
    {
      "language": "sm",
      "name": "samoera"
    },
    {
      "language": "sa",
      "name": "sanskritoa"
    },
    {
      "language": "nso",
      "name": "sepediera"
    },
    {
      "language": "sr",
      "name": "serbiera"
    },
    {
      "language": "sn",
      "name": "shona"
    },
    {
      "language": "sd",
      "name": "sindhia"
    },
    {
      "language": "si",
      "name": "sinhala"
    },
    {
      "language": "so",
      "name": "somaliera"
    },
    {
      "language": "st",
      "name": "sothoera"
    },
    {
      "language": "sv",
      "name": "suediera"
    },
    {
      "language": "su",
      "name": "sundera"
    },
    {
      "language": "sw",
      "name": "swahilia"
    },
    {
      "language": "tg",
      "name": "tajikera"
    },
    {
      "language": "ta",
      "name": "tamilera"
    },
    {
      "language": "tt",
      "name": "tatarera"
    },
    {
      "language": "te",
      "name": "telugua"
    },
    {
      "language": "th",
      "name": "thailandiera"
    },
    {
      "language": "ti",
      "name": "tigrinyera"
    },
    {
      "language": "ts",
      "name": "tsongera"
    },
    {
      "language": "tr",
      "name": "turkiera"
    },
    {
      "language": "tk",
      "name": "turkmenera"
    },
    {
      "language": "ak",
      "name": "twia"
    },
    {
      "language": "cs",
      "name": "txekiera"
    },
    {
      "language": "zh-CN",
      "name": "txinera"
    },
    {
      "language": "ug",
      "name": "uigurrera"
    },
    {
      "language": "uk",
      "name": "ukrainera"
    },
    {
      "language": "ur",
      "name": "urdua"
    },
    {
      "language": "uz",
      "name": "uzbekera"
    },
    {
      "language": "vi",
      "name": "vietnamera"
    },
    {
      "language": "xh",
      "name": "xhosera"
    },
    {
      "language": "yi",
      "name": "yiddisha"
    },
    {
      "language": "zu",
      "name": "zuluera"
    }
  ],
  "targetLanguages": [
    {
      "language": "af",
      "name": "afrikaansa"
    },
    {
      "language": "ay",
      "name": "aimara"
    },
    {
      "language": "sq",
      "name": "albaniera"
    },
    {
      "language": "de",
      "name": "alemana"
    },
    {
      "language": "am",
      "name": "amharera"
    },
    {
      "language": "ar",
      "name": "arabiera"
    },
    {
      "language": "hy",
      "name": "armeniera"
    },
    {
      "language": "as",
      "name": "assamera"
    },
    {
      "language": "az",
      "name": "azerbaijanera"
    },
    {
      "language": "bm",
      "name": "bambarera"
    },
    {
      "language": "bn",
      "name": "bengalera"
    },
    {
      "language": "bho",
      "name": "bhojpurera"
    },
    {
      "language": "be",
      "name": "bielorrusiera"
    },
    {
      "language": "my",
      "name": "birmaniera"
    },
    {
      "language": "bs",
      "name": "bosniera"
    },
    {
      "language": "bg",
      "name": "bulgariera"
    },
    {
      "language": "ceb",
      "name": "cebuanoera"
    },
    {
      "language": "ny",
      "name": "chewera"
    },
    {
      "language": "da",
      "name": "daniera"
    },
    {
      "language": "dv",
      "name": "dhivehia"
    },
    {
      "language": "doi",
      "name": "dogria"
    },
    {
      "language": "ro",
      "name": "errumaniera"
    },
    {
      "language": "ru",
      "name": "errusiera"
    },
    {
      "language": "gd",
      "name": "Eskoziako gaelikoa"
    },
    {
      "language": "sk",
      "name": "eslovakiera"
    },
    {
      "language": "sl",
      "name": "esloveniera"
    },
    {
      "language": "eo",
      "name": "esperantoa"
    },
    {
      "language": "et",
      "name": "estoniera"
    },
    {
      "language": "eu",
      "name": "euskara"
    },
    {
      "language": "ee",
      "name": "eweera"
    },
    {
      "language": "tl",
      "name": "filipinera"
    },
    {
      "language": "fi",
      "name": "finlandiera"
    },
    {
      "language": "fr",
      "name": "frantsesa"
    },
    {
      "language": "fy",
      "name": "frisiera"
    },
    {
      "language": "cy",
      "name": "galesa"
    },
    {
      "language": "gl",
      "name": "galiziera"
    },
    {
      "language": "es",
      "name": "gaztelania"
    },
    {
      "language": "ka",
      "name": "georgiera"
    },
    {
      "language": "el",
      "name": "greziera"
    },
    {
      "language": "gn",
      "name": "guaraniera"
    },
    {
      "language": "gu",
      "name": "gujaratera"
    },
    {
      "language": "ha",
      "name": "hausa"
    },
    {
      "language": "haw",
      "name": "hawaiiera"
    },
    {
      "language": "iw",
      "name": "hebreera"
    },
    {
      "language": "hi",
      "name": "hindia"
    },
    {
      "language": "hmn",
      "name": "hmonga"
    },
    {
      "language": "hu",
      "name": "hungariera"
    },
    {
      "language": "ig",
      "name": "igboera"
    },
    {
      "language": "ilo",
      "name": "ilocanoera"
    },
    {
      "language": "id",
      "name": "indonesiera"
    },
    {
      "language": "en",
      "name": "ingelesa"
    },
    {
      "language": "ga",
      "name": "irlandera"
    },
    {
      "language": "is",
      "name": "islandiera"
    },
    {
      "language": "it",
      "name": "italiera"
    },
    {
      "language": "ja",
      "name": "japoniera"
    },
    {
      "language": "jw",
      "name": "javera"
    },
    {
      "language": "yo",
      "name": "jorubera"
    },
    {
      "language": "kn",
      "name": "kannada"
    },
    {
      "language": "ca",
      "name": "katalana"
    },
    {
      "language": "kk",
      "name": "kazakhera"
    },
    {
      "language": "km",
      "name": "khmerera"
    },
    {
      "language": "rw",
      "name": "kinyaruanda"
    },
    {
      "language": "ky",
      "name": "kirgizera"
    },
    {
      "language": "qu",
      "name": "kitxua"
    },
    {
      "language": "gom",
      "name": "konkanera"
    },
    {
      "language": "ko",
      "name": "koreera"
    },
    {
      "language": "co",
      "name": "korsikera"
    },
    {
      "language": "ht",
      "name": "kreolera (Haiti)"
    },
    {
      "language": "kri",
      "name": "krioera"
    },
    {
      "language": "hr",
      "name": "kroaziera"
    },
    {
      "language": "ku",
      "name": "kurduera (Kurmanji)"
    },
    {
      "language": "ckb",
      "name": "kurduera (sorania)"
    },
    {
      "language": "lo",
      "name": "laosera"
    },
    {
      "language": "la",
      "name": "latina"
    },
    {
      "language": "lv",
      "name": "letoniera"
    },
    {
      "language": "ln",
      "name": "lingala"
    },
    {
      "language": "lt",
      "name": "lituaniera"
    },
    {
      "language": "lg",
      "name": "luganda"
    },
    {
      "language": "lb",
      "name": "luxenburgera"
    },
    {
      "language": "mai",
      "name": "maithilia"
    },
    {
      "language": "ml",
      "name": "malabarera"
    },
    {
      "language": "ms",
      "name": "malaysiera"
    },
    {
      "language": "mg",
      "name": "malgaxea"
    },
    {
      "language": "mt",
      "name": "maltera"
    },
    {
      "language": "mi",
      "name": "maoriera"
    },
    {
      "language": "mr",
      "name": "marathera"
    },
    {
      "language": "mk",
      "name": "mazedoniera"
    },
    {
      "language": "mni-Mtei",
      "name": "meiteilon (manipurera)"
    },
    {
      "language": "lus",
      "name": "mizoa"
    },
    {
      "language": "mn",
      "name": "mongoliera"
    },
    {
      "language": "nl",
      "name": "nederlandera"
    },
    {
      "language": "ne",
      "name": "nepalera"
    },
    {
      "language": "no",
      "name": "norvegiera"
    },
    {
      "language": "or",
      "name": "odia"
    },
    {
      "language": "om",
      "name": "oromoera"
    },
    {
      "language": "ps",
      "name": "paxtunera"
    },
    {
      "language": "fa",
      "name": "persiera"
    },
    {
      "language": "pl",
      "name": "poloniera"
    },
    {
      "language": "pt",
      "name": "portugesa"
    },
    {
      "language": "pa",
      "name": "punjabera"
    },
    {
      "language": "sm",
      "name": "samoera"
    },
    {
      "language": "sa",
      "name": "sanskritoa"
    },
    {
      "language": "nso",
      "name": "sepediera"
    },
    {
      "language": "sr",
      "name": "serbiera"
    },
    {
      "language": "sn",
      "name": "shona"
    },
    {
      "language": "sd",
      "name": "sindhia"
    },
    {
      "language": "si",
      "name": "sinhala"
    },
    {
      "language": "so",
      "name": "somaliera"
    },
    {
      "language": "st",
      "name": "sothoera"
    },
    {
      "language": "sv",
      "name": "suediera"
    },
    {
      "language": "su",
      "name": "sundera"
    },
    {
      "language": "sw",
      "name": "swahilia"
    },
    {
      "language": "tg",
      "name": "tajikera"
    },
    {
      "language": "ta",
      "name": "tamilera"
    },
    {
      "language": "tt",
      "name": "tatarera"
    },
    {
      "language": "te",
      "name": "telugua"
    },
    {
      "language": "th",
      "name": "thailandiera"
    },
    {
      "language": "ti",
      "name": "tigrinyera"
    },
    {
      "language": "ts",
      "name": "tsongera"
    },
    {
      "language": "tr",
      "name": "turkiera"
    },
    {
      "language": "tk",
      "name": "turkmenera"
    },
    {
      "language": "ak",
      "name": "twia"
    },
    {
      "language": "cs",
      "name": "txekiera"
    },
    {
      "language": "zh-CN",
      "name": "txinera (sinplifikatua)"
    },
    {
      "language": "zh-TW",
      "name": "txinera (tradizionala)"
    },
    {
      "language": "ug",
      "name": "uigurrera"
    },
    {
      "language": "uk",
      "name": "ukrainera"
    },
    {
      "language": "ur",
      "name": "urdua"
    },
    {
      "language": "uz",
      "name": "uzbekera"
    },
    {
      "language": "vi",
      "name": "vietnamera"
    },
    {
      "language": "xh",
      "name": "xhosera"
    },
    {
      "language": "yi",
      "name": "yiddisha"
    },
    {
      "language": "zu",
      "name": "zuluera"
    }
  ]
}
);