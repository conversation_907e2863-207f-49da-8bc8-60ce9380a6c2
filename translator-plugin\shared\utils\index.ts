import { TranslationResult, StorageData, UserPreferences } from '../types';
import { DEFAULT_SETTINGS, STORAGE_KEYS, CACHE_CONFIG } from '../constants';

// 存储管理工具
export class StorageManager {
  static async get<T>(key: string): Promise<T | null> {
    try {
      const result = await chrome.storage.local.get(key);
      return result[key] || null;
    } catch (error) {
      console.error('Storage get error:', error);
      return null;
    }
  }

  static async set(key: string, value: any): Promise<boolean> {
    try {
      await chrome.storage.local.set({ [key]: value });
      return true;
    } catch (error) {
      console.error('Storage set error:', error);
      return false;
    }
  }

  static async remove(key: string): Promise<boolean> {
    try {
      await chrome.storage.local.remove(key);
      return true;
    } catch (error) {
      console.error('Storage remove error:', error);
      return false;
    }
  }

  static async clear(): Promise<boolean> {
    try {
      await chrome.storage.local.clear();
      return true;
    } catch (error) {
      console.error('Storage clear error:', error);
      return false;
    }
  }

  // 获取用户设置
  static async getUserSettings(): Promise<UserPreferences> {
    const settings = await this.get<UserPreferences>(STORAGE_KEYS.USER_SETTINGS);
    return { ...DEFAULT_SETTINGS, ...settings };
  }

  // 保存用户设置
  static async saveUserSettings(settings: Partial<UserPreferences>): Promise<boolean> {
    const currentSettings = await this.getUserSettings();
    const newSettings = { ...currentSettings, ...settings };
    return await this.set(STORAGE_KEYS.USER_SETTINGS, newSettings);
  }
}

// 翻译缓存管理
export class TranslationCache {
  private static generateKey(text: string, from: string, to: string): string {
    return `${from}-${to}-${text.toLowerCase().trim()}`;
  }

  static async get(text: string, from: string, to: string): Promise<TranslationResult | null> {
    const key = this.generateKey(text, from, to);
    const cache = await StorageManager.get<Record<string, any>>(STORAGE_KEYS.TRANSLATION_CACHE) || {};
    
    const cached = cache[key];
    if (!cached) return null;

    // 检查缓存是否过期
    const now = Date.now();
    if (now - cached.timestamp > CACHE_CONFIG.TRANSLATION_TTL) {
      await this.remove(text, from, to);
      return null;
    }

    return cached.result;
  }

  static async set(text: string, from: string, to: string, result: TranslationResult): Promise<boolean> {
    const key = this.generateKey(text, from, to);
    const cache = await StorageManager.get<Record<string, any>>(STORAGE_KEYS.TRANSLATION_CACHE) || {};
    
    cache[key] = {
      result,
      timestamp: Date.now()
    };

    // 限制缓存大小
    const keys = Object.keys(cache);
    if (keys.length > 100) {
      // 删除最旧的缓存项
      const sortedKeys = keys.sort((a, b) => cache[a].timestamp - cache[b].timestamp);
      for (let i = 0; i < 20; i++) {
        delete cache[sortedKeys[i]];
      }
    }

    return await StorageManager.set(STORAGE_KEYS.TRANSLATION_CACHE, cache);
  }

  static async remove(text: string, from: string, to: string): Promise<boolean> {
    const key = this.generateKey(text, from, to);
    const cache = await StorageManager.get<Record<string, any>>(STORAGE_KEYS.TRANSLATION_CACHE) || {};
    
    delete cache[key];
    return await StorageManager.set(STORAGE_KEYS.TRANSLATION_CACHE, cache);
  }

  static async clear(): Promise<boolean> {
    return await StorageManager.remove(STORAGE_KEYS.TRANSLATION_CACHE);
  }
}

// 文本处理工具
export class TextUtils {
  // 清理文本
  static cleanText(text: string): string {
    return text
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[\r\n]+/g, ' ');
  }

  // 检测是否为有效的翻译文本
  static isValidTranslationText(text: string): boolean {
    const cleaned = this.cleanText(text);
    return cleaned.length > 0 && cleaned.length <= 1000 && !/^\s*$/.test(cleaned);
  }

  // 检测文本语言（简单实现）
  static detectLanguage(text: string): string {
    const cleaned = this.cleanText(text);
    
    // 中文检测
    if (/[\u4e00-\u9fff]/.test(cleaned)) {
      return 'zh';
    }
    
    // 日文检测
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(cleaned)) {
      return 'ja';
    }
    
    // 韩文检测
    if (/[\uac00-\ud7af]/.test(cleaned)) {
      return 'ko';
    }
    
    // 阿拉伯文检测
    if (/[\u0600-\u06ff]/.test(cleaned)) {
      return 'ar';
    }
    
    // 俄文检测
    if (/[\u0400-\u04ff]/.test(cleaned)) {
      return 'ru';
    }
    
    // 默认英文
    return 'en';
  }

  // 截断文本
  static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }
}

// 错误处理工具
export class ErrorHandler {
  static getErrorMessage(error: any): string {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.error) return error.error;
    return '未知错误';
  }

  static isNetworkError(error: any): boolean {
    return error?.code === 'NETWORK_ERROR' || 
           error?.message?.includes('network') ||
           error?.message?.includes('fetch');
  }

  static isRateLimitError(error: any): boolean {
    return error?.code === 'RATE_LIMIT_EXCEEDED' ||
           error?.status === 429;
  }

  static isAuthError(error: any): boolean {
    return error?.code === 'UNAUTHORIZED' ||
           error?.status === 401;
  }
}

// 防抖工具
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 节流工具
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 重试工具
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      // 指数退避
      const waitTime = delay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw lastError;
}

// 格式化工具
export class FormatUtils {
  // 格式化日期
  static formatDate(date: string | Date): string {
    const d = new Date(date);
    return d.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // 格式化数字
  static formatNumber(num: number): string {
    return new Intl.NumberFormat('zh-CN').format(num);
  }

  // 格式化文件大小
  static formatFileSize(bytes: number): string {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}
