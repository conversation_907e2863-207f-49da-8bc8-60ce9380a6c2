### 注意

* 关于火狐浏览器安装zip格式的扩展包请参考这里：#60 ！

* 为了进一步改善翻译体验，我们建立了侧边翻译用户交流群，欢迎大家加入：[侧边翻译用户交流QQ群](https://jq.qq.com/?_wv=1027&k=gT5EYfFB)

### 新增

* 新增 __多翻译源支持__，目前已支持 __谷歌翻译__，__必应翻译__，__百度翻译__，__腾讯翻译君__ 四个翻译源，可以在翻译结果展示框的左上角选择，后续将加入更多得翻译源，敬请期待！

* 新增 __混合翻译源__ 功能，可以综合展示多个翻译源的结果，例如，可以使用谷歌翻译来展示音标并朗读文本，同时使用百度翻译来获取单词例句；

* 新增 __悬浮框__ 模式，现在可以通过拖动翻译框来使得翻译结果以贴边或悬浮的方式展示：

  + 当翻译框在左侧或右侧贴边展示时，拖动翻译框到页面中部可以使翻译框悬浮在页面上，当你再次选词翻译时，翻译框会自动在选择的文本附近展开；
  
  + 当翻译框在页面中悬浮展示时，将翻译框向左侧或右侧拖动直到鼠标到达屏幕左侧或右侧边界，可以使翻译框恢复到左侧或右侧贴边展示；
  
  + 悬浮框可以自由调整大小和位置，以适应不同的需求；

### 优化

* 重构了项目整体结构，提高了项目可维护性；

* 使用Promise取代了部分回调，提高代码可读性；

* 优化了不同浏览器的代码生成方式，在最终编译结果中去除了冗余代码；

### 修复

* 修复了大量潜在问题；

### 关于打赏

开发这个项目花费了我们许多的时间和精力，如果你真的觉得这个项目对你有帮助，不妨请我们喝罐可乐，支持我们继续做下去！

当然，这 __纯属自愿__，打赏并不能获得什么优待，不打赏也不会有任何影响，请量力而为！

| 微信 | 支付宝 |
| :-: | :-: |
| <img src="https://user-images.githubusercontent.com/25877145/80864662-b6617c00-8cb6-11ea-915a-582ca046118c.png" height=200 alt="微信支付"/> | <img src="https://user-images.githubusercontent.com/25877145/80864685-ced19680-8cb6-11ea-94e5-f5ca8e4389b9.jpg" height=200 alt="支付宝支付"/> |

