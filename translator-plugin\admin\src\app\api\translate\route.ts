import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';

// POST /api/translate - 翻译文本
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // 验证用户认证
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const { text, source_language, target_language, model_id } = body;

    // 验证必填字段
    if (!text || !target_language) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // 文本长度限制
    if (text.length > 1000) {
      return NextResponse.json({ error: 'Text too long (max 1000 characters)' }, { status: 400 });
    }

    const startTime = Date.now();

    try {
      // 获取模型配置
      let model;
      if (model_id) {
        const { data: modelData, error: modelError } = await supabase
          .from('ai_models')
          .select('*')
          .eq('id', model_id)
          .eq('is_enabled', true)
          .single();

        if (modelError || !modelData) {
          return NextResponse.json({ error: 'Model not found or disabled' }, { status: 404 });
        }
        model = modelData;
      } else {
        // 使用默认的免费模型
        const { data: modelData, error: modelError } = await supabase
          .from('ai_models')
          .select('*')
          .eq('is_free', true)
          .eq('is_enabled', true)
          .order('priority', { ascending: true })
          .limit(1)
          .single();

        if (modelError || !modelData) {
          return NextResponse.json({ error: 'No available translation model' }, { status: 503 });
        }
        model = modelData;
      }

      // 检查用户权限（如果不是免费模型）
      if (!model.is_free) {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('subscription_status')
          .eq('id', user.id)
          .single();

        if (userError || userData?.subscription_status !== 'premium') {
          return NextResponse.json({ error: 'Premium subscription required' }, { status: 403 });
        }
      }

      // 调用翻译服务
      const translationResult = await callTranslationAPI(model, text, source_language, target_language);
      
      const responseTime = Date.now() - startTime;

      // 记录用量
      await supabase.from('usage_logs').insert({
        user_id: user.id,
        model_id: model.id,
        request_type: 'translate',
        characters_count: text.length,
        source_language: source_language || 'auto',
        target_language,
        success: true,
        response_time_ms: responseTime
      });

      return NextResponse.json({
        success: true,
        data: {
          ...translationResult,
          model_used: model.name,
          response_time_ms: responseTime
        }
      });

    } catch (translationError: any) {
      const responseTime = Date.now() - startTime;
      
      // 记录失败的用量
      await supabase.from('usage_logs').insert({
        user_id: user.id,
        model_id: model_id || null,
        request_type: 'translate',
        characters_count: text.length,
        source_language: source_language || 'auto',
        target_language,
        success: false,
        error_message: translationError.message,
        response_time_ms: responseTime
      });

      console.error('Translation error:', translationError);
      return NextResponse.json({ 
        error: 'Translation failed', 
        details: translationError.message 
      }, { status: 500 });
    }

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// 调用翻译API的函数
async function callTranslationAPI(model: any, text: string, from: string = 'auto', to: string) {
  const { provider, endpoint, api_key_encrypted } = model;
  
  // 解密API密钥
  const api_key = api_key_encrypted ? Buffer.from(api_key_encrypted, 'base64').toString() : '';

  switch (provider) {
    case 'google':
      return await callGoogleTranslate(text, from, to);
    
    case 'openai':
      return await callOpenAITranslate(endpoint, api_key, text, from, to);
    
    case 'kimi':
      return await callKimiTranslate(endpoint, api_key, text, from, to);
    
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}

// Google翻译（免费接口）
async function callGoogleTranslate(text: string, from: string, to: string) {
  const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=${from}&tl=${to}&dt=t&q=${encodeURIComponent(text)}`;
  
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Google Translate API error: ${response.status}`);
  }
  
  const data = await response.json();
  const translatedText = data[0]?.map((item: any) => item[0]).join('') || '';
  
  return {
    translatedText,
    sourceLanguage: data[2] || from,
    targetLanguage: to
  };
}

// OpenAI翻译
async function callOpenAITranslate(endpoint: string, apiKey: string, text: string, from: string, to: string) {
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: `You are a professional translator. Translate the given text from ${from} to ${to}. Only return the translated text, no explanations.`
        },
        {
          role: 'user',
          content: text
        }
      ],
      temperature: 0.3
    })
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  const data = await response.json();
  const translatedText = data.choices[0]?.message?.content || '';

  return {
    translatedText,
    sourceLanguage: from,
    targetLanguage: to
  };
}

// Kimi翻译
async function callKimiTranslate(endpoint: string, apiKey: string, text: string, from: string, to: string) {
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: 'moonshot-v1-8k',
      messages: [
        {
          role: 'system',
          content: `You are a professional translator. Translate the given text from ${from} to ${to}. Only return the translated text, no explanations.`
        },
        {
          role: 'user',
          content: text
        }
      ],
      temperature: 0.3
    })
  });

  if (!response.ok) {
    throw new Error(`Kimi API error: ${response.status}`);
  }

  const data = await response.json();
  const translatedText = data.choices[0]?.message?.content || '';

  return {
    translatedText,
    sourceLanguage: from,
    targetLanguage: to
  };
}
