@import '../display/library/notifier/notifier.styl';

image-size = 32px;

/*
 * 划词翻译按钮样式
 */
#edge-translate-button {
    display: inline-block !important;
    width: image-size !important;
    min-width: image-size !important;
    max-width: image-size !important;
    height: image-size !important;
    min-height: image-size !important;
    max-height: image-size !important;
    padding: 0 !important;
    margin: 0 !important;
    position: fixed !important;
    box-shadow: 0px 0px 2px 2px #ddd !important;
    cursor: pointer !important;
    border-radius: 50% !important;
    z-index: 2147483647 !important;
    box-sizing: content-box !important;
    overflow: hidden !important;
    border: none !important;
    transition: box-shadow 200ms ease-in-out, transform 300ms ease !important; // set transtition effect when hover occurs
    animation: fade-in 300ms ease-in-out !important; // set fade-in animation on selection icon

    &:hover {
        box-shadow: 0px 0px 8px 3px #bbb !important;
        transform: rotate(180deg) !important;
    }

    @keyframes fade-in {
        0% {
            opacity: 0;
        }

        100% {
            opacity: 1;
        }
    }
}