### Attention

* For the installation of the expansion pack in ZIP format for Firefox browser, please refer to [here](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/en/ToFirefoxUsers.md)!

* In order to further improve the translation experience, we have established an Edge Translate user communication group. Welcome to join: [Edge Translate](https://t.me/EdgeTranslate)

### New

* Allow editing of selected text in the translation display box, making it easy to correct and re-translate text if it was inadvertently omitted or over-selected when selecting.

* Show loading animations before loading audio data after clicking the pronunciation button to enhance the interactive experience.

* Add support for the Uighur translation.

* More practical error alerts, which can provide more information about errors and help identify problems.

* Update the built-in PDF viewer with a more attractive interface.

### Fixes

* Fix a number of style issues in the translation display box.

* Escape HTML characters (#127).

* Fix the problem of images being replaced by Edge Translate on some pages.

* Fix the problem of context menu items randomly disappearing after disabling and then re-enabling the extension.

* Fix problems caused by different language labels on different browsers.

* Fix the problem of mismatched language labelling that prevented Google Web Translate from working.

* Fix conflicts with the Octotree(#128).

* Fix the issue of invalid minimum width limits for translation box.

* Fix the problem of network errors reported by Bing Translate in some cases while the network is normal.

### Sponsor

It took us much time and energy to develop this project. If it truly helped you in some way, you could reward us with cans of Coke to support us to keep improving it: [PayPal](https://paypal.me/EdgeTranslate).

But, this is completely __voluntary__. Sponsoring won't bring any special treatment and you can still use Edge Translate freely without sponsoring. Do it according to your capability!