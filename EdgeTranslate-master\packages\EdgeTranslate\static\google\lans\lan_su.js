// API callback
callback({
  "sourceLanguages": [
    {
      "language": "auto",
      "name": "Détéksi basa"
    },
    {
      "language": "af",
      "name": "Basa Afrika"
    },
    {
      "language": "sq",
      "name": "Basa Albania"
    },
    {
      "language": "am",
      "name": "Basa Amharic"
    },
    {
      "language": "ar",
      "name": "Basa Arab"
    },
    {
      "language": "hy",
      "name": "Basa Arménia"
    },
    {
      "language": "as",
      "name": "Basa Assam"
    },
    {
      "language": "ay",
      "name": "Basa Aymara"
    },
    {
      "language": "az",
      "name": "Basa Azerbaijan"
    },
    {
      "language": "bm",
      "name": "Basa Bambara"
    },
    {
      "language": "eu",
      "name": "Basa Basqi"
    },
    {
      "language": "be",
      "name": "Basa Bélarusia"
    },
    {
      "language": "bn",
      "name": "Basa Béngali"
    },
    {
      "language": "bho",
      "name": "<PERSON>sa Bhojpuri"
    },
    {
      "language": "bs",
      "name": "Basa Bosnia"
    },
    {
      "language": "bg",
      "name": "Basa Bulgaria"
    },
    {
      "language": "ca",
      "name": "Basa Catalan"
    },
    {
      "language": "ceb",
      "name": "Basa Cébuano"
    },
    {
      "language": "cs",
      "name": "Basa Céko"
    },
    {
      "language": "ny",
      "name": "Basa Chichéwa"
    },
    {
      "language": "da",
      "name": "Basa Dénmark"
    },
    {
      "language": "dv",
      "name": "Basa Dhivehi"
    },
    {
      "language": "doi",
      "name": "Basa Dogri"
    },
    {
      "language": "eo",
      "name": "Basa Esperanto"
    },
    {
      "language": "et",
      "name": "Basa Éstonia"
    },
    {
      "language": "ee",
      "name": "Basa Ewe"
    },
    {
      "language": "tl",
      "name": "Basa Filipina"
    },
    {
      "language": "fi",
      "name": "Basa Finlandia"
    },
    {
      "language": "fy",
      "name": "Basa Frisia"
    },
    {
      "language": "gl",
      "name": "Basa Galisia"
    },
    {
      "language": "ka",
      "name": "Basa Georgia"
    },
    {
      "language": "gn",
      "name": "Basa Guarani"
    },
    {
      "language": "gu",
      "name": "Basa Gujarat"
    },
    {
      "language": "ht",
      "name": "Basa Haiti Kréolé"
    },
    {
      "language": "ha",
      "name": "Basa Hausa"
    },
    {
      "language": "haw",
      "name": "Basa Hawai"
    },
    {
      "language": "iw",
      "name": "Basa Hébru"
    },
    {
      "language": "hi",
      "name": "Basa Hindi"
    },
    {
      "language": "hmn",
      "name": "Basa Hmong"
    },
    {
      "language": "hu",
      "name": "Basa Hungaria"
    },
    {
      "language": "ig",
      "name": "Basa Igbo"
    },
    {
      "language": "ilo",
      "name": "Basa Ilocano"
    },
    {
      "language": "id",
      "name": "Basa Indonésia"
    },
    {
      "language": "en",
      "name": "Basa Inggris"
    },
    {
      "language": "ga",
      "name": "Basa Irlandia"
    },
    {
      "language": "is",
      "name": "Basa Islandia"
    },
    {
      "language": "it",
      "name": "Basa Italia"
    },
    {
      "language": "jw",
      "name": "Basa Jawa"
    },
    {
      "language": "ja",
      "name": "Basa Jepang"
    },
    {
      "language": "de",
      "name": "Basa Jérman"
    },
    {
      "language": "kn",
      "name": "Basa Kanada"
    },
    {
      "language": "kk",
      "name": "Basa Kazakh"
    },
    {
      "language": "km",
      "name": "Basa Khmér"
    },
    {
      "language": "rw",
      "name": "Basa Kinyarwanda"
    },
    {
      "language": "ky",
      "name": "Basa Kirgiztan"
    },
    {
      "language": "gom",
      "name": "Basa Konkani"
    },
    {
      "language": "ko",
      "name": "Basa Koréa"
    },
    {
      "language": "co",
      "name": "Basa Korsika"
    },
    {
      "language": "kri",
      "name": "Basa Krio"
    },
    {
      "language": "hr",
      "name": "Basa Kroasia"
    },
    {
      "language": "ku",
      "name": "Basa Kurdi (Kurmanji)"
    },
    {
      "language": "ckb",
      "name": "Basa Kurdi (Sorani)"
    },
    {
      "language": "lo",
      "name": "Basa Laoth"
    },
    {
      "language": "la",
      "name": "Basa Latin"
    },
    {
      "language": "lv",
      "name": "Basa Latvia"
    },
    {
      "language": "ln",
      "name": "Basa Lingala"
    },
    {
      "language": "lt",
      "name": "Basa Lituania"
    },
    {
      "language": "lg",
      "name": "Basa Luganda"
    },
    {
      "language": "lb",
      "name": "Basa Luxembourg"
    },
    {
      "language": "mai",
      "name": "Basa Maithili"
    },
    {
      "language": "mg",
      "name": "Basa Malagasi"
    },
    {
      "language": "ml",
      "name": "Basa Malayalam"
    },
    {
      "language": "mt",
      "name": "Basa Maltis"
    },
    {
      "language": "mi",
      "name": "Basa Maori"
    },
    {
      "language": "mr",
      "name": "Basa Marathi"
    },
    {
      "language": "mk",
      "name": "Basa Masédonia"
    },
    {
      "language": "mni-Mtei",
      "name": "Basa Meiteilon (Manipuri)"
    },
    {
      "language": "ms",
      "name": "Basa Melayu"
    },
    {
      "language": "lus",
      "name": "Basa Mizo"
    },
    {
      "language": "mn",
      "name": "Basa Mongolia"
    },
    {
      "language": "my",
      "name": "Basa Myanmar (Burma)"
    },
    {
      "language": "ne",
      "name": "Basa Népal"
    },
    {
      "language": "no",
      "name": "Basa Norwégia"
    },
    {
      "language": "or",
      "name": "Basa Odia (Oriya)"
    },
    {
      "language": "om",
      "name": "Basa Oromo"
    },
    {
      "language": "ps",
      "name": "Basa Pashto"
    },
    {
      "language": "fa",
      "name": "Basa Pérsia"
    },
    {
      "language": "pl",
      "name": "Basa Polandia"
    },
    {
      "language": "pt",
      "name": "Basa Portugis"
    },
    {
      "language": "fr",
      "name": "Basa Prancis"
    },
    {
      "language": "pa",
      "name": "Basa Punjab"
    },
    {
      "language": "qu",
      "name": "Basa Quechua"
    },
    {
      "language": "ro",
      "name": "Basa Rumania"
    },
    {
      "language": "ru",
      "name": "Basa Rusia"
    },
    {
      "language": "sm",
      "name": "Basa Samoa"
    },
    {
      "language": "sa",
      "name": "Basa Sansakerta"
    },
    {
      "language": "nso",
      "name": "Basa Sepedi"
    },
    {
      "language": "sr",
      "name": "Basa Serbia"
    },
    {
      "language": "st",
      "name": "Basa Sésotho"
    },
    {
      "language": "sn",
      "name": "Basa Shona"
    },
    {
      "language": "sd",
      "name": "Basa Sindhi"
    },
    {
      "language": "si",
      "name": "Basa Sinhala"
    },
    {
      "language": "gd",
      "name": "Basa Skot Gaélik"
    },
    {
      "language": "sk",
      "name": "Basa Slovakia"
    },
    {
      "language": "sl",
      "name": "Basa Slovénia"
    },
    {
      "language": "so",
      "name": "Basa Somali"
    },
    {
      "language": "es",
      "name": "Basa Spanyol"
    },
    {
      "language": "su",
      "name": "Basa Sunda"
    },
    {
      "language": "sw",
      "name": "Basa Swahili"
    },
    {
      "language": "sv",
      "name": "Basa Swédia"
    },
    {
      "language": "tg",
      "name": "Basa Tajik"
    },
    {
      "language": "ta",
      "name": "Basa Tamil"
    },
    {
      "language": "tt",
      "name": "Basa Tatar"
    },
    {
      "language": "te",
      "name": "Basa Telugu"
    },
    {
      "language": "th",
      "name": "Basa Thailan"
    },
    {
      "language": "ti",
      "name": "Basa Tigrinya"
    },
    {
      "language": "ts",
      "name": "Basa Tsonga"
    },
    {
      "language": "tr",
      "name": "Basa Turki"
    },
    {
      "language": "tk",
      "name": "Basa Turkmenistan"
    },
    {
      "language": "ak",
      "name": "Basa Twi"
    },
    {
      "language": "uk",
      "name": "Basa Ukraina"
    },
    {
      "language": "ur",
      "name": "Basa Urdu"
    },
    {
      "language": "ug",
      "name": "Basa Uyghur"
    },
    {
      "language": "uz",
      "name": "Basa Uzbékistan"
    },
    {
      "language": "vi",
      "name": "Basa Viétnam"
    },
    {
      "language": "nl",
      "name": "Basa Walanda"
    },
    {
      "language": "cy",
      "name": "Basa Wélsh"
    },
    {
      "language": "xh",
      "name": "Basa Xhosa"
    },
    {
      "language": "yi",
      "name": "Basa Yiddis"
    },
    {
      "language": "yo",
      "name": "Basa Yoruba"
    },
    {
      "language": "el",
      "name": "Basa Yunani"
    },
    {
      "language": "zu",
      "name": "Basa Zulu"
    },
    {
      "language": "zh-CN",
      "name": "Cina"
    }
  ],
  "targetLanguages": [
    {
      "language": "af",
      "name": "Basa Afrika"
    },
    {
      "language": "sq",
      "name": "Basa Albania"
    },
    {
      "language": "am",
      "name": "Basa Amharic"
    },
    {
      "language": "ar",
      "name": "Basa Arab"
    },
    {
      "language": "hy",
      "name": "Basa Arménia"
    },
    {
      "language": "as",
      "name": "Basa Assam"
    },
    {
      "language": "ay",
      "name": "Basa Aymara"
    },
    {
      "language": "az",
      "name": "Basa Azerbaijan"
    },
    {
      "language": "bm",
      "name": "Basa Bambara"
    },
    {
      "language": "eu",
      "name": "Basa Basqi"
    },
    {
      "language": "be",
      "name": "Basa Bélarusia"
    },
    {
      "language": "bn",
      "name": "Basa Béngali"
    },
    {
      "language": "bho",
      "name": "Basa Bhojpuri"
    },
    {
      "language": "bs",
      "name": "Basa Bosnia"
    },
    {
      "language": "bg",
      "name": "Basa Bulgaria"
    },
    {
      "language": "ca",
      "name": "Basa Catalan"
    },
    {
      "language": "ceb",
      "name": "Basa Cébuano"
    },
    {
      "language": "cs",
      "name": "Basa Céko"
    },
    {
      "language": "ny",
      "name": "Basa Chichéwa"
    },
    {
      "language": "da",
      "name": "Basa Dénmark"
    },
    {
      "language": "dv",
      "name": "Basa Dhivehi"
    },
    {
      "language": "doi",
      "name": "Basa Dogri"
    },
    {
      "language": "eo",
      "name": "Basa Esperanto"
    },
    {
      "language": "et",
      "name": "Basa Éstonia"
    },
    {
      "language": "ee",
      "name": "Basa Ewe"
    },
    {
      "language": "tl",
      "name": "Basa Filipina"
    },
    {
      "language": "fi",
      "name": "Basa Finlandia"
    },
    {
      "language": "fy",
      "name": "Basa Frisia"
    },
    {
      "language": "gl",
      "name": "Basa Galisia"
    },
    {
      "language": "ka",
      "name": "Basa Georgia"
    },
    {
      "language": "gn",
      "name": "Basa Guarani"
    },
    {
      "language": "gu",
      "name": "Basa Gujarat"
    },
    {
      "language": "ht",
      "name": "Basa Haiti Kréolé"
    },
    {
      "language": "ha",
      "name": "Basa Hausa"
    },
    {
      "language": "haw",
      "name": "Basa Hawai"
    },
    {
      "language": "iw",
      "name": "Basa Hébru"
    },
    {
      "language": "hi",
      "name": "Basa Hindi"
    },
    {
      "language": "hmn",
      "name": "Basa Hmong"
    },
    {
      "language": "hu",
      "name": "Basa Hungaria"
    },
    {
      "language": "ig",
      "name": "Basa Igbo"
    },
    {
      "language": "ilo",
      "name": "Basa Ilocano"
    },
    {
      "language": "id",
      "name": "Basa Indonésia"
    },
    {
      "language": "en",
      "name": "Basa Inggris"
    },
    {
      "language": "ga",
      "name": "Basa Irlandia"
    },
    {
      "language": "is",
      "name": "Basa Islandia"
    },
    {
      "language": "it",
      "name": "Basa Italia"
    },
    {
      "language": "jw",
      "name": "Basa Jawa"
    },
    {
      "language": "ja",
      "name": "Basa Jepang"
    },
    {
      "language": "de",
      "name": "Basa Jérman"
    },
    {
      "language": "kn",
      "name": "Basa Kanada"
    },
    {
      "language": "kk",
      "name": "Basa Kazakh"
    },
    {
      "language": "km",
      "name": "Basa Khmér"
    },
    {
      "language": "rw",
      "name": "Basa Kinyarwanda"
    },
    {
      "language": "ky",
      "name": "Basa Kirgiztan"
    },
    {
      "language": "gom",
      "name": "Basa Konkani"
    },
    {
      "language": "ko",
      "name": "Basa Koréa"
    },
    {
      "language": "co",
      "name": "Basa Korsika"
    },
    {
      "language": "kri",
      "name": "Basa Krio"
    },
    {
      "language": "hr",
      "name": "Basa Kroasia"
    },
    {
      "language": "ku",
      "name": "Basa Kurdi (Kurmanji)"
    },
    {
      "language": "ckb",
      "name": "Basa Kurdi (Sorani)"
    },
    {
      "language": "lo",
      "name": "Basa Laoth"
    },
    {
      "language": "la",
      "name": "Basa Latin"
    },
    {
      "language": "lv",
      "name": "Basa Latvia"
    },
    {
      "language": "ln",
      "name": "Basa Lingala"
    },
    {
      "language": "lt",
      "name": "Basa Lituania"
    },
    {
      "language": "lg",
      "name": "Basa Luganda"
    },
    {
      "language": "lb",
      "name": "Basa Luxembourg"
    },
    {
      "language": "mai",
      "name": "Basa Maithili"
    },
    {
      "language": "mg",
      "name": "Basa Malagasi"
    },
    {
      "language": "ml",
      "name": "Basa Malayalam"
    },
    {
      "language": "mt",
      "name": "Basa Maltis"
    },
    {
      "language": "zh-CN",
      "name": "Basa Mandarin (Sederhana)"
    },
    {
      "language": "zh-TW",
      "name": "Basa Mandarin (Tradisional)"
    },
    {
      "language": "mi",
      "name": "Basa Maori"
    },
    {
      "language": "mr",
      "name": "Basa Marathi"
    },
    {
      "language": "mk",
      "name": "Basa Masédonia"
    },
    {
      "language": "mni-Mtei",
      "name": "Basa Meiteilon (Manipuri)"
    },
    {
      "language": "ms",
      "name": "Basa Melayu"
    },
    {
      "language": "lus",
      "name": "Basa Mizo"
    },
    {
      "language": "mn",
      "name": "Basa Mongolia"
    },
    {
      "language": "my",
      "name": "Basa Myanmar (Burma)"
    },
    {
      "language": "ne",
      "name": "Basa Népal"
    },
    {
      "language": "no",
      "name": "Basa Norwégia"
    },
    {
      "language": "or",
      "name": "Basa Odia (Oriya)"
    },
    {
      "language": "om",
      "name": "Basa Oromo"
    },
    {
      "language": "ps",
      "name": "Basa Pashto"
    },
    {
      "language": "fa",
      "name": "Basa Pérsia"
    },
    {
      "language": "pl",
      "name": "Basa Polandia"
    },
    {
      "language": "pt",
      "name": "Basa Portugis"
    },
    {
      "language": "fr",
      "name": "Basa Prancis"
    },
    {
      "language": "pa",
      "name": "Basa Punjab"
    },
    {
      "language": "qu",
      "name": "Basa Quechua"
    },
    {
      "language": "ro",
      "name": "Basa Rumania"
    },
    {
      "language": "ru",
      "name": "Basa Rusia"
    },
    {
      "language": "sm",
      "name": "Basa Samoa"
    },
    {
      "language": "sa",
      "name": "Basa Sansakerta"
    },
    {
      "language": "nso",
      "name": "Basa Sepedi"
    },
    {
      "language": "sr",
      "name": "Basa Serbia"
    },
    {
      "language": "st",
      "name": "Basa Sésotho"
    },
    {
      "language": "sn",
      "name": "Basa Shona"
    },
    {
      "language": "sd",
      "name": "Basa Sindhi"
    },
    {
      "language": "si",
      "name": "Basa Sinhala"
    },
    {
      "language": "gd",
      "name": "Basa Skot Gaélik"
    },
    {
      "language": "sk",
      "name": "Basa Slovakia"
    },
    {
      "language": "sl",
      "name": "Basa Slovénia"
    },
    {
      "language": "so",
      "name": "Basa Somali"
    },
    {
      "language": "es",
      "name": "Basa Spanyol"
    },
    {
      "language": "su",
      "name": "Basa Sunda"
    },
    {
      "language": "sw",
      "name": "Basa Swahili"
    },
    {
      "language": "sv",
      "name": "Basa Swédia"
    },
    {
      "language": "tg",
      "name": "Basa Tajik"
    },
    {
      "language": "ta",
      "name": "Basa Tamil"
    },
    {
      "language": "tt",
      "name": "Basa Tatar"
    },
    {
      "language": "te",
      "name": "Basa Telugu"
    },
    {
      "language": "th",
      "name": "Basa Thailan"
    },
    {
      "language": "ti",
      "name": "Basa Tigrinya"
    },
    {
      "language": "ts",
      "name": "Basa Tsonga"
    },
    {
      "language": "tr",
      "name": "Basa Turki"
    },
    {
      "language": "tk",
      "name": "Basa Turkmenistan"
    },
    {
      "language": "ak",
      "name": "Basa Twi"
    },
    {
      "language": "uk",
      "name": "Basa Ukraina"
    },
    {
      "language": "ur",
      "name": "Basa Urdu"
    },
    {
      "language": "ug",
      "name": "Basa Uyghur"
    },
    {
      "language": "uz",
      "name": "Basa Uzbékistan"
    },
    {
      "language": "vi",
      "name": "Basa Viétnam"
    },
    {
      "language": "nl",
      "name": "Basa Walanda"
    },
    {
      "language": "cy",
      "name": "Basa Wélsh"
    },
    {
      "language": "xh",
      "name": "Basa Xhosa"
    },
    {
      "language": "yi",
      "name": "Basa Yiddis"
    },
    {
      "language": "yo",
      "name": "Basa Yoruba"
    },
    {
      "language": "el",
      "name": "Basa Yunani"
    },
    {
      "language": "zu",
      "name": "Basa Zulu"
    }
  ]
}
);