// EdgeTranslate主题配置
// 复制EdgeTranslate的颜色方案和样式系统

export const EdgeTranslateColors = {
  // 主色调 - EdgeTranslate的标志性蓝色
  primary: '#4a8cf7',
  primaryDark: '#3a7bd5',
  primaryLight: '#6ba3f9',
  
  // 辅助色
  secondary: '#f7f9fc',
  secondaryDark: '#e8f0fe',
  
  // 状态色
  success: '#4caf50',
  warning: '#ff9800',
  error: '#f44336',
  info: '#2196f3',
  
  // 灰度色
  grey: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  
  // 文本色
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#bdbdbd',
    hint: '#9e9e9e',
  },
  
  // 背景色
  background: {
    default: '#ffffff',
    paper: '#ffffff',
    grey: '#f5f5f5',
  },
  
  // 边框色
  border: {
    light: '#e0e0e0',
    main: '#bdbdbd',
    dark: '#757575',
  },
  
  // 阴影色
  shadow: {
    light: 'rgba(0, 0, 0, 0.08)',
    main: 'rgba(0, 0, 0, 0.12)',
    dark: 'rgba(0, 0, 0, 0.16)',
  },
};

// EdgeTranslate字体配置
export const EdgeTranslateFonts = {
  // 字体族
  fontFamily: {
    primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    monospace: '"SF Mono", Monaco, Inconsolata, "Roboto Mono", Consolas, "Courier New", monospace',
  },
  
  // 字体大小
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
  },
  
  // 字体粗细
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
  
  // 行高
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

// EdgeTranslate间距配置
export const EdgeTranslateSpacing = {
  xs: '0.25rem',   // 4px
  sm: '0.5rem',    // 8px
  md: '1rem',      // 16px
  lg: '1.5rem',    // 24px
  xl: '2rem',      // 32px
  '2xl': '3rem',   // 48px
  '3xl': '4rem',   // 64px
};

// EdgeTranslate圆角配置
export const EdgeTranslateBorderRadius = {
  none: '0',
  sm: '0.25rem',   // 4px
  md: '0.5rem',    // 8px
  lg: '0.75rem',   // 12px
  xl: '1rem',      // 16px
  full: '9999px',
};

// EdgeTranslate阴影配置
export const EdgeTranslateShadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  
  // EdgeTranslate特有的阴影
  button: '0 2px 8px rgba(0, 0, 0, 0.15)',
  buttonHover: '0 4px 12px rgba(74, 140, 247, 0.4)',
  panel: '0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08)',
  panelDragging: '0 12px 40px rgba(0, 0, 0, 0.16), 0 4px 12px rgba(0, 0, 0, 0.12)',
};

// EdgeTranslate过渡动画配置
export const EdgeTranslateTransitions = {
  // 缓动函数
  easing: {
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
  },
  
  // 持续时间
  duration: {
    shortest: '150ms',
    shorter: '200ms',
    short: '250ms',
    standard: '300ms',
    complex: '375ms',
    enteringScreen: '225ms',
    leavingScreen: '195ms',
  },
  
  // 常用过渡
  common: {
    fade: 'opacity 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    slide: 'transform 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    scale: 'transform 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    all: 'all 200ms cubic-bezier(0.4, 0, 0.2, 1)',
  },
};

// EdgeTranslate Z-Index配置
export const EdgeTranslateZIndex = {
  base: 1,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  
  // EdgeTranslate特有的层级
  translationButton: 2147483647,
  floatingPanel: 2147483646,
  sidebar: 2147483645,
};

// EdgeTranslate断点配置
export const EdgeTranslateBreakpoints = {
  xs: '0px',
  sm: '600px',
  md: '960px',
  lg: '1280px',
  xl: '1920px',
};

// 完整的EdgeTranslate主题对象
export const EdgeTranslateTheme = {
  colors: EdgeTranslateColors,
  fonts: EdgeTranslateFonts,
  spacing: EdgeTranslateSpacing,
  borderRadius: EdgeTranslateBorderRadius,
  shadows: EdgeTranslateShadows,
  transitions: EdgeTranslateTransitions,
  zIndex: EdgeTranslateZIndex,
  breakpoints: EdgeTranslateBreakpoints,
};

// CSS变量生成器
export const generateEdgeTranslateCSSVariables = () => {
  const cssVars: Record<string, string> = {};
  
  // 颜色变量
  cssVars['--et-primary'] = EdgeTranslateColors.primary;
  cssVars['--et-primary-dark'] = EdgeTranslateColors.primaryDark;
  cssVars['--et-primary-light'] = EdgeTranslateColors.primaryLight;
  cssVars['--et-secondary'] = EdgeTranslateColors.secondary;
  cssVars['--et-success'] = EdgeTranslateColors.success;
  cssVars['--et-warning'] = EdgeTranslateColors.warning;
  cssVars['--et-error'] = EdgeTranslateColors.error;
  cssVars['--et-info'] = EdgeTranslateColors.info;
  
  // 文本颜色变量
  cssVars['--et-text-primary'] = EdgeTranslateColors.text.primary;
  cssVars['--et-text-secondary'] = EdgeTranslateColors.text.secondary;
  cssVars['--et-text-disabled'] = EdgeTranslateColors.text.disabled;
  
  // 背景颜色变量
  cssVars['--et-bg-default'] = EdgeTranslateColors.background.default;
  cssVars['--et-bg-paper'] = EdgeTranslateColors.background.paper;
  cssVars['--et-bg-grey'] = EdgeTranslateColors.background.grey;
  
  // 字体变量
  cssVars['--et-font-family'] = EdgeTranslateFonts.fontFamily.primary;
  cssVars['--et-font-mono'] = EdgeTranslateFonts.fontFamily.monospace;
  
  // 阴影变量
  cssVars['--et-shadow-button'] = EdgeTranslateShadows.button;
  cssVars['--et-shadow-button-hover'] = EdgeTranslateShadows.buttonHover;
  cssVars['--et-shadow-panel'] = EdgeTranslateShadows.panel;
  
  // 过渡变量
  cssVars['--et-transition-all'] = EdgeTranslateTransitions.common.all;
  cssVars['--et-transition-fade'] = EdgeTranslateTransitions.common.fade;
  
  return cssVars;
};

// 应用CSS变量到文档
export const applyEdgeTranslateCSSVariables = () => {
  const cssVars = generateEdgeTranslateCSSVariables();
  const root = document.documentElement;
  
  Object.entries(cssVars).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
};

export default EdgeTranslateTheme;
