### Attention

* For the installation of the expansion pack in ZIP format for Firefox browser, please refer to [here](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/en/ToFirefoxUsers.md)!

* In order to further improve the translation experience, we have established an Edge Translate user communication group. Welcome to join: [Edge Translate](https://t.me/EdgeTranslate)

### Fix

* Fixed the problem that the translation result frame was covered by other page elements in some cases (#143);

* Fixed the problem that the translate icon could not pop up normally on triple-click selecting (#119);

* Fixed the [problem](https://github.com/EdgeTranslate/EdgeTranslate/projects/2#card-51366851) of abnormal style of translation result frame on some pages；

* Fixed the problem of selection range shift when using the query function of right-click menu in Mac OS (#148);

* Fixed the problem that Google page translate was broken on some pages, thanks @Atry ;

* Fixed the problem that the pronouncing loading animation wouldn't end if using Tencent translator without network connection;

### Sponsor

It took us much time and energy to develop this project. If it truly helped you in some way, you could reward us with cans of Coke to support us to keep improving it: [PayPal](https://paypal.me/EdgeTranslate).

But, this is completely __voluntary__. Sponsoring won't bring any special treatment and you can still use Edge Translate freely without sponsoring. Do it according to your capability!