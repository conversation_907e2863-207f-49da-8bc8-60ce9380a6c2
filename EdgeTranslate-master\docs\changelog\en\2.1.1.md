### Attention

* For the installation of the expansion pack in ZIP format for Firefox browser, please refer to [here](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/en/ToFirefoxUsers.md)!

* In order to further improve the translation experience, we have established an Edge Translate user communication group. Welcome to join: [Edge Translate](https://t.me/EdgeTranslate)

### Improvement

* Optimize the speed of Tencent translation requests.

* Improve the display strategy of the original text in the translation results, which is automatically collapsed when the original text is too long to avoid affecting the reading, and can be expanded by clicking on the original text.

* Improve the handling strategy when the sidebar pop-up fails, jumping to a new page to display the results when the current page cannot display the translation results, and giving the necessary explanatory information on the page.

### Fix

* Fix the sidebar issue that breaks words when doing line breaks on overflow text.

* Fix the issue where the sidebar font becomes white and the content is not visible on some black pages or in black mode (#125).

* Fix the problem with the blacklist logo appearing as a question mark under Mac OS.

* Fix the problem where the \</br\> tag used for line break was escaped, causing the line break to fail.

* Fix the problem of slow speech speed for the first pronunciation.

* Fix the problem that only the first line is displayed in the result when <PERSON><PERSON> and <PERSON><PERSON> translate multiple sentences.

* Fix the problem of displaying the title "Example" even when there is no example sentence in the word definition.

* Fix the problem of HTML tag characters being escaped in Tencent translation results.

* Fix the problem of wrong parsing of Baidu translation results in some cases.

### Sponsor

It took us much time and energy to develop this project. If it truly helped you in some way, you could reward us with cans of Coke to support us to keep improving it: [PayPal](https://paypal.me/EdgeTranslate).

But, this is completely __voluntary__. Sponsoring won't bring any special treatment and you can still use Edge Translate freely without sponsoring. Do it according to your capability!