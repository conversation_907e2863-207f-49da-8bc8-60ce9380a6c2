import { createBrowserClient } from '@supabase/ssr'

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          provider: string;
          subscription_status: 'free' | 'premium';
          preferences: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          provider: string;
          subscription_status?: 'free' | 'premium';
          preferences?: any;
        };
        Update: {
          subscription_status?: 'free' | 'premium';
          preferences?: any;
        };
      };
      ai_models: {
        Row: {
          id: string;
          name: string;
          provider: string;
          endpoint: string;
          api_key_encrypted: string;
          is_free: boolean;
          priority: number;
          is_enabled: boolean;
          max_requests_per_day: number;
          supported_languages: string[];
          created_at: string;
          updated_at: string;
        };
        Insert: {
          name: string;
          provider: string;
          endpoint: string;
          api_key_encrypted: string;
          is_free?: boolean;
          priority?: number;
          is_enabled?: boolean;
          max_requests_per_day?: number;
          supported_languages?: string[];
        };
        Update: {
          name?: string;
          provider?: string;
          endpoint?: string;
          api_key_encrypted?: string;
          is_free?: boolean;
          priority?: number;
          is_enabled?: boolean;
          max_requests_per_day?: number;
          supported_languages?: string[];
        };
      };
      usage_logs: {
        Row: {
          id: string;
          user_id: string;
          model_id: string | null;
          request_type: 'translate' | 'ocr';
          characters_count: number;
          source_language: string | null;
          target_language: string | null;
          success: boolean;
          error_message: string | null;
          response_time_ms: number | null;
          created_at: string;
        };
        Insert: {
          user_id: string;
          model_id?: string | null;
          request_type: 'translate' | 'ocr';
          characters_count: number;
          source_language?: string | null;
          target_language?: string | null;
          success?: boolean;
          error_message?: string | null;
          response_time_ms?: number | null;
        };
        Update: never;
      };
      languages: {
        Row: {
          id: string;
          code: string;
          name: string;
          native_name: string | null;
          is_enabled: boolean;
          ocr_supported: boolean;
          translation_supported: boolean;
          created_at: string;
        };
        Insert: {
          code: string;
          name: string;
          native_name?: string | null;
          is_enabled?: boolean;
          ocr_supported?: boolean;
          translation_supported?: boolean;
        };
        Update: {
          name?: string;
          native_name?: string | null;
          is_enabled?: boolean;
          ocr_supported?: boolean;
          translation_supported?: boolean;
        };
      };
      user_words: {
        Row: {
          id: string;
          user_id: string;
          word: string;
          translation: string;
          source_language: string;
          target_language: string;
          context: string | null;
          pronunciation: string | null;
          part_of_speech: string | null;
          difficulty_level: number;
          review_count: number;
          last_reviewed_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          word: string;
          translation: string;
          source_language?: string;
          target_language?: string;
          context?: string | null;
          pronunciation?: string | null;
          part_of_speech?: string | null;
          difficulty_level?: number;
          review_count?: number;
          last_reviewed_at?: string | null;
        };
        Update: {
          word?: string;
          translation?: string;
          source_language?: string;
          target_language?: string;
          context?: string | null;
          pronunciation?: string | null;
          part_of_speech?: string | null;
          difficulty_level?: number;
          review_count?: number;
          last_reviewed_at?: string | null;
          updated_at?: string;
        };
      };
    };
  };
};

export const createClient = () =>
  createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
