### 注意

* 关于火狐浏览器安装zip格式的扩展包请参考[这里](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/zh_CN/%E8%87%B4%E7%81%AB%E7%8B%90%E7%94%A8%E6%88%B7.md)！

* 为了进一步改善翻译体验，我们建立了侧边翻译用户交流群，欢迎大家加入：[侧边翻译用户交流QQ群](https://jq.qq.com/?_wv=1027&k=gT5EYfFB)

### 全新设计的翻译结果展示框

本次更新带来了 __全新设计__ 的翻译结果展示框，相比旧版本更加简洁美观，并且更有效地利用了空间，同样大小的展示框可以展示更多的内容！

![old_new_compare](../../images/old_new_compare.png)

除了更加美观高效的展示框样式，本次更新还带来了两个新的功能：

* 展示框中展示的内容可自定义；现在，你可以隐藏掉那些不需要的内容，让你的翻译框更加简洁清晰！

* 支持自动折叠过长的内容；如果翻译结果中的某些内容过长，你可以选择将它折叠起来，需要的时候再展开！

### PDF阅读器支持黑暗模式

本次更新还带来了对内置PDF阅读器黑暗模式的支持，暗光下看文献不再刺眼！

黑暗模式默认自动跟随系统调整。即，当系统设定为亮色模式时，PDF显示为正常色彩：

![pdf_light](../../images/pdf_light.png)

而当系统设定为暗色模式时，PDF将会自动显示为暗色：

![pdf_dark](../../images/pdf_dark.png)

如果你有特殊的需求，那么点击PDF阅读器右上角的 `A` 按钮即可调整PDF显示模式，支持 __自动__ ，__始终黑暗__ 以及 __始终原色__ 三种模式。

### 其他优化与修复

* 优化了使用从右到左布局时的展示效果；

* 更新了谷歌翻译接口以提供更高质量的翻译；

* 修复了谷歌翻译接口发音出错的问题(#169)；

* 修复了使用Chrome原生PDF阅读器时，翻译结果展示框大小失控的问题(#163)；

* 修复了某些场景下划词翻译按钮无法显示的问题；

### 关于打赏

开发这个项目花费了我们许多的时间和精力，如果你真的觉得这个项目对你有帮助，不妨请我们喝罐可乐，支持我们继续做下去！

当然，这 __纯属自愿__，打赏并不能获得什么优待，不打赏也不会有任何影响，请量力而为！

| 微信 | 支付宝 |
| :-: | :-: |
| <img src="https://user-images.githubusercontent.com/25877145/80864662-b6617c00-8cb6-11ea-915a-582ca046118c.png" height=200 alt="微信支付"/> | <img src="https://user-images.githubusercontent.com/25877145/80864685-ced19680-8cb6-11ea-94e5-f5ca8e4389b9.jpg" height=200 alt="支付宝支付"/> |