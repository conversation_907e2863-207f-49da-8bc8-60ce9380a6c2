import { createTheme, Theme } from '@mui/material/styles';
import { CurrentTheme } from '../stores/useThemeStore';

// 基础颜色配置
const colors = {
  primary: {
    light: '#42a5f5',
    main: '#1976d2',
    dark: '#1565c0',
  },
  secondary: {
    light: '#f48fb1',
    main: '#e91e63',
    dark: '#c2185b',
  },
  success: {
    light: '#66bb6a',
    main: '#4caf50',
    dark: '#388e3c',
  },
  warning: {
    light: '#ffb74d',
    main: '#ff9800',
    dark: '#f57c00',
  },
  error: {
    light: '#ef5350',
    main: '#f44336',
    dark: '#d32f2f',
  },
  info: {
    light: '#29b6f6',
    main: '#2196f3',
    dark: '#1976d2',
  },
};

// 浅色主题配置
export const lightTheme = createTheme({
  palette: {
    mode: 'light',
    primary: colors.primary,
    secondary: colors.secondary,
    success: colors.success,
    warning: colors.warning,
    error: colors.error,
    info: colors.info,
    background: {
      default: 'rgba(255, 255, 255, 0.85)',
      paper: 'rgba(255, 255, 255, 0.95)',
    },
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
    },
    divider: 'rgba(0, 0, 0, 0.12)',
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    fontSize: 14,
    h1: { fontSize: '2rem', fontWeight: 600 },
    h2: { fontSize: '1.75rem', fontWeight: 600 },
    h3: { fontSize: '1.5rem', fontWeight: 600 },
    h4: { fontSize: '1.25rem', fontWeight: 600 },
    h5: { fontSize: '1.125rem', fontWeight: 600 },
    h6: { fontSize: '1rem', fontWeight: 600 },
    subtitle1: { fontSize: '1rem', fontWeight: 500 },
    subtitle2: { fontSize: '0.875rem', fontWeight: 500 },
    body1: { fontSize: '1rem', lineHeight: 1.5 },
    body2: { fontSize: '0.875rem', lineHeight: 1.43 },
    caption: { fontSize: '0.75rem', lineHeight: 1.33 },
    overline: { fontSize: '0.75rem', fontWeight: 500, textTransform: 'uppercase' },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiAccordion: {
      styleOverrides: {
        root: {
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          border: '1px solid rgba(0, 0, 0, 0.12)',
          borderRadius: '8px !important',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          '&:before': { 
            display: 'none' 
          },
          '&.Mui-expanded': {
            margin: '8px 0',
          },
        },
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: {
          minHeight: '48px',
          padding: '0 16px',
          '&.Mui-expanded': {
            minHeight: '48px',
          },
        },
        content: {
          margin: '12px 0',
          '&.Mui-expanded': {
            margin: '12px 0',
          },
        },
      },
    },
    MuiAccordionDetails: {
      styleOverrides: {
        root: {
          padding: '8px 16px 16px',
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          minHeight: '40px',
          fontSize: '0.875rem',
          textTransform: 'none',
          fontWeight: 500,
          '&.Mui-selected': {
            fontWeight: 600,
          },
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: '40px',
        },
        indicator: {
          height: '2px',
          borderRadius: '1px',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: '8px',
        },
        contained: {
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          '&:hover': {
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
          },
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
        },
      },
    },
  },
  transitions: {
    duration: {
      shortest: 120,
      shorter: 120,
      short: 120,
      standard: 120,
      complex: 120,
      enteringScreen: 120,
      leavingScreen: 120,
    },
    easing: {
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
    },
  },
});

// 深色主题配置
export const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      light: '#90caf9',
      main: '#2196f3',
      dark: '#1976d2',
    },
    secondary: {
      light: '#f8bbd9',
      main: '#f48fb1',
      dark: '#e91e63',
    },
    success: colors.success,
    warning: colors.warning,
    error: colors.error,
    info: colors.info,
    background: {
      default: 'rgba(30, 30, 30, 0.85)',
      paper: 'rgba(42, 42, 42, 0.95)',
    },
    text: {
      primary: 'rgba(255, 255, 255, 0.87)',
      secondary: 'rgba(255, 255, 255, 0.6)',
    },
    divider: 'rgba(255, 255, 255, 0.12)',
  },
  typography: lightTheme.typography,
  shape: lightTheme.shape,
  components: {
    ...lightTheme.components,
    MuiAccordion: {
      styleOverrides: {
        root: {
          backdropFilter: 'blur(10px)',
          backgroundColor: 'rgba(42, 42, 42, 0.8)',
          border: '1px solid rgba(255, 255, 255, 0.12)',
          borderRadius: '8px !important',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
          '&:before': { 
            display: 'none' 
          },
          '&.Mui-expanded': {
            margin: '8px 0',
          },
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
          },
        },
      },
    },
  },
  transitions: lightTheme.transitions,
});

// 创建主题函数
export const createMuiTheme = (mode: CurrentTheme): Theme => {
  return mode === 'dark' ? darkTheme : lightTheme;
};

// 获取主题相关的CSS变量
export const getThemeVariables = (mode: CurrentTheme) => {
  if (mode === 'dark') {
    return {
      '--sidebar-bg': 'rgba(30, 30, 30, 0.85)',
      '--sidebar-paper': 'rgba(42, 42, 42, 0.95)',
      '--sidebar-border': 'rgba(255, 255, 255, 0.1)',
      '--sidebar-text': 'rgba(255, 255, 255, 0.87)',
      '--sidebar-text-secondary': 'rgba(255, 255, 255, 0.6)',
      '--sidebar-shadow': 'rgba(0, 0, 0, 0.3)',
      '--sidebar-hover': 'rgba(255, 255, 255, 0.08)',
      '--sidebar-active': 'rgba(255, 255, 255, 0.12)',
      '--sidebar-divider': 'rgba(255, 255, 255, 0.12)',
    };
  } else {
    return {
      '--sidebar-bg': 'rgba(255, 255, 255, 0.85)',
      '--sidebar-paper': 'rgba(255, 255, 255, 0.95)',
      '--sidebar-border': 'rgba(0, 0, 0, 0.1)',
      '--sidebar-text': 'rgba(0, 0, 0, 0.87)',
      '--sidebar-text-secondary': 'rgba(0, 0, 0, 0.6)',
      '--sidebar-shadow': 'rgba(0, 0, 0, 0.15)',
      '--sidebar-hover': 'rgba(0, 0, 0, 0.04)',
      '--sidebar-active': 'rgba(0, 0, 0, 0.08)',
      '--sidebar-divider': 'rgba(0, 0, 0, 0.12)',
    };
  }
};
