### 注意

-   关于火狐浏览器安装 zip 格式的扩展包请参考[这里](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/zh_CN/%E8%87%B4%E7%81%AB%E7%8B%90%E7%94%A8%E6%88%B7.md)！

-   为了进一步改善翻译体验，我们建立了侧边翻译用户交流群，欢迎大家加入：[侧边翻译用户交流 QQ 群](https://jq.qq.com/?_wv=1027&k=gT5EYfFB)

### 新增

-   支持[DeepL](https://www.deepl.com/translator)翻译接口;
-   允许自定义划词翻译按钮出现的位置(左上/右上/左下/右下) (#349), 感谢 @derlans 的贡献;
-   允许通过自定义快捷键来隐藏网页翻译的顶部栏;

### 改进

-   界面和文档的俄语支持, 感谢 @ViktorOn 的贡献;
-   更新 pdf.js 的版本到 v2.13.216;
-   更新谷歌和必应接口;

### 修复

-   在翻译框初始化时在左侧闪烁的问题 (#201);

-   固定的侧边栏在开启收缩页面的设置后闪烁的问题 (#352);

### 关于打赏

开发这个项目花费了我们许多的时间和精力，如果你真的觉得这个项目对你有帮助，不妨请我们喝罐可乐，支持我们继续做下去！

当然，这 **纯属自愿**，打赏并不能获得什么优待，不打赏也不会有任何影响，请量力而为！

|                                                                    微信                                                                     |                                                                    支付宝                                                                     |
| :-----------------------------------------------------------------------------------------------------------------------------------------: | :-------------------------------------------------------------------------------------------------------------------------------------------: |
| <img src="https://user-images.githubusercontent.com/25877145/80864662-b6617c00-8cb6-11ea-915a-582ca046118c.png" height=200 alt="微信支付"/> | <img src="https://user-images.githubusercontent.com/25877145/80864685-ced19680-8cb6-11ea-94e5-f5ca8e4389b9.jpg" height=200 alt="支付宝支付"/> |
