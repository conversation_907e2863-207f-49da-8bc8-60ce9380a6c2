import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';

// GET /api/models - 获取所有AI模型
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // 验证用户认证
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取模型列表
    const { data: models, error } = await supabase
      .from('ai_models')
      .select('*')
      .order('priority', { ascending: true });

    if (error) {
      console.error('Get models error:', error);
      return NextResponse.json({ error: 'Failed to fetch models' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: models });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/models - 创建新的AI模型
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // 验证用户认证
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const { name, provider, endpoint, api_key, is_free, priority, supported_languages } = body;

    // 验证必填字段
    if (!name || !provider || !endpoint) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // 加密API密钥（简单示例，实际应使用更安全的加密方法）
    const api_key_encrypted = api_key ? Buffer.from(api_key).toString('base64') : '';

    // 插入新模型
    const { data: model, error } = await supabase
      .from('ai_models')
      .insert({
        name,
        provider,
        endpoint,
        api_key_encrypted,
        is_free: is_free || false,
        priority: priority || 0,
        supported_languages: supported_languages || []
      })
      .select()
      .single();

    if (error) {
      console.error('Create model error:', error);
      return NextResponse.json({ error: 'Failed to create model' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: model }, { status: 201 });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/models - 更新AI模型
export async function PUT(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // 验证用户认证
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const { id, name, provider, endpoint, api_key, is_free, priority, is_enabled, supported_languages } = body;

    if (!id) {
      return NextResponse.json({ error: 'Model ID is required' }, { status: 400 });
    }

    // 准备更新数据
    const updateData: any = {};
    if (name !== undefined) updateData.name = name;
    if (provider !== undefined) updateData.provider = provider;
    if (endpoint !== undefined) updateData.endpoint = endpoint;
    if (api_key !== undefined) updateData.api_key_encrypted = Buffer.from(api_key).toString('base64');
    if (is_free !== undefined) updateData.is_free = is_free;
    if (priority !== undefined) updateData.priority = priority;
    if (is_enabled !== undefined) updateData.is_enabled = is_enabled;
    if (supported_languages !== undefined) updateData.supported_languages = supported_languages;

    // 更新模型
    const { data: model, error } = await supabase
      .from('ai_models')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Update model error:', error);
      return NextResponse.json({ error: 'Failed to update model' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: model });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/models - 删除AI模型
export async function DELETE(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // 验证用户认证
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 从URL参数获取模型ID
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Model ID is required' }, { status: 400 });
    }

    // 删除模型
    const { error } = await supabase
      .from('ai_models')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Delete model error:', error);
      return NextResponse.json({ error: 'Failed to delete model' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Model deleted successfully' });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
