import React, { useRef, useEffect, RefObject } from 'react';
import { Box, Typography, IconButton, Paper, Select, MenuItem, FormControl } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PushPinIcon from '@mui/icons-material/PushPin';
import PushPinOutlinedIcon from '@mui/icons-material/PushPinOutlined';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import { useSidebarStore } from '../../shared/stores/useSidebarStore';
import { useThemeStore } from '../../shared/stores/useThemeStore';
import { useTranslationStore } from '../../shared/stores/useTranslationStore';

import { TranslationResult } from './components/TranslationResult';

export const TranslatorSidebar: React.FC = () => {
  const sidebarRef = useRef<HTMLDivElement | null>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  const {
    position,
    size,
    isPinned,
    isVisible,
    isDragging,
    isResizing,
    togglePin,
    toggleVisibility,
    updatePosition,
    updateSize,
    setDragging,
    setResizing
  } = useSidebarStore();

  const { currentTheme, toggleTheme } = useThemeStore();
  const { currentText, results, isLoading, errors, translateText } = useTranslationStore();

  // 组件挂载状态检查
  const isMountedRef = React.useRef(true);
  React.useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // 当前选择的翻译引擎
  const [selectedEngine, setSelectedEngine] = React.useState<'google' | 'openai' | 'kimi'>('google');

  // 处理引擎切换
  const handleEngineChange = React.useCallback((newEngine: 'google' | 'openai' | 'kimi') => {
    if (!isMountedRef.current) {
      console.warn('⚠️ Component unmounted, ignoring engine change');
      return;
    }
    console.log('🔄 Engine change requested:', newEngine);
    setSelectedEngine(newEngine);
    // 如果有当前文本，重新翻译
    if (currentText) {
      console.log('🚀 Re-translating with new engine:', newEngine);
      translateText(currentText, [newEngine]);
    }
  }, [currentText, translateText]);

  // 简单的拖拽功能
  const [dragState, setDragState] = React.useState({
    isDragging: false,
    startX: 0,
    startY: 0,
    startLeft: 0,
    startTop: 0,
  });

  const handleMouseDown = (e: React.MouseEvent) => {
    // 移除钉住状态限制，侧边栏默认就可以拖拽
    e.preventDefault();
    e.stopPropagation();

    const rect = sidebarRef.current?.getBoundingClientRect();
    if (!rect) return;

    if (!isMountedRef.current) return;

    setDragState({
      isDragging: true,
      startX: e.clientX,
      startY: e.clientY,
      startLeft: rect.left,
      startTop: rect.top,
    });

    setDragging(true);
  };

  React.useEffect(() => {
    if (!dragState.isDragging) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isMountedRef.current) return;

      const deltaX = e.clientX - dragState.startX;
      const deltaY = e.clientY - dragState.startY;

      const newX = dragState.startLeft + deltaX;
      const newY = dragState.startTop + deltaY;

      // 边界检查
      const maxX = window.innerWidth - size.width;
      const maxY = window.innerHeight - size.height;

      const constrainedX = Math.max(0, Math.min(newX, maxX));
      const constrainedY = Math.max(0, Math.min(newY, maxY));

      updatePosition({ x: constrainedX, y: constrainedY });
    };

    const handleMouseUp = () => {
      if (!isMountedRef.current) return;

      setDragState(prev => ({ ...prev, isDragging: false }));
      setDragging(false);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [dragState, size, updatePosition, setDragging]);

  // 点击外部关闭功能
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!isMountedRef.current) return;

      if (
        !isPinned &&
        isVisible &&
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        toggleVisibility();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isPinned, isVisible, toggleVisibility]);

  // 边缘吸附功能
  useEffect(() => {
    if (isDragging === false) { // 移除isPinned限制，拖拽结束后都可以吸附
      const { x, y } = position;
      const { width, height } = size;
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // 检查是否靠近边缘
      const snapThreshold = 50;
      let newPosition = { ...position };
      let newSize = { ...size };

      // 左边缘吸附
      if (x < snapThreshold) {
        newPosition.x = 0;
        newPosition.y = 0;
        newSize.height = viewportHeight - 100; // 留出一些边距
      }
      // 右边缘吸附
      else if (x + width > viewportWidth - snapThreshold) {
        newPosition.x = viewportWidth - width;
        newPosition.y = 0;
        newSize.height = viewportHeight - 100;
      }

      // 更新位置和大小
      if (newPosition.x !== position.x || newPosition.y !== position.y) {
        updatePosition(newPosition);
      }
      if (newSize.width !== size.width || newSize.height !== size.height) {
        updateSize(newSize);
      }
    }
  }, [position, size, isPinned, isDragging, updatePosition, updateSize]);

  if (!isVisible) {
    return null;
  }

  return (
    <>
      <Paper
        ref={sidebarRef}
        elevation={8}
        sx={{
          position: 'fixed',
          left: position.x,
          top: position.y,
          width: size.width,
          height: size.height,
          zIndex: 2147483647, // 最高层级
          borderRadius: '8px',
          overflow: 'visible', // 保持visible以支持下拉菜单
          pointerEvents: 'auto',
          backdropFilter: 'blur(20px) saturate(180%)',
          backgroundColor: currentTheme === 'dark' 
            ? 'rgba(30, 30, 30, 0.85)' 
            : 'rgba(255, 255, 255, 0.85)',
          border: '1px solid',
          borderColor: currentTheme === 'dark' 
            ? 'rgba(255, 255, 255, 0.1)' 
            : 'rgba(0, 0, 0, 0.1)',
          boxShadow: isDragging
            ? (currentTheme === 'dark'
                ? '0 16px 48px rgba(0, 0, 0, 0.5)'
                : '0 16px 48px rgba(0, 0, 0, 0.25)')
            : (currentTheme === 'dark'
                ? '0 8px 32px rgba(0, 0, 0, 0.3)'
                : '0 8px 32px rgba(0, 0, 0, 0.15)'),
          transition: isDragging || isResizing ? 'none' : 'all 0.3s ease-out',
          cursor: isDragging ? 'grabbing' : 'default',
          transform: isDragging ? 'scale(1.02)' : 'scale(1)',
          opacity: isDragging ? 0.95 : 1,
          display: 'flex',
          flexDirection: 'column',
        }}

      >
        {/* Header */}
        <Box
          ref={headerRef}
          data-draggable="true"
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 1.5,
            bgcolor: currentTheme === 'dark' ? 'rgba(66, 66, 66, 0.8)' : 'rgba(25, 118, 210, 0.9)',
            color: 'white',
            cursor: isDragging ? 'grabbing' : 'grab',
            borderBottom: '1px solid',
            borderBottomColor: currentTheme === 'dark'
              ? 'rgba(255, 255, 255, 0.1)'
              : 'rgba(255, 255, 255, 0.2)',
            userSelect: 'none', // 防止文本选择
            transition: isDragging ? 'none' : 'all 0.2s ease',
            '&:hover': {
              bgcolor: currentTheme === 'dark' ? 'rgba(76, 76, 76, 0.9)' : 'rgba(21, 101, 192, 1)',
              transform: 'translateY(-1px)',
            },
            '&:active': {
              transform: 'translateY(0px)',
            },
            opacity: isDragging ? 0.9 : 1,
          }}
          onMouseDown={(e) => {
            // 检查是否点击的是下拉选择器或其子元素
            const target = e.target as HTMLElement;
            if (target.closest('.MuiSelect-root') || target.closest('.MuiFormControl-root')) {
              console.log('🚫 Ignoring mousedown on select component');
              return; // 不处理下拉选择器的鼠标事件
            }
            handleMouseDown(e);
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
            <DragIndicatorIcon sx={{ mr: 1, fontSize: 18 }} />
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <Select
                value={selectedEngine}
                onChange={(e) => {
                  console.log('📝 Select onChange triggered:', e.target.value);
                  handleEngineChange(e.target.value as 'google' | 'openai' | 'kimi');
                }}
                onClick={(e) => {
                  console.log('🖱️ Select clicked');
                  e.stopPropagation(); // 防止事件冒泡到拖拽处理器
                }}
                MenuProps={{
                  disablePortal: false, // 渲染到body，确保正确定位
                  PaperProps: {
                    sx: {
                      zIndex: 2147483648, // 确保在最顶层
                      maxHeight: 200,
                      minWidth: 120,
                      '& .MuiMenuItem-root': {
                        fontSize: '0.875rem',
                        minHeight: 32,
                      }
                    }
                  },
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left',
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left',
                  },
                  // 确保菜单正确定位到选择器下方
                  getContentAnchorEl: null,
                }}
                sx={{
                  fontSize: '0.875rem',
                  '& .MuiSelect-select': {
                    py: 0.5,
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                  color: 'inherit',
                }}
              >
                <MenuItem
                  value="google"
                  onClick={(e) => {
                    console.log('🌐 Google selected');
                    e.stopPropagation();
                  }}
                >
                  Google 翻译
                </MenuItem>
                <MenuItem
                  value="openai"
                  onClick={(e) => {
                    console.log('🤖 OpenAI selected');
                    e.stopPropagation();
                  }}
                >
                  GPT-4o-mini
                </MenuItem>
                <MenuItem
                  value="kimi"
                  onClick={(e) => {
                    console.log('🌙 Kimi selected');
                    e.stopPropagation();
                  }}
                >
                  Kimi
                </MenuItem>
              </Select>
            </FormControl>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              size="small"
              onClick={toggleTheme}
              sx={{ color: 'inherit', mr: 0.5 }}
              title="切换主题"
            >
              {currentTheme === 'dark' ? <LightModeIcon /> : <DarkModeIcon />}
            </IconButton>
            
            <IconButton
              size="small"
              onClick={togglePin}
              sx={{ color: 'inherit', mr: 0.5 }}
              title={isPinned ? '取消钉住' : '钉住'}
            >
              {isPinned ? <PushPinIcon /> : <PushPinOutlinedIcon />}
            </IconButton>
            
            <IconButton
              size="small"
              onClick={toggleVisibility}
              sx={{ color: 'inherit' }}
              title="关闭"
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Content Area */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'auto',
          maxHeight: 'calc(100% - 120px)', // 减去头部和底部的高度
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: 'transparent',
          },
          '&::-webkit-scrollbar-thumb': {
            background: currentTheme === 'dark' ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.3)',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb:hover': {
            background: currentTheme === 'dark' ? 'rgba(255,255,255,0.5)' : 'rgba(0,0,0,0.5)',
          },
        }}>
          {/* Translation Result */}
          <TranslationResult selectedEngine={selectedEngine} />
        </Box>




      </Paper>
    </>
  );
};
