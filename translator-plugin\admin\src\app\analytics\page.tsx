'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  CircularProgress,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Translate as TranslateIcon,
  Speed as SpeedIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

interface UsageStats {
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  total_characters: number;
  average_response_time: number;
  requests_by_date: { date: string; count: number }[];
  requests_by_model: { model_name: string; count: number }[];
  requests_by_language: { language: string; count: number }[];
}

interface UsageLog {
  id: string;
  user_email: string;
  model_name: string;
  request_type: 'translate' | 'ocr';
  characters_count: number;
  source_language: string;
  target_language: string;
  success: boolean;
  response_time_ms: number;
  created_at: string;
}

export default function AnalyticsPage() {
  const [stats, setStats] = useState<UsageStats | null>(null);
  const [logs, setLogs] = useState<UsageLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('7d');

  useEffect(() => {
    loadAnalytics();
  }, [timeRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟统计数据
      const mockStats: UsageStats = {
        total_requests: 1250,
        successful_requests: 1180,
        failed_requests: 70,
        total_characters: 45680,
        average_response_time: 850,
        requests_by_date: [
          { date: '2025-01-16', count: 180 },
          { date: '2025-01-17', count: 220 },
          { date: '2025-01-18', count: 195 },
          { date: '2025-01-19', count: 240 },
          { date: '2025-01-20', count: 210 },
          { date: '2025-01-21', count: 185 },
          { date: '2025-01-22', count: 20 }
        ],
        requests_by_model: [
          { model_name: 'Google Translate', count: 800 },
          { model_name: 'OpenAI GPT-4', count: 300 },
          { model_name: 'Kimi', count: 150 }
        ],
        requests_by_language: [
          { language: 'en-zh', count: 450 },
          { language: 'zh-en', count: 380 },
          { language: 'ja-zh', count: 220 },
          { language: 'ko-zh', count: 200 }
        ]
      };

      // 模拟日志数据
      const mockLogs: UsageLog[] = [
        {
          id: '1',
          user_email: '<EMAIL>',
          model_name: 'Google Translate',
          request_type: 'translate',
          characters_count: 45,
          source_language: 'en',
          target_language: 'zh',
          success: true,
          response_time_ms: 650,
          created_at: '2025-01-22T10:30:00Z'
        },
        {
          id: '2',
          user_email: '<EMAIL>',
          model_name: 'OpenAI GPT-4',
          request_type: 'translate',
          characters_count: 120,
          source_language: 'zh',
          target_language: 'en',
          success: true,
          response_time_ms: 1200,
          created_at: '2025-01-22T10:25:00Z'
        },
        {
          id: '3',
          user_email: '<EMAIL>',
          model_name: 'Google Translate',
          request_type: 'ocr',
          characters_count: 0,
          source_language: 'auto',
          target_language: null,
          success: false,
          response_time_ms: 0,
          created_at: '2025-01-22T10:20:00Z'
        }
      ];
      
      setStats(mockStats);
      setLogs(mockLogs);
    } catch (err: any) {
      setError('加载统计数据失败');
      console.error('Load analytics error:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSuccessRate = () => {
    if (!stats) return 0;
    return ((stats.successful_requests / stats.total_requests) * 100).toFixed(1);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* 页面标题 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1">
          用量统计
        </Typography>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>时间范围</InputLabel>
          <Select
            value={timeRange}
            label="时间范围"
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <MenuItem value="1d">今天</MenuItem>
            <MenuItem value="7d">最近7天</MenuItem>
            <MenuItem value="30d">最近30天</MenuItem>
            <MenuItem value="90d">最近90天</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : stats ? (
        <>
          {/* 统计卡片 */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <TranslateIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                    <Box>
                      <Typography variant="h4" color="primary.main">
                        {stats.total_requests.toLocaleString()}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        总请求数
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <TrendingUpIcon sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                    <Box>
                      <Typography variant="h4" color="success.main">
                        {getSuccessRate()}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        成功率
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SpeedIcon sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                    <Box>
                      <Typography variant="h4" color="info.main">
                        {stats.average_response_time}ms
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        平均响应时间
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ErrorIcon sx={{ fontSize: 40, color: 'error.main', mr: 2 }} />
                    <Box>
                      <Typography variant="h4" color="error.main">
                        {stats.failed_requests}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        失败请求数
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* 模型使用统计 */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    模型使用分布
                  </Typography>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>模型</TableCell>
                        <TableCell align="right">请求数</TableCell>
                        <TableCell align="right">占比</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {stats.requests_by_model.map((item) => (
                        <TableRow key={item.model_name}>
                          <TableCell>{item.model_name}</TableCell>
                          <TableCell align="right">{item.count}</TableCell>
                          <TableCell align="right">
                            {((item.count / stats.total_requests) * 100).toFixed(1)}%
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    语言对分布
                  </Typography>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>语言对</TableCell>
                        <TableCell align="right">请求数</TableCell>
                        <TableCell align="right">占比</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {stats.requests_by_language.map((item) => (
                        <TableRow key={item.language}>
                          <TableCell>{item.language}</TableCell>
                          <TableCell align="right">{item.count}</TableCell>
                          <TableCell align="right">
                            {((item.count / stats.total_requests) * 100).toFixed(1)}%
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* 最近请求日志 */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                最近请求日志
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>用户</TableCell>
                      <TableCell>模型</TableCell>
                      <TableCell>类型</TableCell>
                      <TableCell>语言</TableCell>
                      <TableCell>字符数</TableCell>
                      <TableCell>状态</TableCell>
                      <TableCell>响应时间</TableCell>
                      <TableCell>时间</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {logs.map((log) => (
                      <TableRow key={log.id} hover>
                        <TableCell>{log.user_email}</TableCell>
                        <TableCell>{log.model_name}</TableCell>
                        <TableCell>
                          <Chip
                            label={log.request_type === 'translate' ? '翻译' : 'OCR'}
                            size="small"
                            color={log.request_type === 'translate' ? 'primary' : 'secondary'}
                          />
                        </TableCell>
                        <TableCell>
                          {log.target_language ? `${log.source_language} → ${log.target_language}` : log.source_language}
                        </TableCell>
                        <TableCell>{log.characters_count}</TableCell>
                        <TableCell>
                          <Chip
                            label={log.success ? '成功' : '失败'}
                            size="small"
                            color={log.success ? 'success' : 'error'}
                          />
                        </TableCell>
                        <TableCell>{log.response_time_ms}ms</TableCell>
                        <TableCell>{formatDate(log.created_at)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </>
      ) : null}
    </Container>
  );
}
