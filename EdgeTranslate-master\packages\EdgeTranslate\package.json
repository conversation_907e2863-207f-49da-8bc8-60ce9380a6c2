{"name": "edge_translate", "version": "2.4.3", "private": true, "scripts": {"test": "jest", "format": "eslint 'src/**/*.js' 'config/**/*.js' --fix", "dev:chrome": "gulp dev --browser chrome", "dev:firefox": "gulp dev --browser firefox", "build:chrome": "gulp build --browser chrome", "build:firefox": "gulp build --browser firefox", "pack:chrome": "gulp pack --browser chrome", "pack:firefox": "gulp pack --browser firefox", "e2e:chrome": "SELENIUM_BROWSER=chrome jest -i --config './test/e2e/jest/jest.config.js'", "e2e:firefox": "SELENIUM_BROWSER=firefox jest -i --config './test/e2e/jest/jest.config.js'", "build": "node -e \"console.log('\\npacking package for chrome')\" && gulp pack --browser chrome && node -e \"console.log('\\npacking package for firefox')\" && gulp pack --browser firefox"}, "devDependencies": {"@babel/core": "^7.17.7", "@babel/eslint-parser": "^7.17.0", "@babel/plugin-transform-react-jsx": "^7.17.3", "@babel/preset-env": "^7.16.11", "@svgr/webpack": "^5.5.0", "adm-zip": "^0.5.9", "axios": "^0.22.0", "babel-loader": "^8.2.3", "chromedriver": "^100.0.0", "crypto": "^1.0.1", "css": "^3.0.0", "del": "^6.0.0", "geckodriver": "^2.0.4", "gulp": "^4.0.2", "gulp-eslint": "^6.0.0", "gulp-stylus": "^2.7.1", "gulp-terser": "^2.1.0", "gulp-zip": "^5.1.0", "jest": "^27.5.1", "jest-chrome": "^0.7.2", "jest-image-snapshot": "^4.5.1", "jest-raw-loader": "^1.0.1", "lodash": "^4.17.21", "merge-stream": "^2.0.0", "minimist": "^1.2.6", "mockttp": "^1.2.2", "querystring": "^0.2.1", "raw-loader": "^4.0.2", "selenium-webdriver": "^4.1.1", "webpack": "^5.70.0", "webpack-merge": "^5.8.0", "webpack-stream": "^6.1.2"}, "dependencies": {"@edge_translate/translators": "^0.1.4", "dompurify": "^2.3.6", "preact": "^10.6.6", "preact-render-to-string": "^5.1.20", "react-shadow": "^19.0.2", "react-use": "^17.3.2", "resize-observer-polyfill": "^1.5.1", "simplebar-react": "^2.3.6", "styled-components": "5.3.0"}, "jest": {"verbose": true, "testEnvironment": "jsdom", "testRegex": "(/test/unit/.*\\.(test|spec))\\.(ts|tsx|js)$", "testTimeout": 10000, "transform": {"^.+\\.[t|j]sx?$": "babel-jest", "\\.(css|less)$": "jest-raw-loader"}, "moduleDirectories": ["node_modules", "src"], "setupFilesAfterEnv": ["./test/unit/jest.setup.js"]}}