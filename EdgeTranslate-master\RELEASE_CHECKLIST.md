## 功能测试

### 核心功能

-   [x] 划词弹出翻译按钮，划词立即翻译，翻译完成后取消文本选择

-   [x] 双击弹出翻译按钮，双击立即翻译

-   [ ] 侧边栏发音按钮测试，原始文本编辑/重新翻译（火狐上无效），翻译结果复制按钮

-   [ ] source、target 发音，详细意思，定义和例句过长内容的展开/折叠

-   [x] 侧边栏到设置页面的跳转

-   [ ] 关闭侧边栏自动停止发音

-   [ ] popup 输入框查词，互译模式，语言设定

-   [ ] pdf 文件权限请求，自动跳转，划词翻译

-   [ ] 提示页面自动跳转，翻译结果展示

-   [ ] 各翻译接口功能测试

-   [ ] 混合翻译功能测试

-   [ ] 语言设定与默认翻译源设定的联动

-   [ ] 错误信息展示

-   [ ] PDF 阅读器与 chrome 自带阅读器的切换，黑色阅读模式

### UI

-   [x] 弹出侧边栏时压缩/不压缩页面

-   [x] 侧边栏固定/取消固定/自动隐藏

-   [ ] 侧边栏固定模式/悬浮模式切换

-   [ ] 侧边栏拖动/自动贴边/大小调整

-   [ ] 翻译按钮随页面滚动

-   [ ] 发音失败后顶部弹出错误提示框

-   [x] 从右到左显示内容

-   [ ] 翻译按钮出现在鼠标的`左上/右上/左下/右下`位置

### 网页翻译

-   [ ] 各翻译器功能测试

-   [ ] 默认翻译器设置

### 黑名单功能

-   [ ] 网址加入/移出黑名单

-   [ ] 域名加入/移出黑名单

### 快捷键

-   [ ] 激活 popup

-   [ ] 启用/禁用互译模式

-   [ ] 展开翻译语言设定

-   [ ] 交换源语言/目标语言

-   [ ] 翻译选中单词

-   [ ] 朗读原始文本

-   [ ] 朗读翻译结果

-   [ ] 复制翻译结果

-   [ ] 固定侧边栏

-   [ ] 关闭侧边栏

-   [ ] 朗读选中单词

-   [ ] 翻译当前页面

-   [ ] 取消页面翻译

-   [ ] 打开/关闭网页翻译(谷歌)的顶部栏

-   [ ] 与`Vimium C - All by Keyboard`插件在 PDF 阅读器中能够共同运行

## 版本信息

-   manifest.json，package.json，package-lock.json

-   git tag

-   更新日志
