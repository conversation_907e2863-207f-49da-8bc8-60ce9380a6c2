// OCR Worker for content script
import { createWorker } from 'tesseract.js';

let worker: any = null;

export async function initOCRWorker() {
  if (worker) {
    return worker;
  }

  try {
    console.log('🤖 Initializing Tesseract worker...');
    worker = await createWorker(['eng', 'chi_sim'], 1, {
      logger: m => {
        if (m.status === 'recognizing text') {
          console.log(`📖 OCR progress: ${Math.round(m.progress * 100)}%`);
        }
      }
    });
    console.log('✅ Tesseract worker initialized');
    return worker;
  } catch (error) {
    console.error('💥 Failed to initialize OCR worker:', error);
    throw error;
  }
}

export async function performOCR(imageData: string): Promise<{ text: string; confidence: number; language: string }> {
  try {
    const ocrWorker = await initOCRWorker();
    
    console.log('🔍 Starting OCR recognition...');
    const { data } = await ocrWorker.recognize(imageData);
    
    console.log('🔍 OCR result:', {
      text: data.text?.substring(0, 100) + '...',
      confidence: data.confidence,
      length: data.text?.length
    });

    if (data.text && data.text.trim()) {
      // 清理识别结果
      let cleanText = data.text
        .replace(/\n+/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();

      console.log('🧹 清理后文本长度:', cleanText.length);

      // 如果文本过长，截取前800字符（留一些余量）
      if (cleanText.length > 800) {
        cleanText = cleanText.substring(0, 800) + '...';
        console.log('✂️ 文本截取至800字符');
      }

      return {
        text: cleanText,
        confidence: data.confidence / 100,
        language: 'auto'
      };
    } else {
      return {
        text: '[OCR未识别到文字] 请确保选择区域包含清晰的文字内容。',
        confidence: 0.0,
        language: 'zh'
      };
    }
  } catch (error) {
    console.error('💥 OCR recognition failed:', error);
    return {
      text: `[OCR识别失败] ${error instanceof Error ? error.message : '未知错误'}`,
      confidence: 0.0,
      language: 'zh'
    };
  }
}

export async function terminateOCRWorker() {
  if (worker) {
    try {
      await worker.terminate();
      worker = null;
      console.log('🗑️ OCR worker terminated');
    } catch (error) {
      console.warn('Failed to terminate OCR worker:', error);
    }
  }
}
