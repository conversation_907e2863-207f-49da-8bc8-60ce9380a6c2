
    // go/mss-setup#7-load-the-js-or-css-from-your-initial-page
    if(!window['_DumpException']) {
      const _DumpException = window['_DumpException'] || function(e) {
        throw e;
      };
      window['_DumpException'] = _DumpException;
    }
  "use strict";this.default_tr=this.default_tr||{};(function(_){var window=this;
try{
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
/*

 SPDX-License-Identifier: Apache-2.0
*/
var ba,ea,xa,Aa,Ia,Ma,Na,Qa,Ra,Sa,Ta,Va,ab,bb,cb,db,v,eb,gb,hb,lb;_.aa=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,_.aa);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)};ba=function(a){_.t.setTimeout(function(){throw a;},0)};_.ca=function(a){a&&"function"==typeof a.T&&a.T()};ea=function(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];_.da(d)?ea.apply(null,d):_.ca(d)}};_.ka=function(){!_.fa&&_.ia&&_.ja();return _.fa};
_.ja=function(){_.fa=(0,_.ia)();la.forEach(function(a){a(_.fa)});la=[]};_.na=function(a){_.fa&&ma(a)};_.pa=function(){_.fa&&oa(_.fa)};_.ra=function(a,b){b.hasOwnProperty("displayName")||(b.displayName=a);b[qa]=a};_.ta=function(a,b){return 0<=(0,_.sa)(a,b)};_.ua=function(a,b){_.ta(a,b)||a.push(b)};_.va=function(a,b){b=(0,_.sa)(a,b);var c;(c=0<=b)&&Array.prototype.splice.call(a,b,1);return c};_.wa=function(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};
xa=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.da(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}};Aa=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],g=_.ya(f)?"o"+_.za(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,g)||(e[g]=!0,b[c++]=f)}b.length=c};_.Ba=function(){var a=_.t.navigator;return a&&(a=a.userAgent)?a:""};_.u=function(a){return-1!=_.Ba().indexOf(a)};
_.Ea=function(){return _.Ca?!!_.Da&&0<_.Da.brands.length:!1};_.Fa=function(){return _.Ea()?!1:_.u("Opera")};_.Ga=function(){return _.Ea()?!1:_.u("Trident")||_.u("MSIE")};_.Ha=function(){return _.Ea()?!1:_.u("Edge")};Ia=function(){return _.Ca?!!_.Da&&!!_.Da.platform:!1};_.Ja=function(){return _.u("iPhone")&&!_.u("iPod")&&!_.u("iPad")};_.Ka=function(){return _.Ja()||_.u("iPad")||_.u("iPod")};_.La=function(){return Ia()?"macOS"===_.Da.platform:_.u("Macintosh")};
Ma=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};Na=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.Oa=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};Qa=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Pa.length;f++)c=Pa[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};
Ra=function(a){var b=arguments.length;if(1==b&&Array.isArray(arguments[0]))return Ra.apply(null,arguments[0]);for(var c={},d=0;d<b;d++)c[arguments[d]]=!0;return c};Sa=function(){};Ta=function(a){return{valueOf:a}.valueOf()};Va=function(a){return new _.Ua(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})};
_.Xa=function(a,b){a.src=_.Wa(b);var c,d;(c=(b=null==(d=(c=(a.ownerDocument&&a.ownerDocument.defaultView||window).document).querySelector)?void 0:d.call(c,"script[nonce]"))?b.nonce||b.getAttribute("nonce")||"":"")&&a.setAttribute("nonce",c)};_.$a=function(a){a=_.Ya(a);return _.Za(a)};_.Ya=function(a){return null===a?"null":void 0===a?"undefined":a};ab=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};
bb="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};cb=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");};db=cb(this);
v=function(a,b){if(b)a:{var c=db;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&bb(c,a,{configurable:!0,writable:!0,value:b})}};
v("Symbol",function(a){if(a)return a;var b=function(f,g){this.g=f;bb(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("b");return new b(c+(f||"")+"_"+d++,f)};return e});
v("Symbol.iterator",function(a){if(a)return a;a=Symbol("c");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=db[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&bb(d.prototype,a,{configurable:!0,writable:!0,value:function(){return eb(ab(this))}})}return a});eb=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
_.x=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if("number"==typeof a.length)return{next:ab(a)};throw Error("d`"+String(a));};_.fb=function(a){if(!(a instanceof Array)){a=_.x(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a};gb="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b};
if("function"==typeof Object.setPrototypeOf)hb=Object.setPrototypeOf;else{var ib;a:{var jb={a:!0},kb={};try{kb.__proto__=jb;ib=kb.a;break a}catch(a){}ib=!1}hb=ib?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError("e`"+a);return a}:null}lb=hb;
_.y=function(a,b){a.prototype=gb(b.prototype);a.prototype.constructor=a;if(lb)lb(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.P=b.prototype};_.mb=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};
v("Promise",function(a){function b(){this.g=null}function c(g){return g instanceof e?g:new e(function(h){h(g)})}if(a)return a;b.prototype.h=function(g){if(null==this.g){this.g=[];var h=this;this.j(function(){h.o()})}this.g.push(g)};var d=db.setTimeout;b.prototype.j=function(g){d(g,0)};b.prototype.o=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var k=g[h];g[h]=null;try{k()}catch(m){this.l(m)}}}this.g=null};b.prototype.l=function(g){this.j(function(){throw g;
})};var e=function(g){this.g=0;this.j=void 0;this.h=[];this.A=!1;var h=this.l();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.l=function(){function g(m){return function(n){k||(k=!0,m.call(h,n))}}var h=this,k=!1;return{resolve:g(this.L),reject:g(this.o)}};e.prototype.L=function(g){if(g===this)this.o(new TypeError("h"));else if(g instanceof e)this.N(g);else{a:switch(typeof g){case "object":var h=null!=g;break a;case "function":h=!0;break a;default:h=!1}h?this.H(g):this.s(g)}};e.prototype.H=
function(g){var h=void 0;try{h=g.then}catch(k){this.o(k);return}"function"==typeof h?this.ha(h,g):this.s(g)};e.prototype.o=function(g){this.B(2,g)};e.prototype.s=function(g){this.B(1,g)};e.prototype.B=function(g,h){if(0!=this.g)throw Error("i`"+g+"`"+h+"`"+this.g);this.g=g;this.j=h;2===this.g&&this.G();this.F()};e.prototype.G=function(){var g=this;d(function(){if(g.D()){var h=db.console;"undefined"!==typeof h&&h.error(g.j)}},1)};e.prototype.D=function(){if(this.A)return!1;var g=db.CustomEvent,h=db.Event,
k=db.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):"function"===typeof h?g=new h("unhandledrejection",{cancelable:!0}):(g=db.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.j;return k(g)};e.prototype.F=function(){if(null!=this.h){for(var g=0;g<this.h.length;++g)f.h(this.h[g]);this.h=null}};var f=new b;e.prototype.N=function(g){var h=this.l();g.Kd(h.resolve,h.reject)};
e.prototype.ha=function(g,h){var k=this.l();try{g.call(h,k.resolve,k.reject)}catch(m){k.reject(m)}};e.prototype.then=function(g,h){function k(q,r){return"function"==typeof q?function(w){try{m(q(w))}catch(D){n(D)}}:r}var m,n,p=new e(function(q,r){m=q;n=r});this.Kd(k(g,m),k(h,n));return p};e.prototype.catch=function(g){return this.then(void 0,g)};e.prototype.Kd=function(g,h){function k(){switch(m.g){case 1:g(m.j);break;case 2:h(m.j);break;default:throw Error("j`"+m.g);}}var m=this;null==this.h?f.h(k):
this.h.push(k);this.A=!0};e.resolve=c;e.reject=function(g){return new e(function(h,k){k(g)})};e.race=function(g){return new e(function(h,k){for(var m=_.x(g),n=m.next();!n.done;n=m.next())c(n.value).Kd(h,k)})};e.all=function(g){var h=_.x(g),k=h.next();return k.done?c([]):new e(function(m,n){function p(w){return function(D){q[w]=D;r--;0==r&&m(q)}}var q=[],r=0;do q.push(void 0),r++,c(k.value).Kd(p(q.length-1),n),k=h.next();while(!k.done)})};return e});
var nb=function(a,b,c){if(null==a)throw new TypeError("k`"+c);if(b instanceof RegExp)throw new TypeError("l`"+c);return a+""};v("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=nb(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});var ob=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};
v("WeakMap",function(a){function b(){}function c(k){var m=typeof k;return"object"===m&&null!==k||"function"===m}function d(k){if(!ob(k,f)){var m=new b;bb(k,f,{value:m})}}function e(k){var m=Object[k];m&&(Object[k]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),m=Object.seal({}),n=new a([[k,2],[m,3]]);if(2!=n.get(k)||3!=n.get(m))return!1;n.delete(k);n.set(m,4);return!n.has(k)&&4==n.get(m)}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(k){this.g=(g+=Math.random()+1).toString();if(k){k=_.x(k);for(var m;!(m=k.next()).done;)m=m.value,this.set(m[0],m[1])}};h.prototype.set=function(k,m){if(!c(k))throw Error("m");d(k);if(!ob(k,f))throw Error("n`"+k);k[f][this.g]=m;return this};h.prototype.get=function(k){return c(k)&&ob(k,f)?k[f][this.g]:void 0};h.prototype.has=function(k){return c(k)&&ob(k,f)&&ob(k[f],this.g)};h.prototype.delete=function(k){return c(k)&&
ob(k,f)&&ob(k[f],this.g)?delete k[f][this.g]:!1};return h});
v("Map",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(_.x([[h,"s"]]));if("s"!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var m=k.entries(),n=m.next();if(n.done||n.value[0]!=h||"s"!=n.value[1])return!1;n=m.next();return n.done||4!=n.value[0].x||"t"!=n.value[1]||!m.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(h){this[0]={};this[1]=
f();this.size=0;if(h){h=_.x(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=0===h?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.Ma?m.Ma.value=k:(m.Ma={next:this[1],Lb:this[1].Lb,head:this[1],key:h,value:k},m.list.push(m.Ma),this[1].Lb.next=m.Ma,this[1].Lb=m.Ma,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.Ma&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.Ma.Lb.next=h.Ma.next,h.Ma.next.Lb=
h.Ma.Lb,h.Ma.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Lb=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).Ma};c.prototype.get=function(h){return(h=d(this,h).Ma)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var m=this.entries(),
n;!(n=m.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,k){var m=k&&typeof k;"object"==m||"function"==m?b.has(k)?m=b.get(k):(m=""+ ++g,b.set(k,m)):m="p_"+k;var n=h[0][m];if(n&&ob(h[0],m))for(h=0;h<n.length;h++){var p=n[h];if(k!==k&&p.key!==p.key||k===p.key)return{id:m,list:n,index:h,Ma:p}}return{id:m,list:n,index:-1,Ma:void 0}},e=function(h,k){var m=h[1];return eb(function(){if(m){for(;m.head!=h[1];)m=m.Lb;for(;m.next!=m.head;)return m=
m.next,{done:!1,value:k(m)};m=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.Lb=h.next=h.head=h},g=0;return c});var pb="function"==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)ob(d,e)&&(a[e]=d[e])}return a};v("Object.assign",function(a){return a||pb});
v("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});v("String.prototype.endsWith",function(a){return a?a:function(b,c){var d=nb(this,b,"endsWith");void 0===c&&(c=d.length);c=Math.max(0,Math.min(c|0,d.length));for(var e=b.length;0<e&&0<c;)if(d[--c]!=b[--e])return!1;return 0>=e}});
v("Number.isFinite",function(a){return a?a:function(b){return"number"!==typeof b?!1:!isNaN(b)&&Infinity!==b&&-Infinity!==b}});var qb=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};v("Array.prototype.entries",function(a){return a?a:function(){return qb(this,function(b,c){return[b,c]})}});
v("Array.prototype.keys",function(a){return a?a:function(){return qb(this,function(b){return b})}});v("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});
v("Array.prototype.values",function(a){return a?a:function(){return qb(this,function(b,c){return c})}});
v("Set",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var c=Object.seal({x:4}),d=new a(_.x([c]));if(!d.has(c)||1!=d.size||d.add(c)!=d||1!=d.size||d.add({x:4})!=d||2!=d.size)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||4!=f.value[0].x||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;var b=function(c){this.g=new Map;if(c){c=
_.x(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size};b.prototype.add=function(c){c=0===c?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return this.g.entries()};b.prototype.values=function(){return this.g.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b});v("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)ob(b,d)&&c.push(b[d]);return c}});v("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});
v("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});v("String.prototype.includes",function(a){return a?a:function(b,c){return-1!==nb(this,b,"includes").indexOf(b,c||0)}});v("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
v("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)ob(b,d)&&c.push([d,b[d]]);return c}});v("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("o");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});_._DumpException=window._DumpException||function(a){throw a;};window._DumpException=_._DumpException;var rb,sb,ub,tb,xb,yb,zb,Ab,Eb;rb=rb||{};_.t=this||self;sb=/^[a-zA-Z_$][a-zA-Z0-9._$]*$/;ub=function(a){if("string"!==typeof a||!a||-1==a.search(sb))throw Error("p");if(!tb||"goog"!=tb.type)throw Error("q`"+a);if(tb.ek)throw Error("r");tb.ek=a};ub.get=function(){return null};tb=null;_.vb=function(a,b){a=a.split(".");b=b||_.t;for(var c=0;c<a.length;c++)if(b=b[a[c]],null==b)return null;return b};_.wb=function(a){var b=typeof a;return"object"!=b?b:a?Array.isArray(a)?"array":b:"null"};
_.da=function(a){var b=_.wb(a);return"array"==b||"object"==b&&"number"==typeof a.length};_.ya=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b};_.za=function(a){return Object.prototype.hasOwnProperty.call(a,xb)&&a[xb]||(a[xb]=++yb)};xb="closure_uid_"+(1E9*Math.random()>>>0);yb=0;zb=function(a,b,c){return a.call.apply(a.bind,arguments)};
Ab=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}};_.z=function(a,b,c){_.z=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?zb:Ab;return _.z.apply(null,arguments)};
_.Bb=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}};_.Cb=function(){return Date.now()};_.Db=function(a,b){a=a.split(".");var c=_.t;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};
_.A=function(a,b){function c(){}c.prototype=b.prototype;a.P=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.lm=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};Eb=function(a){return a};_.A(_.aa,Error);_.aa.prototype.name="CustomError";var Fb;_.B=function(){this.Ca=this.Ca;this.ha=this.ha};_.B.prototype.Ca=!1;_.B.prototype.ob=function(){return this.Ca};_.B.prototype.T=function(){this.Ca||(this.Ca=!0,this.I())};_.B.prototype.I=function(){if(this.ha)for(;this.ha.length;)this.ha.shift()()};var Hb;_.Gb=function(){};Hb=function(a){return function(){throw Error(a);}};var Ib,Jb=function(){if(void 0===Ib){var a=null,b=_.t.trustedTypes;if(b&&b.createPolicy){try{a=b.createPolicy("goog#html",{createHTML:Eb,createScript:Eb,createScriptURL:Eb})}catch(c){_.t.console&&_.t.console.error(c.message)}Ib=a}else Ib=a}return Ib};var Lb,Kb;_.Mb=function(a,b){this.g=a===Kb&&b||"";this.h=Lb};_.Mb.prototype.nb=!0;_.Mb.prototype.Sa=function(){return this.g};_.Nb=function(a){return a instanceof _.Mb&&a.constructor===_.Mb&&a.h===Lb?a.g:"type_error:Const"};_.Ob=function(a){return new _.Mb(Kb,a)};Lb={};Kb={};var Pb={},Qb=function(a){this.g=a;this.nb=!0};Qb.prototype.toString=function(){return this.g.toString()};Qb.prototype.Sa=function(){return this.g.toString()};_.Rb=function(a){return a instanceof Qb&&a.constructor===Qb?a.g:"type_error:SafeScript"};_.Sb=function(a){var b=Jb();a=b?b.createScript(a):a;return new Qb(a,Pb)};var Vb;_.Tb=function(a){this.g=a};_.Tb.prototype.toString=function(){return this.g+""};_.Tb.prototype.nb=!0;_.Tb.prototype.Sa=function(){return this.g.toString()};_.Wa=function(a){return a instanceof _.Tb&&a.constructor===_.Tb?a.g:"type_error:TrustedResourceUrl"};_.Ub=RegExp("^((https:)?//[0-9a-z.:[\\]-]+/|/[^/\\\\]|[^:/\\\\%]+/|[^:/\\\\%]*[?#]|about:blank#)","i");Vb={};_.Za=function(a){var b=Jb();a=b?b.createScriptURL(a):a;return new _.Tb(a,Vb)};ub=ub||{};var Wb=function(){_.B.call(this)};_.A(Wb,_.B);Wb.prototype.initialize=function(){};var Xb=function(a,b){this.g=a;this.h=b};Xb.prototype.j=function(a){this.g&&(this.g.call(this.h||null,a),this.g=this.h=null)};Xb.prototype.abort=function(){this.h=this.g=null};var Yb=function(a,b){_.B.call(this);this.h=a;this.s=b;this.o=[];this.l=[];this.j=[]};_.A(Yb,_.B);Yb.prototype.A=Wb;Yb.prototype.g=null;Yb.prototype.jb=function(){return this.s};var Zb=function(a,b){a.l.push(new Xb(b))};Yb.prototype.onLoad=function(a){var b=new this.A;b.initialize(a());this.g=b;b=(b=!!$b(this.j,a()))||!!$b(this.o,a());b||(this.l.length=0);return b};Yb.prototype.Af=function(a){(a=$b(this.l,a))&&_.t.setTimeout(Hb("Module errback failures: "+a),0);this.j.length=0;this.o.length=0};
var $b=function(a,b){for(var c=[],d=0;d<a.length;d++)try{a[d].j(b)}catch(e){ba(e),c.push(e)}a.length=0;return c.length?c:null};Yb.prototype.I=function(){Yb.P.I.call(this);_.ca(this.g)};var ac=function(){this.B=this.Ca=null};_.l=ac.prototype;_.l.Kh=function(){};_.l.Of=function(){};_.l.Hh=function(){throw Error("v");};_.l.Ng=function(){return this.Ca};_.l.Pf=function(a){this.Ca=a};_.l.isActive=function(){return!1};_.l.jh=function(){return!1};_.l.Fh=function(){};var la;_.fa=null;_.ia=null;la=[];var C=function(a,b){this.h=a;this.g=b||null};C.prototype.toString=function(){return this.h};new C("ZtVrH");_.bc=new C("rJmJrc","rJmJrc");new C("fJuxOc");new C("NGntwf");new C("ofuapc");new C("BWETze");new C("ZmXAm");_.cc=new C("n73qwf","n73qwf");var qa=Symbol("x");var gc;_.sa=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.dc=Array.prototype.lastIndexOf?function(a,b){return Array.prototype.lastIndexOf.call(a,b,a.length-1)}:function(a,b){var c=a.length-1;0>c&&(c=Math.max(0,a.length+c));if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.lastIndexOf(b,c);for(;0<=c;c--)if(c in a&&a[c]===b)return c;return-1};_.ec=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.fc=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f="string"===typeof a?a.split(""):a,g=0;g<c;g++)if(g in f){var h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d};gc=Array.prototype.reduce?function(a,b,c){Array.prototype.reduce.call(a,b,c)}:function(a,b,c){var d=c;(0,_.ec)(a,function(e,f){d=b.call(void 0,d,e,f,a)})};
_.hc=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};var ic,jc;ic=_.vb("WIZ_global_data.oxN3nb");jc=ic&&ic[610401301];_.Ca=null!=jc?jc:!1;_.kc=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};var lc;lc=_.t.navigator;_.Da=lc?lc.userAgentData||null:null;var mc=function(a){mc[" "](a);return a};mc[" "]=function(){};_.nc=function(a,b){try{return mc(a[b]),!0}catch(c){}return!1};var Bc,Cc,Hc;_.oc=_.Fa();_.E=_.Ga();_.pc=_.u("Edge");_.qc=_.pc||_.E;_.rc=_.u("Gecko")&&!(-1!=_.Ba().toLowerCase().indexOf("webkit")&&!_.u("Edge"))&&!(_.u("Trident")||_.u("MSIE"))&&!_.u("Edge");_.sc=-1!=_.Ba().toLowerCase().indexOf("webkit")&&!_.u("Edge");_.tc=_.sc&&_.u("Mobile");_.uc=_.La();_.vc=Ia()?"Windows"===_.Da.platform:_.u("Windows");_.wc=Ia()?"Android"===_.Da.platform:_.u("Android");_.xc=_.Ja();_.yc=_.u("iPad");_.zc=_.u("iPod");_.Ac=_.Ka();
Bc=function(){var a=_.t.document;return a?a.documentMode:void 0};a:{var Dc="",Ec=function(){var a=_.Ba();if(_.rc)return/rv:([^\);]+)(\)|;)/.exec(a);if(_.pc)return/Edge\/([\d\.]+)/.exec(a);if(_.E)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.sc)return/WebKit\/(\S+)/.exec(a);if(_.oc)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Ec&&(Dc=Ec?Ec[1]:"");if(_.E){var Fc=Bc();if(null!=Fc&&Fc>parseFloat(Dc)){Cc=String(Fc);break a}}Cc=Dc}_.Gc=Cc;
if(_.t.document&&_.E){var Ic=Bc();Hc=Ic?Ic:parseInt(_.Gc,10)||void 0}else Hc=void 0;_.Jc=Hc;_.Kc=_.E||_.sc;var Pa;Pa="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");_.Lc=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};var Pc;_.Mc=function(a){this.g=a};_.Mc.prototype.toString=function(){return this.g.toString()};_.Mc.prototype.nb=!0;_.Mc.prototype.Sa=function(){return this.g.toString()};var Nc;try{new URL("s://g"),Nc=!0}catch(a){Nc=!1}_.Oc=Nc;Pc={};_.Qc=function(a){return new _.Mc(a,Pc)};_.Rc=_.Qc("about:invalid#zClosurez");_.Sc={};_.Tc=function(a){this.g=a;this.nb=!0};_.Tc.prototype.Sa=function(){return this.g};_.Tc.prototype.toString=function(){return this.g.toString()};_.Uc=new _.Tc("",_.Sc);_.Vc=RegExp("^[-+,.\"'%_!#/ a-zA-Z0-9\\[\\]]+$");_.Wc=RegExp("\\b(url\\([ \t\n]*)('[ -&(-\\[\\]-~]*'|\"[ !#-\\[\\]-~]*\"|[!#-&*-\\[\\]-~]*)([ \t\n]*\\))","g");
_.Xc=RegExp("\\b(calc|cubic-bezier|fit-content|hsl|hsla|linear-gradient|matrix|minmax|radial-gradient|repeat|rgb|rgba|(rotate|scale|translate)(X|Y|Z|3d)?|steps|var)\\([-+*/0-9a-zA-Z.%#\\[\\], ]+\\)","g");var Yc,cd;Yc={};_.Zc=function(a){this.g=a;this.nb=!0};_.Zc.prototype.Sa=function(){return this.g.toString()};_.Zc.prototype.toString=function(){return this.g.toString()};_.$c=function(a){return a instanceof _.Zc&&a.constructor===_.Zc?a.g:"type_error:SafeHtml"};_.ad=function(a){var b=Jb();a=b?b.createHTML(a):a;return new _.Zc(a,Yc)};_.bd=_.ad("<!DOCTYPE html>");cd=new _.Zc(_.t.trustedTypes&&_.t.trustedTypes.emptyHTML||"",Yc);_.dd=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){var a=document.createElement("div"),b=document.createElement("div");b.appendChild(document.createElement("div"));a.appendChild(b);b=a.firstChild.firstChild;a.innerHTML=_.$c(cd);return!b.parentElement});_.ed=function(a,b){this.width=a;this.height=b};_.fd=function(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1};_.l=_.ed.prototype;_.l.aspectRatio=function(){return this.width/this.height};_.l.Ib=function(){return!(this.width*this.height)};_.l.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};_.l.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
_.l.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.gd=function(a){return encodeURIComponent(String(a))};_.hd=function(a){return decodeURIComponent(a.replace(/\+/g," "))};_.id=function(){return Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^_.Cb()).toString(36)};var od,nd;_.ld=function(a){return a?new _.jd(_.kd(a)):Fb||(Fb=new _.jd)};_.md=function(a,b){return"string"===typeof b?a.getElementById(b):b};od=function(a,b){_.Lc(b,function(c,d){c&&"object"==typeof c&&c.nb&&(c=c.Sa());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:nd.hasOwnProperty(d)?a.setAttribute(nd[d],c):0==d.lastIndexOf("aria-",0)||0==d.lastIndexOf("data-",0)?a.setAttribute(d,c):a[d]=c})};
nd={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};_.qd=function(a){a=a.document;a=_.pd(a)?a.documentElement:a.body;return new _.ed(a.clientWidth,a.clientHeight)};_.rd=function(a){return a?a.parentWindow||a.defaultView:window};
_.ud=function(a,b){var c=b[1],d=_.sd(a,String(b[0]));c&&("string"===typeof c?d.className=c:Array.isArray(c)?d.className=c.join(" "):od(d,c));2<b.length&&_.td(a,d,b,2);return d};
_.td=function(a,b,c,d){function e(h){h&&b.appendChild("string"===typeof h?a.createTextNode(h):h)}for(;d<c.length;d++){var f=c[d];if(!_.da(f)||_.ya(f)&&0<f.nodeType)e(f);else{a:{if(f&&"number"==typeof f.length){if(_.ya(f)){var g="function"==typeof f.item||"string"==typeof f.item;break a}if("function"===typeof f){g="function"==typeof f.item;break a}}g=!1}_.ec(g?_.wa(f):f,e)}}};_.sd=function(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)};
_.pd=function(a){return"CSS1Compat"==a.compatMode};_.vd=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.wd=function(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.kd=function(a){return 9==a.nodeType?a:a.ownerDocument||a.document};
_.xd=function(a,b){if("textContent"in a)a.textContent=b;else if(3==a.nodeType)a.data=String(b);else if(a.firstChild&&3==a.firstChild.nodeType){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else _.vd(a),a.appendChild(_.kd(a).createTextNode(String(b)))};_.jd=function(a){this.g=a||_.t.document||document};_.jd.prototype.C=function(a){return _.md(this.g,a)};_.jd.prototype.U=function(a,b,c){return _.ud(this.g,arguments)};
_.yd=function(a){a=a.g;return a.parentWindow||a.defaultView};_.jd.prototype.appendChild=function(a,b){a.appendChild(b)};_.jd.prototype.Ff=_.vd;_.jd.prototype.h=_.wd;_.jd.prototype.Vc=_.xd;var zd=function(){this.id="b"};zd.prototype.toString=function(){return this.id};_.Ad=function(a,b){this.type=a instanceof zd?String(a):a;this.currentTarget=this.target=b;this.defaultPrevented=this.h=!1};_.Ad.prototype.stopPropagation=function(){this.h=!0};_.Ad.prototype.preventDefault=function(){this.defaultPrevented=!0};var Bd=function(){if(!_.t.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};_.t.addEventListener("test",c,b);_.t.removeEventListener("test",c,b)}catch(d){}return a}();_.Dd=function(a,b){_.Ad.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.j=!1;this.pointerId=0;this.pointerType="";this.g=null;if(a){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=
b;(b=a.relatedTarget)?_.rc&&(_.nc(b,"nodeName")||(b=null)):"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=_.sc||void 0!==a.offsetX?a.offsetX:a.layerX,this.offsetY=_.sc||void 0!==a.offsetY?a.offsetY:a.layerY,this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:
a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||("keypress"==c?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.j=_.uc?a.metaKey:a.ctrlKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:Cd[a.pointerType]||"";this.state=a.state;this.g=a;a.defaultPrevented&&_.Dd.P.preventDefault.call(this)}};
_.A(_.Dd,_.Ad);var Cd={2:"touch",3:"pen",4:"mouse"};_.Dd.prototype.stopPropagation=function(){_.Dd.P.stopPropagation.call(this);this.g.stopPropagation?this.g.stopPropagation():this.g.cancelBubble=!0};_.Dd.prototype.preventDefault=function(){_.Dd.P.preventDefault.call(this);var a=this.g;a.preventDefault?a.preventDefault():a.returnValue=!1};var Ed;Ed="closure_listenable_"+(1E6*Math.random()|0);_.Fd=function(a){return!(!a||!a[Ed])};var Gd=0;var Hd=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.be=e;this.key=++Gd;this.vd=this.Jd=!1},Id=function(a){a.vd=!0;a.listener=null;a.proxy=null;a.src=null;a.be=null};var Jd=function(a){this.src=a;this.g={};this.h=0},Ld;Jd.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var g=Kd(a,b,d,e);-1<g?(b=a[g],c||(b.Jd=!1)):(b=new Hd(b,this.src,f,!!d,e),b.Jd=c,a.push(b));return b};Jd.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.g))return!1;var e=this.g[a];b=Kd(e,b,c,d);return-1<b?(Id(e[b]),Array.prototype.splice.call(e,b,1),0==e.length&&(delete this.g[a],this.h--),!0):!1};
Ld=function(a,b){var c=b.type;if(!(c in a.g))return!1;var d=_.va(a.g[c],b);d&&(Id(b),0==a.g[c].length&&(delete a.g[c],a.h--));return d};_.Md=function(a){var b=0,c;for(c in a.g){for(var d=a.g[c],e=0;e<d.length;e++)++b,Id(d[e]);delete a.g[c];a.h--}};Jd.prototype.ld=function(a,b,c,d){a=this.g[a.toString()];var e=-1;a&&(e=Kd(a,b,c,d));return-1<e?a[e]:null};
Jd.prototype.hasListener=function(a,b){var c=void 0!==a,d=c?a.toString():"",e=void 0!==b;return Ma(this.g,function(f){for(var g=0;g<f.length;++g)if(!(c&&f[g].type!=d||e&&f[g].capture!=b))return!0;return!1})};var Kd=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.vd&&f.listener==b&&f.capture==!!c&&f.be==d)return e}return-1};var Nd,Od,Pd,Sd,Ud,Vd,Wd,Zd,Rd;Nd="closure_lm_"+(1E6*Math.random()|0);Od={};Pd=0;_.F=function(a,b,c,d,e){if(d&&d.once)return _.Qd(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.F(a,b[f],c,d,e);return null}c=Rd(c);return _.Fd(a)?a.J(b,c,_.ya(d)?!!d.capture:!!d,e):Sd(a,b,c,!1,d,e)};
Sd=function(a,b,c,d,e,f){if(!b)throw Error("C");var g=_.ya(e)?!!e.capture:!!e,h=_.Td(a);h||(a[Nd]=h=new Jd(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=Ud();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)Bd||(e=g),void 0===e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Vd(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("D");Pd++;return c};Ud=function(){var a=Wd,b=function(c){return a.call(b.src,b.listener,c)};return b};
_.Qd=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Qd(a,b[f],c,d,e);return null}c=Rd(c);return _.Fd(a)?a.Xb(b,c,_.ya(d)?!!d.capture:!!d,e):Sd(a,b,c,!0,d,e)};_.Xd=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)_.Xd(a,b[f],c,d,e);else d=_.ya(d)?!!d.capture:!!d,c=Rd(c),_.Fd(a)?a.qb(b,c,d,e):a&&(a=_.Td(a))&&(b=a.ld(b,c,d,e))&&_.Yd(b)};
_.Yd=function(a){if("number"===typeof a||!a||a.vd)return!1;var b=a.src;if(_.Fd(b))return Ld(b.ab,a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Vd(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Pd--;(c=_.Td(b))?(Ld(c,a),0==c.h&&(c.src=null,b[Nd]=null)):Id(a);return!0};Vd=function(a){return a in Od?Od[a]:Od[a]="on"+a};
Wd=function(a,b){if(a.vd)a=!0;else{b=new _.Dd(b,this);var c=a.listener,d=a.be||a.src;a.Jd&&_.Yd(a);a=c.call(d,b)}return a};_.Td=function(a){a=a[Nd];return a instanceof Jd?a:null};Zd="__closure_events_fn_"+(1E9*Math.random()>>>0);Rd=function(a){if("function"===typeof a)return a;a[Zd]||(a[Zd]=function(b){return a.handleEvent(b)});return a[Zd]};_.G=function(){_.B.call(this);this.ab=new Jd(this);this.zi=this;this.se=null};_.A(_.G,_.B);_.G.prototype[Ed]=!0;_.l=_.G.prototype;_.l.ve=function(a){this.se=a};_.l.addEventListener=function(a,b,c,d){_.F(this,a,b,c,d)};_.l.removeEventListener=function(a,b,c,d){_.Xd(this,a,b,c,d)};
_.l.dispatchEvent=function(a){var b,c=this.se;if(c)for(b=[];c;c=c.se)b.push(c);c=this.zi;var d=a.type||a;if("string"===typeof a)a=new _.Ad(a,c);else if(a instanceof _.Ad)a.target=a.target||c;else{var e=a;a=new _.Ad(d,c);Qa(a,e)}e=!0;if(b)for(var f=b.length-1;!a.h&&0<=f;f--){var g=a.currentTarget=b[f];e=$d(g,d,!0,a)&&e}a.h||(g=a.currentTarget=c,e=$d(g,d,!0,a)&&e,a.h||(e=$d(g,d,!1,a)&&e));if(b)for(f=0;!a.h&&f<b.length;f++)g=a.currentTarget=b[f],e=$d(g,d,!1,a)&&e;return e};
_.l.I=function(){_.G.P.I.call(this);this.ab&&_.Md(this.ab);this.se=null};_.l.J=function(a,b,c,d){return this.ab.add(String(a),b,!1,c,d)};_.l.Xb=function(a,b,c,d){return this.ab.add(String(a),b,!0,c,d)};_.l.qb=function(a,b,c,d){return this.ab.remove(String(a),b,c,d)};var $d=function(a,b,c,d){b=a.ab.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.vd&&g.capture==c){var h=g.listener,k=g.be||g.src;g.Jd&&Ld(a.ab,g);e=!1!==h.call(k,d)&&e}}return e&&!d.defaultPrevented};
_.G.prototype.ld=function(a,b,c,d){return this.ab.ld(String(a),b,c,d)};_.G.prototype.hasListener=function(a,b){return this.ab.hasListener(void 0!==a?String(a):void 0,b)};var ae=function(a){_.G.call(this);this.g=a||window;this.h=_.F(this.g,"resize",this.l,!1,this);this.j=_.qd(this.g||window)};_.A(ae,_.G);ae.prototype.I=function(){ae.P.I.call(this);this.h&&(_.Yd(this.h),this.h=null);this.j=this.g=null};ae.prototype.l=function(){var a=_.qd(this.g||window);_.fd(a,this.j)||(this.j=a,this.dispatchEvent("resize"))};var be=function(a){_.G.call(this);this.j=a?_.yd(a):window;this.o=1.5<=this.j.devicePixelRatio?2:1;this.h=(0,_.z)(this.s,this);this.l=null;(this.g=this.j.matchMedia?this.j.matchMedia("(min-resolution: 1.5dppx), (-webkit-min-device-pixel-ratio: 1.5)"):null)&&"function"!==typeof this.g.addListener&&"function"!==typeof this.g.addEventListener&&(this.g=null)};_.A(be,_.G);
be.prototype.start=function(){var a=this;this.g&&("function"===typeof this.g.addEventListener?(this.g.addEventListener("change",this.h),this.l=function(){a.g.removeEventListener("change",a.h)}):(this.g.addListener(this.h),this.l=function(){a.g.removeListener(a.h)}))};be.prototype.s=function(){var a=1.5<=this.j.devicePixelRatio?2:1;this.o!=a&&(this.o=a,this.dispatchEvent("a"))};be.prototype.I=function(){this.l&&this.l();be.P.I.call(this)};var de=function(a,b){_.B.call(this);this.o=a;if(b){if(this.l)throw Error("E");this.l=b;this.h=_.ld(b);this.g=new ae(_.rd(b));this.g.ve(this.o.h());this.j=new be(this.h);this.j.start()}};_.A(de,_.B);de.prototype.I=function(){this.h=this.l=null;this.g&&(this.g.T(),this.g=null);_.ca(this.j);this.j=null};_.ra(_.cc,de);var ee=function(a,b){this.l=a;this.j=b;this.h=0;this.g=null};ee.prototype.get=function(){if(0<this.h){this.h--;var a=this.g;this.g=a.next;a.next=null}else a=this.l();return a};var fe=function(a,b){a.j(b);100>a.h&&(a.h++,b.next=a.g,a.g=b)};var ge,he=function(){var a=_.t.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!_.u("Presto")&&(a=function(){var e=_.sd(document,"IFRAME");e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var g="callImmediate"+Math.random(),h="file:"==f.location.protocol?"*":f.location.protocol+"//"+f.location.host;e=(0,_.z)(function(k){if(("*"==h||k.origin==h)&&k.data==g)this.port1.onmessage()},
this);f.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(g,h)}}});if("undefined"!==typeof a&&!_.Ga()){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.zg;c.zg=null;e()}};return function(e){d.next={zg:e};d=d.next;b.port2.postMessage(0)}}return function(e){_.t.setTimeout(e,0)}};var ie=function(){this.h=this.g=null};ie.prototype.add=function(a,b){var c=je.get();c.set(a,b);this.h?this.h.next=c:this.g=c;this.h=c};ie.prototype.remove=function(){var a=null;this.g&&(a=this.g,this.g=this.g.next,this.g||(this.h=null),a.next=null);return a};var je=new ee(function(){return new ke},function(a){return a.reset()}),ke=function(){this.next=this.g=this.h=null};ke.prototype.set=function(a,b){this.h=a;this.g=b;this.next=null};ke.prototype.reset=function(){this.next=this.g=this.h=null};var le,me=!1,ne=new ie,pe=function(a,b){le||oe();me||(le(),me=!0);ne.add(a,b)},oe=function(){if(_.t.Promise&&_.t.Promise.resolve){var a=_.t.Promise.resolve(void 0);le=function(){a.then(qe)}}else le=function(){var b=qe;"function"!==typeof _.t.setImmediate||_.t.Window&&_.t.Window.prototype&&!_.Ha()&&_.t.Window.prototype.setImmediate==_.t.setImmediate?(ge||(ge=he()),ge(b)):_.t.setImmediate(b)}},qe=function(){for(var a;a=ne.remove();){try{a.h.call(a.g)}catch(b){ba(b)}fe(je,a)}me=!1};var re=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};var ue,Ee,Ce,Ae;_.te=function(a){this.g=0;this.A=void 0;this.l=this.h=this.j=null;this.o=this.s=!1;if(a!=_.Gb)try{var b=this;a.call(void 0,function(c){_.se(b,2,c)},function(c){_.se(b,3,c)})}catch(c){_.se(this,3,c)}};ue=function(){this.next=this.j=this.h=this.l=this.g=null;this.o=!1};ue.prototype.reset=function(){this.j=this.h=this.l=this.g=null;this.o=!1};var ve=new ee(function(){return new ue},function(a){a.reset()}),we=function(a,b,c){var d=ve.get();d.l=a;d.h=b;d.j=c;return d};
_.te.prototype.then=function(a,b,c){return xe(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};_.te.prototype.$goog_Thenable=!0;_.te.prototype.B=function(a,b){return xe(this,null,a,b)};_.te.prototype.catch=_.te.prototype.B;_.te.prototype.cancel=function(a){if(0==this.g){var b=new ye(a);pe(function(){ze(this,b)},this)}};
var ze=function(a,b){if(0==a.g)if(a.j){var c=a.j;if(c.h){for(var d=0,e=null,f=null,g=c.h;g&&(g.o||(d++,g.g==a&&(e=g),!(e&&1<d)));g=g.next)e||(f=g);e&&(0==c.g&&1==d?ze(c,b):(f?(d=f,d.next==c.l&&(c.l=d),d.next=d.next.next):Ae(c),Be(c,e,3,b)))}a.j=null}else _.se(a,3,b)},De=function(a,b){a.h||2!=a.g&&3!=a.g||Ce(a);a.l?a.l.next=b:a.h=b;a.l=b},xe=function(a,b,c,d){var e=we(null,null,null);e.g=new _.te(function(f,g){e.l=b?function(h){try{var k=b.call(d,h);f(k)}catch(m){g(m)}}:f;e.h=c?function(h){try{var k=
c.call(d,h);void 0===k&&h instanceof ye?g(h):f(k)}catch(m){g(m)}}:g});e.g.j=a;De(a,e);return e.g};_.te.prototype.D=function(a){this.g=0;_.se(this,2,a)};_.te.prototype.H=function(a){this.g=0;_.se(this,3,a)};
_.se=function(a,b,c){if(0==a.g){a===c&&(b=3,c=new TypeError("F"));a.g=1;a:{var d=c,e=a.D,f=a.H;if(d instanceof _.te){De(d,we(e||_.Gb,f||null,a));var g=!0}else if(re(d))d.then(e,f,a),g=!0;else{if(_.ya(d))try{var h=d.then;if("function"===typeof h){Ee(d,h,e,f,a);g=!0;break a}}catch(k){f.call(a,k);g=!0;break a}g=!1}}g||(a.A=c,a.g=b,a.j=null,Ce(a),3!=b||c instanceof ye||Fe(a,c))}};Ee=function(a,b,c,d,e){var f=!1,g=function(k){f||(f=!0,c.call(e,k))},h=function(k){f||(f=!0,d.call(e,k))};try{b.call(a,g,h)}catch(k){h(k)}};
Ce=function(a){a.s||(a.s=!0,pe(a.F,a))};Ae=function(a){var b=null;a.h&&(b=a.h,a.h=b.next,b.next=null);a.h||(a.l=null);return b};_.te.prototype.F=function(){for(var a;a=Ae(this);)Be(this,a,this.g,this.A);this.s=!1};
var Be=function(a,b,c,d){if(3==c&&b.h&&!b.o)for(;a&&a.o;a=a.j)a.o=!1;if(b.g)b.g.j=null,Ge(b,c,d);else try{b.o?b.l.call(b.j):Ge(b,c,d)}catch(e){He.call(null,e)}fe(ve,b)},Ge=function(a,b,c){2==b?a.l.call(a.j,c):a.h&&a.h.call(a.j,c)},Fe=function(a,b){a.o=!0;pe(function(){a.o&&He.call(null,b)})},He=ba,ye=function(a){_.aa.call(this,a)};_.A(ye,_.aa);ye.prototype.name="cancel";/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
var Ie=function(a,b){this.s=[];this.G=a;this.L=b||null;this.l=this.g=!1;this.j=void 0;this.D=this.N=this.B=!1;this.A=0;this.h=null;this.o=0};_.A(Ie,Sa);Ie.prototype.cancel=function(a){if(this.g)this.j instanceof Ie&&this.j.cancel();else{if(this.h){var b=this.h;delete this.h;a?b.cancel(a):(b.o--,0>=b.o&&b.cancel())}this.G?this.G.call(this.L,this):this.D=!0;this.g||this.F(new _.Je(this))}};Ie.prototype.H=function(a,b){this.B=!1;Ke(this,a,b)};
var Ke=function(a,b,c){a.g=!0;a.j=c;a.l=!b;Le(a)},Ne=function(a){if(a.g){if(!a.D)throw new Me(a);a.D=!1}};Ie.prototype.callback=function(a){Ne(this);Ke(this,!0,a)};Ie.prototype.F=function(a){Ne(this);Ke(this,!1,a)};var Pe=function(a,b,c){Oe(a,b,null,c)},Qe=function(a,b,c){Oe(a,null,b,c)},Oe=function(a,b,c,d){a.s.push([b,c,d]);a.g&&Le(a)};
Ie.prototype.then=function(a,b,c){var d,e,f=new _.te(function(g,h){e=g;d=h});Oe(this,e,function(g){g instanceof _.Je?f.cancel():d(g);return Re},this);return f.then(a,b,c)};Ie.prototype.$goog_Thenable=!0;var Se=function(a,b){b instanceof Ie?Pe(a,(0,_.z)(b.ha,b)):Pe(a,function(){return b})};Ie.prototype.ha=function(a){var b=new Ie;Oe(this,b.callback,b.F,b);a&&(b.h=this,this.o++);return b};
var Te=function(a){return _.hc(a.s,function(b){return"function"===typeof b[1]})},Re={},Le=function(a){if(a.A&&a.g&&Te(a)){var b=a.A,c=Ue[b];c&&(_.t.clearTimeout(c.g),delete Ue[b]);a.A=0}a.h&&(a.h.o--,delete a.h);b=a.j;for(var d=c=!1;a.s.length&&!a.B;){var e=a.s.shift(),f=e[0],g=e[1];e=e[2];if(f=a.l?g:f)try{var h=f.call(e||a.L,b);h===Re&&(h=void 0);void 0!==h&&(a.l=a.l&&(h==b||h instanceof Error),a.j=b=h);if(re(b)||"function"===typeof _.t.Promise&&b instanceof _.t.Promise)d=!0,a.B=!0}catch(k){b=k,
a.l=!0,Te(a)||(c=!0)}}a.j=b;d&&(h=(0,_.z)(a.H,a,!0),d=(0,_.z)(a.H,a,!1),b instanceof Ie?(Oe(b,h,d),b.N=!0):b.then(h,d));c&&(b=new Ve(b),Ue[b.g]=b,a.A=b.g)},Me=function(){_.aa.call(this)};_.A(Me,_.aa);Me.prototype.message="Deferred has already fired";Me.prototype.name="AlreadyCalledError";_.Je=function(){_.aa.call(this)};_.A(_.Je,_.aa);_.Je.prototype.message="Deferred was canceled";_.Je.prototype.name="CanceledError";var Ve=function(a){this.g=_.t.setTimeout((0,_.z)(this.j,this),0);this.h=a};
Ve.prototype.j=function(){delete Ue[this.g];throw this.h;};var Ue={};var We=function(a,b){this.type=a;this.status=b};We.prototype.toString=function(){return Xe(this)+" ("+(void 0!=this.status?this.status:"?")+")"};var Xe=function(a){switch(a.type){case We.g.rg:return"Unauthorized";case We.g.fg:return"Consecutive load failures";case We.g.TIMEOUT:return"Timed out";case We.g.pg:return"Out of date module id";case We.g.Be:return"Init error";default:return"Unknown failure type "+a.type}};ub.Wa=We;ub.Wa.g={rg:0,fg:1,TIMEOUT:2,pg:3,Be:4};var Ye=function(){ac.call(this);this.g={};this.l=[];this.o=[];this.L=[];this.h=[];this.A=[];this.s={};this.N={};this.j=this.F=new Yb([],"");this.ha=null;this.H=new Ie;this.G=!1;this.D=0;this.S=this.V=this.Y=!1};_.A(Ye,ac);var Ze=function(a,b){_.aa.call(this,"Error loading "+a+": "+b)};_.A(Ze,_.aa);_.l=Ye.prototype;_.l.Kh=function(a){this.G=a};
_.l.Of=function(a,b){if(!(this instanceof Ye))this.Of(a,b);else if("string"===typeof a){a=a.split("/");for(var c=[],d=0;d<a.length;d++){var e=a[d].split(":"),f=e[0];if(e[1]){e=e[1].split(",");for(var g=0;g<e.length;g++)e[g]=c[parseInt(e[g],36)]}else e=[];c.push(f);this.g[f]?(f=this.g[f].h,f!=e&&f.splice.apply(f,[0,f.length].concat(_.fb(e)))):this.g[f]=new Yb(e,f)}b&&b.length?(xa(this.l,b),this.ha=b[b.length-1]):this.H.g||this.H.callback();$e(this)}};
_.l.Hh=function(a,b){if(this.s[a]){delete this.s[a][b];for(var c in this.s[a])return;delete this.s[a]}};_.l.Pf=function(a){Ye.P.Pf.call(this,a);$e(this)};_.l.isActive=function(){return 0<this.l.length};_.l.jh=function(){return 0<this.A.length};
var bf=function(a){var b=a.Y,c=a.isActive();c!=b&&(af(a,c?"active":"idle"),a.Y=c);b=a.jh();b!=a.V&&(af(a,b?"userActive":"userIdle"),a.V=b)},ef=function(a,b,c){var d=[];Aa(b,d);b=[];for(var e={},f=0;f<d.length;f++){var g=d[f],h=a.g[g];if(!h)throw Error("G`"+g);var k=new Ie;e[g]=k;h.g?k.callback(a.Ca):(cf(a,g,h,!!c,k),df(a,g)||b.push(g))}0<b.length&&(0===a.l.length?a.M(b):(a.h.push(b),bf(a)));return e},cf=function(a,b,c,d,e){c.o.push(new Xb(e.callback,e));Zb(c,function(f){e.F(new Ze(b,f))});df(a,b)?
d&&(_.ta(a.A,b)||a.A.push(b),bf(a)):d&&(_.ta(a.A,b)||a.A.push(b))};
Ye.prototype.M=function(a,b,c){var d=this;b||(this.D=0);var e=ff(this,a);this.l=e;this.o=this.G?a:_.wa(e);bf(this);if(0!==e.length){this.L.push.apply(this.L,e);if(0<Object.keys(this.s).length&&!this.B.G)throw Error("H");a=(0,_.z)(this.B.H,this.B,_.wa(e),this.g,{Pi:this.s,Ti:!!c,Af:function(f){var g=d.o;f=null!=f?f:void 0;d.D++;d.o=g;e.forEach(_.Bb(_.va,d.L),d);401==f?(gf(d,new ub.Wa(ub.Wa.g.rg,f)),d.h.length=0):410==f?(hf(d,new ub.Wa(ub.Wa.g.pg,f)),jf(d)):3<=d.D?(hf(d,new ub.Wa(ub.Wa.g.fg,f)),jf(d)):
d.M(d.o,!0,8001==f)},qk:(0,_.z)(this.ba,this)});(b=5E3*Math.pow(this.D,2))?_.t.setTimeout(a,b):a()}};
var ff=function(a,b){b=b.filter(function(e){return a.g[e].g?(_.t.setTimeout(function(){return Error("I`"+e)},0),!1):!0});for(var c=[],d=0;d<b.length;d++)c=c.concat(kf(a,b[d]));Aa(c);return!a.G&&1<c.length?(b=c.shift(),a.h=c.map(function(e){return[e]}).concat(a.h),[b]):c},kf=function(a,b){var c=Ra(a.L),d=[];c[b]||d.push(b);b=[b];for(var e=0;e<b.length;e++)for(var f=a.g[b[e]].h,g=f.length-1;0<=g;g--){var h=f[g];a.g[h].g||c[h]||(d.push(h),b.push(h))}d.reverse();Aa(d);return d},$e=function(a){a.j==a.F&&
(a.j=null,a.F.onLoad((0,_.z)(a.Ng,a))&&gf(a,new ub.Wa(ub.Wa.g.Be)),bf(a))},oa=function(a){if(a.j){var b=a.j.jb(),c=[];if(a.s[b]){for(var d=_.x(Object.keys(a.s[b])),e=d.next();!e.done;e=d.next()){e=e.value;var f=a.g[e];f&&!f.g&&(a.Hh(b,e),c.push(e))}ef(a,c)}a.ob()||(a.g[b].onLoad((0,_.z)(a.Ng,a))&&gf(a,new ub.Wa(ub.Wa.g.Be)),_.va(a.A,b),_.va(a.l,b),0===a.l.length&&jf(a),a.ha&&b==a.ha&&(a.H.g||a.H.callback()),bf(a),a.j=null)}},df=function(a,b){if(_.ta(a.l,b))return!0;for(var c=0;c<a.h.length;c++)if(_.ta(a.h[c],
b))return!0;return!1};Ye.prototype.load=function(a,b){return ef(this,[a],b)[a]};var ma=function(a){var b=_.fa;b.j&&"synthetic_module_overhead"===b.j.jb()&&(oa(b),delete b.g.synthetic_module_overhead);b.g[a]&&lf(b,b.g[a].h||[],function(c){c.g=new Wb;_.va(b.l,c.jb())},function(c){return!c.g});b.j=b.g[a]};Ye.prototype.Fh=function(a){this.j||(this.g.synthetic_module_overhead=new Yb([],"synthetic_module_overhead"),this.j=this.g.synthetic_module_overhead);this.j.j.push(new Xb(a))};
Ye.prototype.ba=function(){hf(this,new ub.Wa(ub.Wa.g.TIMEOUT));jf(this)};
var hf=function(a,b){1<a.o.length?a.h=a.o.map(function(c){return[c]}).concat(a.h):gf(a,b)},gf=function(a,b){var c=a.o;a.l.length=0;for(var d=[],e=0;e<a.h.length;e++){var f=a.h[e].filter(function(k){var m=kf(this,k);return _.hc(c,function(n){return _.ta(m,n)})},a);xa(d,f)}for(e=0;e<c.length;e++)_.ua(d,c[e]);for(e=0;e<d.length;e++){for(f=0;f<a.h.length;f++)_.va(a.h[f],d[e]);_.va(a.A,d[e])}var g=a.N.error;if(g)for(e=0;e<g.length;e++){var h=g[e];for(f=0;f<d.length;f++)h("error",d[f],b)}for(e=0;e<c.length;e++)a.g[c[e]]&&
a.g[c[e]].Af(b);a.o.length=0;bf(a)},jf=function(a){for(;a.h.length;){var b=a.h.shift().filter(function(c){return!this.g[c].g},a);if(0<b.length){a.M(b);return}}bf(a)},af=function(a,b){a=a.N[b];for(var c=0;a&&c<a.length;c++)a[c](b)},lf=function(a,b,c,d,e){d=void 0===d?function(){return!0}:d;e=void 0===e?{}:e;b=_.x(b);for(var f=b.next();!f.done;f=b.next()){f=f.value;var g=a.g[f];!e[f]&&d(g)&&(e[f]=!0,lf(a,g.h||[],c,d,e),c(g))}};
Ye.prototype.T=function(){ea(Na(this.g),this.F);this.g={};this.l=[];this.o=[];this.A=[];this.h=[];this.N={};this.S=!0};Ye.prototype.ob=function(){return this.S};_.ia=function(){return new Ye};var mf=function(a,b){this.g=a[_.t.Symbol.iterator]();this.h=b};mf.prototype[Symbol.iterator]=function(){return this};mf.prototype.next=function(){var a=this.g.next();return{value:a.done?void 0:this.h.call(void 0,a.value),done:a.done}};var nf=function(a,b){return new mf(a,b)};_.of=function(){};_.of.prototype.next=function(){return _.pf};_.pf={done:!0,value:void 0};_.of.prototype.Sb=function(){return this};var tf=function(a){if(a instanceof qf||a instanceof rf||a instanceof sf)return a;if("function"==typeof a.next)return new qf(function(){return a});if("function"==typeof a[Symbol.iterator])return new qf(function(){return a[Symbol.iterator]()});if("function"==typeof a.Sb)return new qf(function(){return a.Sb()});throw Error("J");},qf=function(a){this.g=a};qf.prototype.Sb=function(){return new rf(this.g())};qf.prototype[Symbol.iterator]=function(){return new sf(this.g())};qf.prototype.h=function(){return new sf(this.g())};
var rf=function(a){this.g=a};_.y(rf,_.of);rf.prototype.next=function(){return this.g.next()};rf.prototype[Symbol.iterator]=function(){return new sf(this.g)};rf.prototype.h=function(){return new sf(this.g)};var sf=function(a){qf.call(this,function(){return a});this.j=a};_.y(sf,qf);sf.prototype.next=function(){return this.j.next()};var vf;_.uf=function(a,b){this.h={};this.g=[];this.j=this.size=0;var c=arguments.length;if(1<c){if(c%2)throw Error("z");for(var d=0;d<c;d+=2)this.set(arguments[d],arguments[d+1])}else if(a)if(a instanceof _.uf)for(c=a.mc(),d=0;d<c.length;d++)this.set(c[d],a.get(c[d]));else for(d in a)this.set(d,a[d])};_.uf.prototype.Ya=function(){vf(this);for(var a=[],b=0;b<this.g.length;b++)a.push(this.h[this.g[b]]);return a};_.uf.prototype.mc=function(){vf(this);return this.g.concat()};_.wf=function(a,b){return a.has(b)};
_.uf.prototype.has=function(a){return xf(this.h,a)};_.uf.prototype.Ib=function(){return 0==this.size};_.uf.prototype.remove=function(a){return _.yf(this,a)};_.yf=function(a,b){return xf(a.h,b)?(delete a.h[b],--a.size,a.j++,a.g.length>2*a.size&&vf(a),!0):!1};vf=function(a){if(a.size!=a.g.length){for(var b=0,c=0;b<a.g.length;){var d=a.g[b];xf(a.h,d)&&(a.g[c++]=d);b++}a.g.length=c}if(a.size!=a.g.length){var e={};for(c=b=0;b<a.g.length;)d=a.g[b],xf(e,d)||(a.g[c++]=d,e[d]=1),b++;a.g.length=c}};_.l=_.uf.prototype;
_.l.get=function(a,b){return xf(this.h,a)?this.h[a]:b};_.l.set=function(a,b){xf(this.h,a)||(this.size+=1,this.g.push(a),this.j++);this.h[a]=b};_.l.forEach=function(a,b){for(var c=this.mc(),d=0;d<c.length;d++){var e=c[d],f=this.get(e);a.call(b,f,e,this)}};_.l.keys=function(){return tf(this.Sb(!0)).h()};_.l.values=function(){return tf(this.Sb(!1)).h()};_.l.entries=function(){var a=this;return nf(this.keys(),function(b){return[b,a.get(b)]})};
_.l.Sb=function(a){vf(this);var b=0,c=this.j,d=this,e=new _.of;e.next=function(){if(c!=d.j)throw Error("K");if(b>=d.g.length)return _.pf;var f=d.g[b++];return{value:a?f:d.h[f],done:!1}};return e};var xf=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.zf=function(a){if(a.Ya&&"function"==typeof a.Ya)return a.Ya();if("undefined"!==typeof Map&&a instanceof Map||"undefined"!==typeof Set&&a instanceof Set)return Array.from(a.values());if("string"===typeof a)return a.split("");if(_.da(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}return Na(a)};
_.Af=function(a){if(a.mc&&"function"==typeof a.mc)return a.mc();if(!a.Ya||"function"!=typeof a.Ya){if("undefined"!==typeof Map&&a instanceof Map)return Array.from(a.keys());if(!("undefined"!==typeof Set&&a instanceof Set)){if(_.da(a)||"string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}return _.Oa(a)}}};
_.Bf=function(a,b,c){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,c);else if(_.da(a)||"string"===typeof a)Array.prototype.forEach.call(a,b,c);else for(var d=_.Af(a),e=_.zf(a),f=e.length,g=0;g<f;g++)b.call(c,e[g],d&&d[g],a)};_.Cf=function(){this.g=new _.uf;this.size=0};_.Df=function(a){var b=typeof a;return"object"==b&&a||"function"==b?"o"+_.za(a):b.charAt(0)+a};_.l=_.Cf.prototype;_.l.add=function(a){this.g.set(_.Df(a),a);this.size=this.g.size};_.l.remove=function(a){a=this.g.remove(_.Df(a));this.size=this.g.size;return a};_.l.Ib=function(){return 0===this.g.size};_.l.has=function(a){return _.wf(this.g,_.Df(a))};_.l.Ya=function(){return this.g.Ya()};_.l.values=function(){return this.g.values()};_.l.Sb=function(){return this.g.Sb(!1)};
_.Cf.prototype[Symbol.iterator]=function(){return this.values()};var Ef=[],Ff=function(a){function b(d){d&&gc(d,function(e,f){e[f.id]=!0;return e},c.wk)}var c={wk:{},index:Ef.length,ln:a};b(a.g);b(a.j);Ef.push(c);a.g&&_.ec(a.g,function(d){var e=d.id;e instanceof C&&d.module&&(e.g=d.module)})};new C("Bgf0ib");var Gf=new C("MpJwZc","MpJwZc");_.Hf=new C("UUJqVe","UUJqVe");_.If=new C("GHAeAc","GHAeAc");_.Jf=new C("Wt6vjf","Wt6vjf");_.Kf=new C("byfTOb","byfTOb");_.Lf=new C("LEikZe","LEikZe");_.Mf=new C("lsjVmc","lsjVmc");new C("pVbxBc");new C("klpyYe");new C("OPbIxb");new C("pg9hFd");new C("IaqD3e");new C("Xpw1of");new C("v5BQle");new C("tdUkaf");new C("WSziFf");new C("UBSgGf");new C("zZa4xc");new C("o1bZcd");new C("WwG67d");new C("z72MOc");new C("JccZRe");new C("amY3Td");new C("ABma3e");new C("gSshPb");new C("yu4DA");new C("vk3Wc");new C("IykvEf");new C("J5K1Ad");new C("IW8Usd");new C("jbDgG");new C("b8xKu");new C("d0RAGb");new C("AzG0ke");new C("J4QWB");new C("TuDsZ");new C("hdXIif");new C("mITR5c");new C("DFElXb");new C("FENZqe");new C("tLnxq");Ff({g:[{id:_.cc,ic:de,multiple:!0}]});var Nf={};var Of=new zd,Pf=function(a,b){_.Ad.call(this,a,b);this.node=b};_.y(Pf,_.Ad);_.Qf=RegExp("^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)","i");var Tf;_.Rf=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");_.Sf=function(a){return a?decodeURI(a):a};Tf=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?_.hd(e):"")}}};
_.Uf=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Uf(a,String(b[d]),c);else null!=b&&c.push(a+(""===b?"":"="+_.gd(b)))};var Wf,Xf,Yf,$f,bg,jg,cg,eg,dg,hg,fg,kg;_.Vf=function(a){this.h=this.A=this.l="";this.B=null;this.s=this.j="";this.o=!1;var b;a instanceof _.Vf?(this.o=a.o,Wf(this,a.l),this.A=a.A,this.h=a.h,Xf(this,a.B),this.j=a.j,Yf(this,Zf(a.g)),this.s=a.s):a&&(b=String(a).match(_.Rf))?(this.o=!1,Wf(this,b[1]||"",!0),this.A=$f(b[2]||""),this.h=$f(b[3]||"",!0),Xf(this,b[4]),this.j=$f(b[5]||"",!0),Yf(this,b[6]||"",!0),this.s=$f(b[7]||"")):(this.o=!1,this.g=new _.ag(null,this.o))};
_.Vf.prototype.toString=function(){var a=[],b=this.l;b&&a.push(bg(b,cg,!0),":");var c=this.h;if(c||"file"==b)a.push("//"),(b=this.A)&&a.push(bg(b,cg,!0),"@"),a.push(_.gd(c).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.B,null!=c&&a.push(":",String(c));if(c=this.j)this.h&&"/"!=c.charAt(0)&&a.push("/"),a.push(bg(c,"/"==c.charAt(0)?dg:eg,!0));(c=this.g.toString())&&a.push("?",c);(c=this.s)&&a.push("#",bg(c,fg));return a.join("")};
_.Vf.prototype.resolve=function(a){var b=new _.Vf(this),c=!!a.l;c?Wf(b,a.l):c=!!a.A;c?b.A=a.A:c=!!a.h;c?b.h=a.h:c=null!=a.B;var d=a.j;if(c)Xf(b,a.B);else if(c=!!a.j){if("/"!=d.charAt(0))if(this.h&&!this.j)d="/"+d;else{var e=b.j.lastIndexOf("/");-1!=e&&(d=b.j.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):".."==h?((1<f.length||1==f.length&&
""!=f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?b.j=d:c=""!==a.g.toString();c?Yf(b,Zf(a.g)):c=!!a.s;c&&(b.s=a.s);return b};Wf=function(a,b,c){a.l=c?$f(b,!0):b;a.l&&(a.l=a.l.replace(/:$/,""))};Xf=function(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("L`"+b);a.B=b}else a.B=null};Yf=function(a,b,c){b instanceof _.ag?(a.g=b,gg(a.g,a.o)):(c||(b=bg(b,hg)),a.g=new _.ag(b,a.o))};_.ig=function(a){var b=_.id();a.g.set("zx",b)};
$f=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""};bg=function(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,jg),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null};jg=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};cg=/[#\/\?@]/g;eg=/[#\?:]/g;dg=/[#\?]/g;hg=/[#\?@]/g;fg=/#/g;_.ag=function(a,b){this.h=this.g=null;this.j=a||null;this.l=!!b};
kg=function(a){a.g||(a.g=new Map,a.h=0,a.j&&Tf(a.j,function(b,c){a.add(_.hd(b),c)}))};_.ag.prototype.add=function(a,b){kg(this);this.j=null;a=lg(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.h+=1;return this};_.ag.prototype.remove=function(a){kg(this);a=lg(this,a);return this.g.has(a)?(this.j=null,this.h-=this.g.get(a).length,this.g.delete(a)):!1};_.ag.prototype.Ib=function(){kg(this);return 0==this.h};var mg=function(a,b){kg(a);b=lg(a,b);return a.g.has(b)};_.l=_.ag.prototype;
_.l.forEach=function(a,b){kg(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};_.l.mc=function(){kg(this);for(var a=Array.from(this.g.values()),b=Array.from(this.g.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};_.l.Ya=function(a){kg(this);var b=[];if("string"===typeof a)mg(this,a)&&(b=b.concat(this.g.get(lg(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
_.l.set=function(a,b){kg(this);this.j=null;a=lg(this,a);mg(this,a)&&(this.h-=this.g.get(a).length);this.g.set(a,[b]);this.h+=1;return this};_.l.get=function(a,b){if(!a)return b;a=this.Ya(a);return 0<a.length?String(a[0]):b};_.ng=function(a,b,c){a.remove(b);0<c.length&&(a.j=null,a.g.set(lg(a,b),_.wa(c)),a.h+=c.length)};
_.ag.prototype.toString=function(){if(this.j)return this.j;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=_.gd(d);d=this.Ya(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+_.gd(d[f]));a.push(g)}}return this.j=a.join("&")};
var Zf=function(a){var b=new _.ag;b.j=a.j;a.g&&(b.g=new Map(a.g),b.h=a.h);return b},lg=function(a,b){b=String(b);a.l&&(b=b.toLowerCase());return b},gg=function(a,b){b&&!a.l&&(kg(a),a.j=null,a.g.forEach(function(c,d){var e=d.toLowerCase();d!=e&&(this.remove(d),_.ng(this,e,c))},a));a.l=b};_.ag.prototype.o=function(a){for(var b=0;b<arguments.length;b++)_.Bf(arguments[b],function(c,d){this.add(d,c)},this)};_.og=Ta(function(){try{return new URL("s://g"),!0}catch(a){return!1}});var pg="ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER".split(" "),qg=[["A",new Map([["href",
{Ba:2}]])],["AREA",new Map([["href",{Ba:2}]])],["LINK",new Map([["href",{Ba:2,conditions:new Map([["rel",new Set("alternate author bookmark canonical cite help icon license next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" "))]])}]])],["SOURCE",new Map([["src",{Ba:1}]])],["IMG",new Map([["src",{Ba:1}]])],["VIDEO",new Map([["src",{Ba:1}]])],["AUDIO",new Map([["src",{Ba:1}]])]],rg="title aria-atomic aria-autocomplete aria-busy aria-checked aria-current aria-disabled aria-dropeffect aria-expanded aria-haspopup aria-hidden aria-invalid aria-label aria-level aria-live aria-multiline aria-multiselectable aria-orientation aria-posinset aria-pressed aria-readonly aria-relevant aria-required aria-selected aria-setsize aria-sort aria-valuemax aria-valuemin aria-valuenow aria-valuetext alt align autocapitalize autocomplete autocorrect autofocus autoplay bgcolor border cellpadding cellspacing checked color cols colspan controls datetime disabled download draggable enctype face formenctype frameborder height hreflang hidden ismap label lang loop max maxlength media minlength min multiple muted nonce open placeholder preload rel required reversed role rows rowspan selected shape size sizes slot span spellcheck start step summary translate type valign value width wrap itemscope itemtype itemid itemprop itemref".split(" "),
sg=[["dir",{Ba:3,conditions:Ta(function(){return new Map([["dir",new Set(["auto","ltr","rtl"])]])})}],["async",{Ba:3,conditions:Ta(function(){return new Map([["async",new Set(["async"])]])})}],["cite",{Ba:2}],["loading",{Ba:3,conditions:Ta(function(){return new Map([["loading",new Set(["eager","lazy"])]])})}],["poster",{Ba:2}],["target",{Ba:3,conditions:Ta(function(){return new Map([["target",new Set(["_self","_blank"])]])})}]],tg=new function(a,b,c){var d=new Set(["data-","aria-"]),e=new Map(qg);
this.j=a;this.g=e;this.l=b;this.o=c;this.h=d}(new Set(Ta(function(){return pg.concat("STYLE TITLE INPUT TEXTAREA BUTTON LABEL".split(" "))})),new Set(Ta(function(){return rg.concat(["class","id","tabindex","contenteditable","name"])})),new Map(Ta(function(){return sg.concat([["style",{Ba:4}]])})));var ug;ug=function(){this.h=tg;this.g=[]};_.vg=Ta(function(){return new ug});_.Ua=function(a){this.Kj=a};_.wg=[Va("data"),Va("http"),Va("https"),Va("mailto"),Va("ftp"),new _.Ua(function(a){return/^[^:]*([/?#]|$)/.test(a)})];_.xg=function(a,b){b||_.ld();this.j=a||null};_.xg.prototype.Oa=function(a){a=a({},this.j?this.j.g():{});this.h(null,"function"==typeof _.yg&&a instanceof _.yg?a.Db:null)};_.xg.prototype.h=function(){};var zg=function(a){this.h=a;this.j=this.h.g(_.Hf)};zg.prototype.g=function(){this.h.ob()||(this.j=this.h.g(_.Hf));return this.j?this.j.l():{}};var Ag=function(a){var b=new zg(a);_.xg.call(this,b,a.get(_.cc).h);this.l=new _.G;this.o=b};_.y(Ag,_.xg);Ag.prototype.g=function(){return this.o.g()};Ag.prototype.h=function(a,b){_.xg.prototype.h.call(this,a,b);this.l.dispatchEvent(new Pf(Of,a,b))};_.ra(Gf,Ag);Ff({g:[{id:Gf,ic:Ag,multiple:!0}]});var Bg=function(a,b){this.defaultValue=a;this.type=b;this.value=a};Bg.prototype.get=function(){return this.value};Bg.prototype.set=function(a){this.value=a};var Cg=function(a){Bg.call(this,a,"b")};_.y(Cg,Bg);Cg.prototype.get=function(){return this.value};var Dg=function(a){this.Nf=a};Dg.prototype.toString=function(){return this.Nf.join(".")};var Eg=function(){this.g={};this.h="";this.j={};this.l=null};Eg.prototype.toString=function(){if(this.h.endsWith("_/wa/"))var a=this.l?this.h+Fg(this,"wk")+"/"+this.l:this.h+Fg(this,"wk")+".wasm";else{a=this.h+Gg(this);var b=this.j;var c=[],d;for(d in b)_.Uf(d,b[d],c);b=c.join("&");c="";""!=b&&(c="?"+b);a+=c}return a};
var Hg=function(a){a=Fg(a,"md");return!!a&&"0"!==a},Gg=function(a){var b=[],c=(0,_.z)(function(d){void 0!==this.g[d]&&b.push(d+"="+this.g[d])},a);Hg(a)?(c("md"),c("k"),c("ck"),c("am"),c("rs"),c("gssmodulesetproto")):(c("sdch"),c("k"),c("ck"),c("am"),c("rt"),"d"in a.g||Ig(a,"d","0"),c("d"),c("exm"),c("excm"),(a.g.excm||a.g.exm)&&b.push("ed=1"),c("im"),c("dg"),c("sm"),"1"==Fg(a,"br")&&c("br"),""!==Jg(a)&&c("wt"),c("gssmodulesetproto"),c("ujg"),c("rs"),c("cb"),c("ee"),c("m"));return b.join("/")},Fg=
function(a,b){return a.g[b]?a.g[b]:null},Ig=function(a,b,c){c?a.g[b]=c:delete a.g[b]},Jg=function(a){switch(Fg(a,"wt")){case "0":return"0";case "1":return"1";case "2":return"2";default:return""}},Ng=function(a){var b=void 0===b?!0:b;var c=Kg(a),d=new Eg,e=c.match(_.Rf)[5];_.Lc(Lg,function(g){var h=e.match("/"+g+"=([^/]+)");h&&Ig(d,g,h[1])});var f=-1!=a.indexOf("_/ss/")?"_/ss/":-1!=a.indexOf("_/wa/")?"_/wa/":"_/js/";d.h=a.substr(0,a.indexOf(f)+f.length);if(d.h.endsWith("_/wa/"))return b=Mg(a),a.endsWith(".wasm")||
(a=a.split("/"),d.l=a[a.length-1]),Ig(d,"wk",b.toString()),d;if(!b)return d;(a=c.match(_.Rf)[6]||null)&&Tf(a,function(g,h){d.j[g]=h});return d},Mg=function(a){a.endsWith(".wasm")?a=a.substring(a.lastIndexOf("_/wa/")+5,a.lastIndexOf(".wasm")):(a=a.split("/"),a=a[a.length-2]);try{var b=a.split(".");var c=3!==b.length?null:new Dg(b);if(null===c)throw new TypeError("Q`"+a);return c}catch(d){return null}},Kg=function(a){return a.startsWith("https://uberproxy-pen-redirect.corp.google.com/uberproxy/pen?url=")?
a.substr(65):a},Lg={Jl:"k",bl:"ck",gm:"wk",yl:"m",kl:"exm",il:"excm",Sk:"am",wl:"mm",Il:"rt",rl:"d",jl:"ed",Sl:"sv",cl:"deob",Zk:"cb",Pl:"rs",Kl:"sdch",ul:"im",dl:"dg",gl:"br",hm:"wt",ll:"ee",Rl:"sm",xl:"md",pl:"gssmodulesetproto",em:"ujg"};_.Og=function(a){_.B.call(this);this.h=a;this.g={}};_.A(_.Og,_.B);var Pg=[];_.Og.prototype.J=function(a,b,c,d){return Qg(this,a,b,c,d)};var Qg=function(a,b,c,d,e,f){Array.isArray(c)||(c&&(Pg[0]=c.toString()),c=Pg);for(var g=0;g<c.length;g++){var h=_.F(b,c[g],d||a.handleEvent,e||!1,f||a.h||a);if(!h)break;a.g[h.key]=h}return a};_.Og.prototype.Xb=function(a,b,c,d){return Rg(this,a,b,c,d)};
var Rg=function(a,b,c,d,e,f){if(Array.isArray(c))for(var g=0;g<c.length;g++)Rg(a,b,c[g],d,e,f);else{b=_.Qd(b,c,d||a.handleEvent,e,f||a.h||a);if(!b)return a;a.g[b.key]=b}return a};_.Og.prototype.qb=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.qb(a,b[f],c,d,e);else c=c||this.handleEvent,d=_.ya(d)?!!d.capture:!!d,e=e||this.h||this,c=Rd(c),d=!!d,b=_.Fd(a)?a.ld(b,c,d,e):a?(a=_.Td(a))?a.ld(b,c,d,e):null:null,b&&(_.Yd(b),delete this.g[b.key]);return this};
_.Sg=function(a){_.Lc(a.g,function(b,c){this.g.hasOwnProperty(c)&&_.Yd(b)},a);a.g={}};_.Og.prototype.I=function(){_.Og.P.I.call(this);_.Sg(this)};_.Og.prototype.handleEvent=function(){throw Error("R");};var Tg=function(){};Tg.prototype.h=null;var Ug=function(a){return a.h||(a.h=a.l())};var Vg,Wg=function(){};_.A(Wg,Tg);Wg.prototype.g=function(){var a=Xg(this);return a?new ActiveXObject(a):new XMLHttpRequest};Wg.prototype.l=function(){var a={};Xg(this)&&(a[0]=!0,a[1]=!0);return a};var Xg=function(a){if(!a.j&&"undefined"==typeof XMLHttpRequest&&"undefined"!=typeof ActiveXObject){for(var b=["MSXML2.XMLHTTP.6.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],c=0;c<b.length;c++){var d=b[c];try{return new ActiveXObject(d),a.j=d}catch(e){}}throw Error("S");}return a.j};
Vg=new Wg;var Yg=function(){};_.A(Yg,Tg);Yg.prototype.g=function(){var a=new XMLHttpRequest;if("withCredentials"in a)return a;if("undefined"!=typeof XDomainRequest)return new Zg;throw Error("T");};Yg.prototype.l=function(){return{}};
var Zg=function(){this.g=new XDomainRequest;this.readyState=0;this.onreadystatechange=null;this.responseType=this.responseText="";this.status=-1;this.statusText="";this.g.onload=(0,_.z)(this.Zh,this);this.g.onerror=(0,_.z)(this.hg,this);this.g.onprogress=(0,_.z)(this.wj,this);this.g.ontimeout=(0,_.z)(this.yj,this)};_.l=Zg.prototype;_.l.open=function(a,b,c){if(null!=c&&!c)throw Error("U");this.g.open(a,b)};_.l.send=function(a){if(a)if("string"==typeof a)this.g.send(a);else throw Error("V");else this.g.send()};
_.l.abort=function(){this.g.abort()};_.l.setRequestHeader=function(){};_.l.getResponseHeader=function(a){return"content-type"==a.toLowerCase()?this.g.contentType:""};_.l.Zh=function(){this.status=200;this.responseText=this.g.responseText;$g(this,4)};_.l.hg=function(){this.status=500;this.responseText="";$g(this,4)};_.l.yj=function(){this.hg()};_.l.wj=function(){this.status=200;$g(this,1)};var $g=function(a,b){a.readyState=b;if(a.onreadystatechange)a.onreadystatechange()};
Zg.prototype.getAllResponseHeaders=function(){return"content-type: "+this.g.contentType};_.ah=function(a,b,c){if("function"===typeof a)c&&(a=(0,_.z)(a,c));else if(a&&"function"==typeof a.handleEvent)a=(0,_.z)(a.handleEvent,a);else throw Error("X");return 2147483647<Number(b)?-1:_.t.setTimeout(a,b||0)};var ch,dh;_.bh=function(a){_.G.call(this);this.headers=new Map;this.H=a||null;this.h=!1;this.D=this.g=null;this.s="";this.l=0;this.j=this.G=this.A=this.L=!1;this.o=0;this.B=null;this.M="";this.N=this.F=!1};_.A(_.bh,_.G);ch=/^https?$/i;dh=["POST","PUT"];_.eh=[];_.bh.prototype.V=function(){this.T();_.va(_.eh,this)};
_.bh.prototype.send=function(a,b,c,d){if(this.g)throw Error("Y`"+this.s+"`"+a);b=b?b.toUpperCase():"GET";this.s=a;this.l=0;this.L=!1;this.h=!0;this.g=this.H?this.H.g():Vg.g();this.D=this.H?Ug(this.H):Ug(Vg);this.g.onreadystatechange=(0,_.z)(this.S,this);try{this.G=!0,this.g.open(b,String(a),!0),this.G=!1}catch(g){fh(this);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if("function"===typeof d.keys&&"function"===typeof d.get){e=
_.x(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Z`"+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});e=_.t.FormData&&a instanceof _.t.FormData;!_.ta(dh,b)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=_.x(c);for(d=b.next();!d.done;d=b.next())c=_.x(d.value),d=c.next().value,c=c.next().value,this.g.setRequestHeader(d,c);this.M&&(this.g.responseType=this.M);"withCredentials"in
this.g&&this.g.withCredentials!==this.F&&(this.g.withCredentials=this.F);try{gh(this),0<this.o&&((this.N=hh(this.g))?(this.g.timeout=this.o,this.g.ontimeout=(0,_.z)(this.Y,this)):this.B=_.ah(this.Y,this.o,this)),this.A=!0,this.g.send(a),this.A=!1}catch(g){fh(this)}};var hh=function(a){return _.E&&"number"===typeof a.timeout&&void 0!==a.ontimeout};_.bh.prototype.Y=function(){"undefined"!=typeof rb&&this.g&&(this.l=8,this.dispatchEvent("timeout"),this.abort(8))};
var fh=function(a){a.h=!1;a.g&&(a.j=!0,a.g.abort(),a.j=!1);a.l=5;ih(a);jh(a)},ih=function(a){a.L||(a.L=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};_.bh.prototype.abort=function(a){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.l=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),jh(this))};_.bh.prototype.I=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),jh(this,!0));_.bh.P.I.call(this)};
_.bh.prototype.S=function(){this.ob()||(this.G||this.A||this.j?kh(this):this.ba())};_.bh.prototype.ba=function(){kh(this)};
var kh=function(a){if(a.h&&"undefined"!=typeof rb&&(!a.D[1]||4!=(a.g?a.g.readyState:0)||2!=a.ub()))if(a.A&&4==(a.g?a.g.readyState:0))_.ah(a.S,0,a);else if(a.dispatchEvent("readystatechange"),4==(a.g?a.g.readyState:0)){a.h=!1;try{a.nd()?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.l=6,a.ub(),ih(a))}finally{jh(a)}}},jh=function(a,b){if(a.g){gh(a);var c=a.g,d=a.D[0]?function(){}:null;a.g=null;a.D=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=d}catch(e){}}},gh=function(a){a.g&&
a.N&&(a.g.ontimeout=null);a.B&&(_.t.clearTimeout(a.B),a.B=null)};_.bh.prototype.isActive=function(){return!!this.g};_.bh.prototype.nd=function(){var a=this.ub();a:switch(a){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var b=!0;break a;default:b=!1}if(!b){if(a=0===a)a=String(this.s).match(_.Rf)[1]||null,!a&&_.t.self&&_.t.self.location&&(a=_.t.self.location.protocol.slice(0,-1)),a=!ch.test(a?a.toLowerCase():"");b=a}return b};
_.bh.prototype.ub=function(){try{return 2<(this.g?this.g.readyState:0)?this.g.status:-1}catch(a){return-1}};_.bh.prototype.kb=function(){try{return this.g?this.g.responseText:""}catch(a){return""}};var mh=function(a){_.B.call(this);this.D=a;this.A=Ng(a);this.l=this.o=null;this.G=!0;this.h=new _.Og(this);this.L=[];this.s=new Set;this.g=[];this.N=new lh;this.j=[];this.B=!1;a=(0,_.z)(this.F,this);Nf.version=a};_.y(mh,_.B);var nh=function(a,b){a.g.length&&Se(b,a.g[a.g.length-1]);a.g.push(b);Pe(b,function(){_.va(this.g,b)},a)};mh.prototype.H=function(a,b,c){var d=void 0===c?{}:c;c=d.Ti;var e=d.Af,f=d.qk;a=oh(this,a,b,d.Pi,c);ph(this,a,e,f,c)};
var oh=function(a,b,c,d,e){d=void 0===d?{}:d;var f=[];qh(a,b,c,d,void 0===e?!1:e,function(g){f.push(g.jb())});return f},qh=function(a,b,c,d,e,f,g){g=void 0===g?{}:g;b=_.x(b);for(var h=b.next();!h.done;h=b.next()){var k=h.value;h=c[k];!e&&(a.s.has(k)||h.g)||g[k]||(g[k]=!0,k=d[k]?Object.keys(d[k]):[],qh(a,h.h.concat(k),c,d,e,f,g),f(h))}},ph=function(a,b,c,d,e){e=void 0===e?!1:e;var f=[],g=new Ie;b=[b];for(var h=function(p,q){for(var r=[],w=0,D=Math.floor(p.length/q)+1,Q=0;Q<q;Q++){var W=(Q+1)*D;r.push(p.slice(w,
W));w=W}return r},k=b.shift();k;){var m=rh(a,k,!!e,!0);if(2E3>=m.length){if(k=sh(a,k,e))f.push(k),Se(g,k.g)}else b=h(k,Math.ceil(m.length/2E3)).concat(b);k=b.shift()}var n=new Ie;nh(a,n);Pe(n,function(){return th(a,f,c,d)});Qe(n,function(){var p=new uh;p.j=!0;p.h=-1;th(this,[p],c,d)},a);Pe(g,function(){return n.callback()});g.callback()},sh=function(a,b,c){var d=rh(a,b,!(void 0===c||!c));a.L.push(d);b=_.x(b);for(c=b.next();!c.done;c=b.next())a.s.add(c.value);if(a.B)a=_.sd(document,"SCRIPT"),_.Xa(a,
_.$a(d)),a.type="text/javascript",a.async=!1,document.body.appendChild(a);else{var e=new uh,f=new _.bh(0<a.j.length?new Yg:void 0);a.h.J(f,"success",(0,_.z)(e.B,e,f));a.h.J(f,"error",(0,_.z)(e.A,e,f));a.h.J(f,"timeout",(0,_.z)(e.s,e));Qg(a.h,f,"ready",f.T,!1,f);f.o=3E4;vh(a.N,function(){f.send(d);return e.g});return e}return null},th=function(a,b,c,d){for(var e=!1,f,g=!1,h=0;h<b.length;h++){var k=b[h];if(!f&&k.j){e=!0;f=k.h;break}else k.l&&(g=!0)}h=_.wa(a.g);(e||g)&&-1!=f&&(a.g.length=0);if(e)c&&
c(f);else if(g)d&&d();else for(a=0;a<b.length;a++)d=b[a],wh(d.o,d.ib)||c&&c(8001);(e||g)&&-1!=f&&_.ec(h,function(m){m.cancel()})};mh.prototype.I=function(){this.h.T();delete Nf.version;_.B.prototype.I.call(this)};mh.prototype.F=function(){return Fg(this.A,"k")};
var rh=function(a,b,c,d){d=void 0===d?!1:d;var e=_.Sf(a.D.match(_.Rf)[3]||null);if(0<a.j.length&&!_.ta(a.j,e)&&null!=e&&window.location.hostname!=e)throw Error("ba`"+e);e=Ng(a.A.toString());delete e.g.m;delete e.g.exm;delete e.g.ed;Ig(e,"m",b.join(","));a.o&&(Ig(e,"ck",a.o),a.l&&Ig(e,"rs",a.l));Ig(e,"d","0");c&&(a=_.id(),e.j.zx=a);a=e.toString();if(d&&0==a.lastIndexOf("/",0)){e=document.location.href.match(_.Rf);d=e[1];b=e[2];c=e[3];e=e[4];var f="";d&&(f+=d+":");c&&(f+="//",b&&(f+=b+"@"),f+=c,e&&
(f+=":"+e));a=f+a}return a},wh=function(a,b){var c="";if(1<a.length&&"\n"===a.charAt(a.length-1)){var d=a.lastIndexOf("\n",a.length-2);0<=d&&(c=a.substring(d+1,a.length-1))}d=c.length-11;if(0<=d&&c.indexOf("Google Inc.",d)==d||0==c.lastIndexOf("//# sourceMappingURL=",0))try{c=window;a=a+"\r\n//# sourceURL="+b;a=_.Ya(a);var e=_.Sb(a);var f=_.Rb(e);c.eval(f)===f&&c.eval(f.toString())}catch(g){return!1}else return!1;return!0},xh=function(a){var b=_.Sf(a.match(_.Rf)[5]||null)||"",c=_.Sf(Kg(b).match(_.Rf)[5]||
null);return(null===c?0:RegExp("/_/wa/","g").test(c)?Mg(b):RegExp("(/_/js/)|(/_/ss/)","g").test(c)&&/\/k=/.test(c))?a:null},uh=function(){this.g=new Ie;this.ib=this.o="";this.j=!1;this.h=0;this.l=!1};uh.prototype.B=function(a){this.o=a.kb();this.ib=String(a.s);this.g.callback()};uh.prototype.A=function(a){this.j=!0;this.h=a.ub();this.g.callback()};uh.prototype.s=function(){this.l=!0;this.g.callback()};
var lh=function(){this.g=0;this.h=[]},vh=function(a,b){a.h.push(b);yh(a)},yh=function(a){for(;5>a.g&&a.h.length;)zh(a,a.h.shift())},zh=function(a,b){a.g++;Pe(b(),function(){this.g--;yh(this)},a)};var Ah=new Cg(!1),Bh=document.location.href;
Ff({h:{dml:Ah},initialize:function(a){var b=Ah.get(),c="",d="";window&&window._F_cssRowKey&&(c=window._F_cssRowKey,window._F_combinedSignature&&(d=window._F_combinedSignature));if(c&&"function"!==typeof window._F_installCss)throw Error("$");var e,f=_.t._F_jsUrl;f&&(e=xh(f));!e&&(f=document.getElementById("base-js"))&&(e=f.src?f.src:f.getAttribute("href"),e=xh(e));e||(e=xh(Bh));e||(e=document.getElementsByTagName("script"),e=xh(e[e.length-1].src));if(!e)throw Error("aa");e=new mh(e);c&&(e.o=c);d&&
(e.l=d);e.B=b;b=_.ka();b.B=e;b.Kh(!0);b=_.ka();b.Pf(a);a.A(b)}});
_._ModuleManager_initialize=function(a,b){if(!_.fa){if(!_.ia)return;_.ja()}_.fa.Of(a,b)};
_._ModuleManager_initialize('b/sy0/sy1/sy2/rJmJrc:1,2,3/n73qwf/UUJqVe/MpJwZc/GHAeAc/sy3/sy4:9/sy5/Wt6vjf:2,a,b/sy6:1/byfTOb:d/sy7:a/sy8/sy9/LEikZe:2,3,d,f,g,h/lsjVmc:f,g/sya/el_conf:k/el_main_css/syc:a/E4ivtd:n/syd:n/VinDy:o,p/sye:9/syf/el_main:b,f,h,k,m,p,r,s/corsproxy/website_error/navigationui:a,s/_stam:r',['sya','el_conf']);
}catch(e){_._DumpException(e)}
try{
_.K={};MSG_TRANSLATE="Traduci";_.K[0]=MSG_TRANSLATE;MSG_CANCEL="Annulla";_.K[1]=MSG_CANCEL;MSG_CLOSE="Chiudi";_.K[2]=MSG_CLOSE;MSGFUNC_PAGE_TRANSLATED_TO=function(a){return"Google ha tradotto automaticamente questa pagina in: "+a};_.K[3]=MSGFUNC_PAGE_TRANSLATED_TO;MSGFUNC_TRANSLATED_TO=function(a){return"Tradotta in: "+a};_.K[4]=MSGFUNC_TRANSLATED_TO;MSG_GENERAL_ERROR="Errore. Impossibile completare la richiesta nel server. Riprova pi\u00f9 tardi.";_.K[5]=MSG_GENERAL_ERROR;MSG_LEARN_MORE="Ulteriori informazioni";
_.K[6]=MSG_LEARN_MORE;MSGFUNC_POWERED_BY=function(a){return"Powered by "+a};_.K[7]=MSGFUNC_POWERED_BY;MSG_TRANSLATE_PRODUCT_NAME="Traduttore";_.K[8]=MSG_TRANSLATE_PRODUCT_NAME;MSG_TRANSLATION_IN_PROGRESS="Traduzione in corso";_.K[9]=MSG_TRANSLATION_IN_PROGRESS;MSGFUNC_TRANSLATE_PAGE_TO=function(a){return"Tradurre questa pagina in "+a+" utilizzando Google Traduttore?"};_.K[10]=MSGFUNC_TRANSLATE_PAGE_TO;MSGFUNC_VIEW_PAGE_IN=function(a){return"Visualizza questa pagina in: "+a};_.K[11]=MSGFUNC_VIEW_PAGE_IN;
MSG_RESTORE="Mostra originale";_.K[12]=MSG_RESTORE;MSG_SSL_INFO_LOCAL_FILE="I contenuti di questo file locale saranno inviati a Google per essere tradotti utilizzando una connessione protetta.";_.K[13]=MSG_SSL_INFO_LOCAL_FILE;MSG_SSL_INFO_SECURE_PAGE="I contenuti di questa pagina sicura saranno inviati a Google per essere tradotti utilizzando una connessione protetta.";_.K[14]=MSG_SSL_INFO_SECURE_PAGE;MSG_SSL_INFO_INTRANET_PAGE="I contenuti di questa pagina intranet saranno inviati a Google per essere tradotti utilizzando una connessione protetta.";
_.K[15]=MSG_SSL_INFO_INTRANET_PAGE;MSG_SELECT_LANGUAGE="Seleziona lingua";_.K[16]=MSG_SELECT_LANGUAGE;MSGFUNC_TURN_OFF_TRANSLATION=function(a){return"Disattiva traduzione di "+a};_.K[17]=MSGFUNC_TURN_OFF_TRANSLATION;MSGFUNC_TURN_OFF_FOR=function(a){return"Disattiva per: "+a};_.K[18]=MSGFUNC_TURN_OFF_FOR;MSG_ALWAYS_HIDE_AUTO_POPUP_BANNER="Nascondi sempre";_.K[19]=MSG_ALWAYS_HIDE_AUTO_POPUP_BANNER;MSG_ORIGINAL_TEXT="Testo originale:";_.K[20]=MSG_ORIGINAL_TEXT;MSG_FILL_SUGGESTION="Contribuisci a una traduzione migliore";
_.K[21]=MSG_FILL_SUGGESTION;MSG_SUBMIT_SUGGESTION="Contribuisci";_.K[22]=MSG_SUBMIT_SUGGESTION;MSG_SHOW_TRANSLATE_ALL="Traduci tutto";_.K[23]=MSG_SHOW_TRANSLATE_ALL;MSG_SHOW_RESTORE_ALL="Ripristina tutto";_.K[24]=MSG_SHOW_RESTORE_ALL;MSG_SHOW_CANCEL_ALL="Annulla tutto";_.K[25]=MSG_SHOW_CANCEL_ALL;MSG_TRANSLATE_TO_MY_LANGUAGE="Traduci le sezioni nella mia lingua";_.K[26]=MSG_TRANSLATE_TO_MY_LANGUAGE;MSGFUNC_TRANSLATE_EVERYTHING_TO=function(a){return"Traduci tutto in "+a};_.K[27]=MSGFUNC_TRANSLATE_EVERYTHING_TO;
MSG_SHOW_ORIGINAL_LANGUAGES="Mostra lingue originali";_.K[28]=MSG_SHOW_ORIGINAL_LANGUAGES;MSG_OPTIONS="Opzioni";_.K[29]=MSG_OPTIONS;MSG_TURN_OFF_TRANSLATION_FOR_THIS_SITE="Disattiva la traduzione per questo sito";_.K[30]=MSG_TURN_OFF_TRANSLATION_FOR_THIS_SITE;_.K[31]=null;MSG_ALT_SUGGESTION="Mostra traduzioni alternative";_.K[32]=MSG_ALT_SUGGESTION;MSG_ALT_ACTIVITY_HELPER_TEXT="Fai clic sulle parole riportate sopra per ottenere traduzioni alternative";_.K[33]=MSG_ALT_ACTIVITY_HELPER_TEXT;
MSG_USE_ALTERNATIVES="Utilizza";_.K[34]=MSG_USE_ALTERNATIVES;MSG_DRAG_TIP="Trascina tenendo premuto MAIUSC per riordinare.";_.K[35]=MSG_DRAG_TIP;MSG_CLICK_FOR_ALT="Fai clic per visualizzare le traduzioni alternative";_.K[36]=MSG_CLICK_FOR_ALT;MSG_DRAG_INSTUCTIONS="Tieni premuto il tasto MAIUSC, fai clic e trascina le parole riportate sopra per riordinarle.";_.K[37]=MSG_DRAG_INSTUCTIONS;MSG_SUGGESTION_SUBMITTED="Grazie per aver contribuito a Google Traduttore con la traduzione che hai suggerito.";
_.K[38]=MSG_SUGGESTION_SUBMITTED;MSG_MANAGE_TRANSLATION_FOR_THIS_SITE="Gestisci la traduzione per questo sito";_.K[39]=MSG_MANAGE_TRANSLATION_FOR_THIS_SITE;MSG_ALT_AND_CONTRIBUTE_ACTIVITY_HELPER_TEXT="Fai clic su una parola per visualizzare le traduzioni alternative oppure fai doppio clic per apportare direttamente le modifiche";_.K[40]=MSG_ALT_AND_CONTRIBUTE_ACTIVITY_HELPER_TEXT;MSG_ORIGINAL_TEXT_NO_COLON="Testo originale";_.K[41]=MSG_ORIGINAL_TEXT_NO_COLON;_.K[42]="Traduttore";_.K[43]="Traduci";
_.K[44]="La correzione \u00e8 stata inviata.";MSG_LANGUAGE_UNSUPPORTED="Errore: la lingua della pagina web non \u00e8 supportata.";_.K[45]=MSG_LANGUAGE_UNSUPPORTED;MSG_LANGUAGE_TRANSLATE_WIDGET="Widget di traduzione della lingua";_.K[46]=MSG_LANGUAGE_TRANSLATE_WIDGET;MSG_RATE_THIS_TRANSLATION="Valuta questa traduzione";_.K[47]=MSG_RATE_THIS_TRANSLATION;MSG_FEEDBACK_USAGE_FOR_IMPROVEMENT="Il tuo feedback verr\u00e0 utilizzato per contribuire a migliorare Google Traduttore";_.K[48]=MSG_FEEDBACK_USAGE_FOR_IMPROVEMENT;
MSG_FEEDBACK_SATISFIED_LABEL="Traduzione di buona qualit\u00e0";_.K[49]=MSG_FEEDBACK_SATISFIED_LABEL;MSG_FEEDBACK_DISSATISFIED_LABEL="Traduzione mediocre";_.K[50]=MSG_FEEDBACK_DISSATISFIED_LABEL;MSG_TRANSLATION_NO_COLON="Traduzione";_.K[51]=MSG_TRANSLATION_NO_COLON;
}catch(e){_._DumpException(e)}
try{
_.na("el_conf");
var mk;_._exportVersion=function(a){_.Db("google.translate.v",a)};_._getCallbackFunction=function(a){return _.vb(a)};_._exportMessages=function(){_.Db("google.translate.m",_.K)};mk=function(a){var b=document.getElementsByTagName("head")[0];b||(b=document.body.parentNode.appendChild(document.createElement("head")));b.appendChild(a)};_._loadJs=function(a){var b=_.sd(document,"SCRIPT");b.type="text/javascript";b.charset="UTF-8";_.Xa(b,_.$a(a));mk(b)};
_._loadCss=function(a){var b=document.createElement("link");b.type="text/css";b.rel="stylesheet";b.charset="UTF-8";b.href=a;mk(b)};_._isNS=function(a){a=a.split(".");for(var b=window,c=0;c<a.length;++c)if(!(b=b[a[c]]))return!1;return!0};_._setupNS=function(a){a=a.split(".");for(var b=window,c=0;c<a.length;++c)b.hasOwnProperty?b.hasOwnProperty(a[c])?b=b[a[c]]:b=b[a[c]]={}:b=b[a[c]]||(b[a[c]]={});return b};_.Db("_exportVersion",_._exportVersion);_.Db("_getCallbackFunction",_._getCallbackFunction);
_.Db("_exportMessages",_._exportMessages);_.Db("_loadJs",_._loadJs);_.Db("_loadCss",_._loadCss);_.Db("_isNS",_._isNS);_.Db("_setupNS",_._setupNS);window.addEventListener&&"undefined"==typeof document.readyState&&window.addEventListener("DOMContentLoaded",function(){document.readyState="complete"},!1);
_.pa();
}catch(e){_._DumpException(e)}
}).call(this,this.default_tr);
// Google Inc.

//# sourceURL=/_/translate_http/_/js/k=translate_http.tr.it.xsu1zECPYBA.O/d=1/rs=AN8SPfo7yXzRr9s9y_6U0QJNmLuqXjqYkw/m=el_conf
    // Configure Constants
    (function(){
      let gtConstEvalStartTime = new Date();
      if (_isNS('google.translate.Element')){return}

      (function(){
        const c=_setupNS('google.translate._const');

        c._cest = gtConstEvalStartTime;
        gtConstEvalStartTime = undefined; // hide this eval start time constant
  c._cl='it'; c._cuc='TECB_1E07F158C6FA4460B352973E9693B329'; c._cac='tee'; c._cam=''; c._ctkk='469672.2966364769'; const h='translate.googleapis.com'; const oph='translate-pa.googleapis.com'; const s='https'+'://'; c._pah=h; c._pas=s; const b=s+'translate.googleapis.com'; const staticPath = '/translate_static/'; c._pci=b+staticPath+'img/te_ctrl3.gif'; c._pmi=b+staticPath+'img/mini_google.png'; c._pbi=b+staticPath+'img/te_bk.gif'; c._pli=b+staticPath+'img/loading.gif'; c._ps = this.EDGE_TRANSLATE_URL + 'google/element_main.css'; c._plla = this.EDGE_TRANSLATE_URL + 'google/lans/lan_la.js'; c._puh='translate.google.com';c._cnal={};_loadCss(c._ps); _loadJs(this.EDGE_TRANSLATE_URL + 'google/element_main.js');_exportMessages(); _exportVersion('TE_20230726');
        })();
      })();
  