## 各位火狐浏览器用户你们好

我是侧边翻译的开发者之一，主要负责侧边翻译在火狐浏览器上的上架管理。

首先感谢各位对于侧边翻译的喜爱，正是因为有你们的支持，侧边翻译才能走到今天，真的很谢谢你们！

但是我很抱歉的通知各位，我们将在火狐浏览器版的侧边翻译中删除网页翻译相关功能（其他平台的版本不受影响），我们对此感到很遗憾，但是我们别无选择。

简单来说，为了实现网页翻译功能，我们需要使用谷歌的网页翻译组件，但这些组件都是私有的，我们无法获得它们的源码。而现在，火狐要求我们提供我们所用到的所有第三方组件的源码，否则我们的扩展就会被从商店中移除。审核人员曾建议我们找找开源的组件，但我们找不到，所以只能删除这个功能以求通过审核。

这个插件是我们利用空闲时间开发的作品，凝聚了我们很多的心血，被迫删除这个辛苦实现的功能让我们很痛心，加上火狐浏览器自身的许许多多的问题（如不支持使用扩展内置的 pdf 阅读器打开 pdf 文件，导致侧边翻译的 pdf 划词功能失效；不支持在通知中添加按钮，导致我们需要花额外的时间去兼容等等等等）带来的额外的开发成本，我们以后可能不会再为火狐版的侧边翻译增加新功能。我们强烈建议喜欢侧边翻译的用户尝试使用 Chromium 系的浏览器，如 Chrome，新版 Edge，开源的 Chromium 等。

如果你真的需要网页翻译功能，可以考虑如下两种方案：

1. 在可能的情况下，我们会提供拥有测试签名的安装包，可以直接下载安装，步骤如下：

    1. 前往 [Releases 页面](https://github.com/EdgeTranslate/EdgeTranslate/releases/latest)

    2. 如果页面最下方的下载选项中有文件名格式为`EdgeTranslate_vx.x.x.x_firefox.xpi`的文件（注意是 **4** 位版本号），那么直接下载安装该文件即可

    3. 如果没有这样的文件，说明我们无法提供安装包了，此时请遵循下一种方案

2. 手动下载安装未签名的完整版侧边翻译，具体步骤如下：

    1. 下载安装 Firefox 的[延长支持版（ESR）](//www.mozilla.org/firefox/organizations/)、[开发者版](//www.mozilla.org/firefox/developer/)或 [Nightly 版](//nightly.mozilla.org/)，三者任选其一，推荐程度：延长支持版 > 开发者版 > Nightly 版

    2. 前往 [Releases 页面](https://github.com/EdgeTranslate/EdgeTranslate/releases/latest)下载最新版的，带有网页翻译功能的火狐版侧边翻译安装包，包名格式为：`EdgeTranslate_vx.x.x_firefox.zip`

    3. 打开 Firefox 配置编辑器（在地址栏输入`about:config` 然后按回车），搜索 `xpinstall.signatures.required` 并将值设置为 `false`（点最后的双向箭头切换）

    4. 打开 Firefox 附加组件管理器 （在地址栏输入`about:addons`然后按回车），点击右上角设置菜单，选择“从文件安装附加组件”，选择下载的 zip 文件即可

感谢大家一直以来的支持！
