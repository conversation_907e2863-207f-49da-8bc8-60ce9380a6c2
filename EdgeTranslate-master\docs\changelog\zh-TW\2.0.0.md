### 注意

* 關於火狐瀏覽器安裝zip格式的擴展包請參攷這裡：#60！

* 為了進一步改善翻譯體驗，我們建立了側邊翻譯用戶交流群，歡迎大家加入：[側邊翻譯](https://t.me/EdgeTranslate)

### 新增

* 新增 __多翻譯源支持__，現時已支持 __穀歌翻譯__，__必應翻譯__，__百度翻譯__，__騰訊翻譯君__ 四個翻譯源，可以在翻譯結果展示框的左上角選擇，後續將加入更多得翻譯源，敬請期待！

* 新增 __混合翻譯源__ 功能，可以綜合展示多個翻譯源的結果，例如，可以使用穀歌翻譯來展示音標並朗讀文字，同時使用百度翻譯來獲取單詞例句；

* 新增 __懸浮框__ 模式，現在可以通過拖動翻譯框來使得翻譯結果以貼邊或懸浮的管道展示：

  + 當翻譯框在左側或右側貼邊展示時，拖動翻譯框到頁面中部可以使翻譯框懸浮在頁面上，當你再次選詞翻譯時，翻譯框會自動在選擇的文字附近展開；

  + 當翻譯框在頁面中懸浮展示時，將翻譯框向左側或右側拖動直到滑鼠到達荧幕左側或右側邊界，可以使翻譯框恢復到左側或右側貼邊展示；

  + 懸浮框可以自由調整大小和位置，以適應不同的需求；

### 優化

* 重構了項目整體結構，提高了項目可維護性；

* 使用Promise取代了部分回檔，提高程式碼可讀性；

* 優化了不同瀏覽器的程式碼生成管道，在最終編譯結果中去除了冗餘程式碼；

### 修復

* 修復了大量潜在問題；

### 支持我們

開發側邊翻譯花費了我們許多的時間和精力，如果你真的覺得這個項目對你有幫助，不妨請我們喝罐可樂，支持我們繼續做下去：[PayPal](https://paypal.me/EdgeTranslate)

當然，這 __純屬自願__，打賞并不會帶來什麽優待，不打賞也不會有影響，請量力而爲！
