{"name": "wxt-starter", "description": "manifest.json description", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@wxt-dev/module-react": "^1.1.3", "eslint-config-prettier": "^10.1.8", "prettier": "^3.6.2", "typescript": "^5.8.3", "vitest": "^3.2.4", "wxt": "^0.20.6"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tesseract.js": "^6.0.1", "zustand": "^5.0.6"}}