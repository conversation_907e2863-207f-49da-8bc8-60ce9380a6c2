### 本地化

我們開發了此擴展程序來幫助更多的人克服語言障礙。但是世界上有太多的語言，而我們只會說英語和中文。到目前為止，我們的軟件僅支持以下語言：

- 英語
  
- 簡體中文
  
- 繁體中文

- 俄語（感謝[ElectroLom](https://github.com/electrolom42) 和 [Viktor](https://github.com/ViktorOn)）

- 日語（感謝[ykyuki](https://github.com/ykyuki))

如果您的母語不在上面的列表中，並且您願意幫我們增加對您母語的支持，我們將不勝感激。

### 如何添加語言支持

1. Fork並克隆此[項目](https://github.com/EdgeTranslate/EdgeTranslate)。

2. 在`static/_locales`下添加一個名為`localeCode`的文件夾，其中`localeCode`是諸如`en`（代表英語）和`ru`（代表俄語）之類的代碼，您可以在[此處](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/src/popup/languages.js)找到大多數語言對應的代碼；

3. 將文件`static/_locales/en/messages.json`複製到剛創建的`localeCode`文件夾中；

4. 翻譯`messages.json`的內容

   例如，在`AppName`標籤中有`"messages": "Edge Translate"`和`"description": "App Name"`這兩個內容，您需要將`Edge Translate`和`App Name`這兩個內容翻譯成您的語言。順便說一句，從標籤`Afrikaans`開始的語言名稱可以不用翻譯；

5. 提交您的更改並將其推送到您Fork過來的倉庫中；

6. 向我們的倉庫創建一個新的[pull request](https://github.com/EdgeTranslate/EdgeTranslate/pulls)。我們將盡快合併您的拉取請求。

### 更多信息

請前往[Chrome開發者文檔](https://developer.chrome.com/extensions/i18n)獲取更多信息。
