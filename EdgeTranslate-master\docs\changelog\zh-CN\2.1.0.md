### 注意

* 关于火狐浏览器安装zip格式的扩展包请参考[这里](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/zh_CN/%E8%87%B4%E7%81%AB%E7%8B%90%E7%94%A8%E6%88%B7.md)！

* 为了进一步改善翻译体验，我们建立了侧边翻译用户交流群，欢迎大家加入：[侧边翻译用户交流QQ群](https://jq.qq.com/?_wv=1027&k=gT5EYfFB)

### 新增

* 允许在翻译展示框中编辑选中的文本，如果选择文本时不慎漏选或多选了，可以方便地进行纠正并重新翻译；

* 点击发音按钮后加载出音频数据前展示加载动画，提升交互体验；

* 添加对维吾尔语翻译功能的支持；

* 更加实用的错误提示，可以提供更多的错误信息，有助于及时找出问题所在；

* 更新内置地PDF阅读器，界面更美观；

### 修复

* 修复翻译展示框的一系列样式问题；

* 修复HTML字符的转义问题(#127)；

* 修复某些网页上的图片被侧边翻译替换的问题；

* 修复禁用再重新启用插件后上下文菜单项随机消失的问题；

* 修复由于不同浏览器中语言标识不同导致的问题；

* 修复语言标识不统一导致谷歌网页翻译无法使用的问题；

* 修复侧边翻译与Octotree插件冲突的问题(#128)；

* 修复悬浮框最小宽度限制失效的问题；

* 修复某些情况下网络正常必应翻译却提示网络错误的问题；

### 关于打赏

开发这个项目花费了我们许多的时间和精力，如果你真的觉得这个项目对你有帮助，不妨请我们喝罐可乐，支持我们继续做下去！

当然，这 __纯属自愿__，打赏并不能获得什么优待，不打赏也不会有任何影响，请量力而为！

| 微信 | 支付宝 |
| :-: | :-: |
| <img src="https://user-images.githubusercontent.com/25877145/80864662-b6617c00-8cb6-11ea-915a-582ca046118c.png" height=200 alt="微信支付"/> | <img src="https://user-images.githubusercontent.com/25877145/80864685-ced19680-8cb6-11ea-94e5-f5ca8e4389b9.jpg" height=200 alt="支付宝支付"/> |