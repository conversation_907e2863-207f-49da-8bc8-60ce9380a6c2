// API callback
callback({
  "sourceLanguages": [
    {
      "language": "auto",
      "name": "Detectar idioma"
    },
    {
      "language": "af",
      "name": "afrikáans"
    },
    {
      "language": "ay",
      "name": "aimara"
    },
    {
      "language": "sq",
      "name": "albanés"
    },
    {
      "language": "de",
      "name": "ale<PERSON>"
    },
    {
      "language": "am",
      "name": "amh<PERSON>rico"
    },
    {
      "language": "ar",
      "name": "árabe"
    },
    {
      "language": "hy",
      "name": "armenio"
    },
    {
      "language": "as",
      "name": "asam<PERSON>"
    },
    {
      "language": "az",
      "name": "azerí"
    },
    {
      "language": "bm",
      "name": "bambara"
    },
    {
      "language": "bn",
      "name": "bengalí"
    },
    {
      "language": "bho",
      "name": "bhoyapurí"
    },
    {
      "language": "be",
      "name": "bielorruso"
    },
    {
      "language": "my",
      "name": "birmano"
    },
    {
      "language": "bs",
      "name": "bos<PERSON>"
    },
    {
      "language": "bg",
      "name": "búlgaro"
    },
    {
      "language": "km",
      "name": "camboyano"
    },
    {
      "language": "kn",
      "name": "canarés"
    },
    {
      "language": "ca",
      "name": "catalán"
    },
    {
      "language": "ceb",
      "name": "cebuano"
    },
    {
      "language": "cs",
      "name": "checo"
    },
    {
      "language": "ny",
      "name": "chichewa"
    },
    {
      "language": "zh-CN",
      "name": "chino"
    },
    {
      "language": "si",
      "name": "cingalés"
    },
    {
      "language": "ko",
      "name": "coreano"
    },
    {
      "language": "co",
      "name": "corso"
    },
    {
      "language": "ht",
      "name": "criollo haitiano"
    },
    {
      "language": "hr",
      "name": "croata"
    },
    {
      "language": "da",
      "name": "danés"
    },
    {
      "language": "dv",
      "name": "divehi"
    },
    {
      "language": "doi",
      "name": "dogri"
    },
    {
      "language": "sk",
      "name": "eslovaco"
    },
    {
      "language": "sl",
      "name": "esloveno"
    },
    {
      "language": "es",
      "name": "español"
    },
    {
      "language": "eo",
      "name": "esperanto"
    },
    {
      "language": "et",
      "name": "estonio"
    },
    {
      "language": "eu",
      "name": "euskera"
    },
    {
      "language": "ee",
      "name": "ewé"
    },
    {
      "language": "fi",
      "name": "finlandés"
    },
    {
      "language": "fr",
      "name": "francés"
    },
    {
      "language": "fy",
      "name": "frisio"
    },
    {
      "language": "gd",
      "name": "gaélico escocés"
    },
    {
      "language": "cy",
      "name": "galés"
    },
    {
      "language": "gl",
      "name": "gallego"
    },
    {
      "language": "ka",
      "name": "georgiano"
    },
    {
      "language": "el",
      "name": "griego"
    },
    {
      "language": "gn",
      "name": "guaraní"
    },
    {
      "language": "gu",
      "name": "gujarati"
    },
    {
      "language": "ha",
      "name": "hausa"
    },
    {
      "language": "haw",
      "name": "hawaiano"
    },
    {
      "language": "iw",
      "name": "hebreo"
    },
    {
      "language": "hi",
      "name": "hindi"
    },
    {
      "language": "hmn",
      "name": "hmong"
    },
    {
      "language": "hu",
      "name": "húngaro"
    },
    {
      "language": "ig",
      "name": "igbo"
    },
    {
      "language": "ilo",
      "name": "ilocano"
    },
    {
      "language": "id",
      "name": "indonesio"
    },
    {
      "language": "en",
      "name": "inglés"
    },
    {
      "language": "ga",
      "name": "irlandés"
    },
    {
      "language": "is",
      "name": "islandés"
    },
    {
      "language": "it",
      "name": "italiano"
    },
    {
      "language": "ja",
      "name": "japonés"
    },
    {
      "language": "jw",
      "name": "javanés"
    },
    {
      "language": "kk",
      "name": "kazajo"
    },
    {
      "language": "rw",
      "name": "kinyarwanda"
    },
    {
      "language": "ky",
      "name": "kirguís"
    },
    {
      "language": "gom",
      "name": "konkaní"
    },
    {
      "language": "kri",
      "name": "krio"
    },
    {
      "language": "ku",
      "name": "kurdo (kurmanyi)"
    },
    {
      "language": "ckb",
      "name": "kurdo (sorani)"
    },
    {
      "language": "lo",
      "name": "lao"
    },
    {
      "language": "la",
      "name": "latín"
    },
    {
      "language": "lv",
      "name": "letón"
    },
    {
      "language": "ln",
      "name": "lingala"
    },
    {
      "language": "lt",
      "name": "lituano"
    },
    {
      "language": "lg",
      "name": "luganda"
    },
    {
      "language": "lb",
      "name": "luxemburgués"
    },
    {
      "language": "mk",
      "name": "macedonio"
    },
    {
      "language": "mai",
      "name": "maithili"
    },
    {
      "language": "ml",
      "name": "malayalam"
    },
    {
      "language": "ms",
      "name": "malayo"
    },
    {
      "language": "mg",
      "name": "malgache"
    },
    {
      "language": "mt",
      "name": "maltés"
    },
    {
      "language": "mi",
      "name": "maorí"
    },
    {
      "language": "mr",
      "name": "maratí"
    },
    {
      "language": "mni-Mtei",
      "name": "meiteilon (manipuri)"
    },
    {
      "language": "lus",
      "name": "mizo"
    },
    {
      "language": "mn",
      "name": "mongol"
    },
    {
      "language": "nl",
      "name": "neerlandés"
    },
    {
      "language": "ne",
      "name": "nepalí"
    },
    {
      "language": "no",
      "name": "noruego"
    },
    {
      "language": "or",
      "name": "oriya"
    },
    {
      "language": "om",
      "name": "oromo"
    },
    {
      "language": "pa",
      "name": "panyabí"
    },
    {
      "language": "ps",
      "name": "pastún"
    },
    {
      "language": "fa",
      "name": "persa"
    },
    {
      "language": "pl",
      "name": "polaco"
    },
    {
      "language": "pt",
      "name": "portugués"
    },
    {
      "language": "qu",
      "name": "quechua"
    },
    {
      "language": "ro",
      "name": "rumano"
    },
    {
      "language": "ru",
      "name": "ruso"
    },
    {
      "language": "sm",
      "name": "samoano"
    },
    {
      "language": "sa",
      "name": "sánscrito"
    },
    {
      "language": "nso",
      "name": "sepedi"
    },
    {
      "language": "sr",
      "name": "serbio"
    },
    {
      "language": "st",
      "name": "sesoto"
    },
    {
      "language": "sn",
      "name": "shona"
    },
    {
      "language": "sd",
      "name": "sindhi"
    },
    {
      "language": "so",
      "name": "somalí"
    },
    {
      "language": "sw",
      "name": "suajili"
    },
    {
      "language": "sv",
      "name": "sueco"
    },
    {
      "language": "su",
      "name": "sundanés"
    },
    {
      "language": "tl",
      "name": "tagalo"
    },
    {
      "language": "th",
      "name": "tailandés"
    },
    {
      "language": "ta",
      "name": "tamil"
    },
    {
      "language": "tt",
      "name": "tártaro"
    },
    {
      "language": "tg",
      "name": "tayiko"
    },
    {
      "language": "te",
      "name": "telugu"
    },
    {
      "language": "ti",
      "name": "tigriña"
    },
    {
      "language": "ts",
      "name": "tsonga"
    },
    {
      "language": "tr",
      "name": "turco"
    },
    {
      "language": "tk",
      "name": "turkmeno"
    },
    {
      "language": "ak",
      "name": "twi"
    },
    {
      "language": "uk",
      "name": "ucraniano"
    },
    {
      "language": "ug",
      "name": "uigur"
    },
    {
      "language": "ur",
      "name": "urdu"
    },
    {
      "language": "uz",
      "name": "uzbeco"
    },
    {
      "language": "vi",
      "name": "vietnamita"
    },
    {
      "language": "xh",
      "name": "xhosa"
    },
    {
      "language": "yi",
      "name": "yidis"
    },
    {
      "language": "yo",
      "name": "yoruba"
    },
    {
      "language": "zu",
      "name": "zulú"
    }
  ],
  "targetLanguages": [
    {
      "language": "af",
      "name": "afrikáans"
    },
    {
      "language": "ay",
      "name": "aimara"
    },
    {
      "language": "sq",
      "name": "albanés"
    },
    {
      "language": "de",
      "name": "alemán"
    },
    {
      "language": "am",
      "name": "amhárico"
    },
    {
      "language": "ar",
      "name": "árabe"
    },
    {
      "language": "hy",
      "name": "armenio"
    },
    {
      "language": "as",
      "name": "asamés"
    },
    {
      "language": "az",
      "name": "azerí"
    },
    {
      "language": "bm",
      "name": "bambara"
    },
    {
      "language": "bn",
      "name": "bengalí"
    },
    {
      "language": "bho",
      "name": "bhoyapurí"
    },
    {
      "language": "be",
      "name": "bielorruso"
    },
    {
      "language": "my",
      "name": "birmano"
    },
    {
      "language": "bs",
      "name": "bosnio"
    },
    {
      "language": "bg",
      "name": "búlgaro"
    },
    {
      "language": "km",
      "name": "camboyano"
    },
    {
      "language": "kn",
      "name": "canarés"
    },
    {
      "language": "ca",
      "name": "catalán"
    },
    {
      "language": "ceb",
      "name": "cebuano"
    },
    {
      "language": "cs",
      "name": "checo"
    },
    {
      "language": "ny",
      "name": "chichewa"
    },
    {
      "language": "zh-CN",
      "name": "chino (simplificado)"
    },
    {
      "language": "zh-TW",
      "name": "chino (tradicional)"
    },
    {
      "language": "si",
      "name": "cingalés"
    },
    {
      "language": "ko",
      "name": "coreano"
    },
    {
      "language": "co",
      "name": "corso"
    },
    {
      "language": "ht",
      "name": "criollo haitiano"
    },
    {
      "language": "hr",
      "name": "croata"
    },
    {
      "language": "da",
      "name": "danés"
    },
    {
      "language": "dv",
      "name": "divehi"
    },
    {
      "language": "doi",
      "name": "dogri"
    },
    {
      "language": "sk",
      "name": "eslovaco"
    },
    {
      "language": "sl",
      "name": "esloveno"
    },
    {
      "language": "es",
      "name": "español"
    },
    {
      "language": "eo",
      "name": "esperanto"
    },
    {
      "language": "et",
      "name": "estonio"
    },
    {
      "language": "eu",
      "name": "euskera"
    },
    {
      "language": "ee",
      "name": "ewé"
    },
    {
      "language": "fi",
      "name": "finlandés"
    },
    {
      "language": "fr",
      "name": "francés"
    },
    {
      "language": "fy",
      "name": "frisio"
    },
    {
      "language": "gd",
      "name": "gaélico escocés"
    },
    {
      "language": "cy",
      "name": "galés"
    },
    {
      "language": "gl",
      "name": "gallego"
    },
    {
      "language": "ka",
      "name": "georgiano"
    },
    {
      "language": "el",
      "name": "griego"
    },
    {
      "language": "gn",
      "name": "guaraní"
    },
    {
      "language": "gu",
      "name": "gujarati"
    },
    {
      "language": "ha",
      "name": "hausa"
    },
    {
      "language": "haw",
      "name": "hawaiano"
    },
    {
      "language": "iw",
      "name": "hebreo"
    },
    {
      "language": "hi",
      "name": "hindi"
    },
    {
      "language": "hmn",
      "name": "hmong"
    },
    {
      "language": "hu",
      "name": "húngaro"
    },
    {
      "language": "ig",
      "name": "igbo"
    },
    {
      "language": "ilo",
      "name": "ilocano"
    },
    {
      "language": "id",
      "name": "indonesio"
    },
    {
      "language": "en",
      "name": "inglés"
    },
    {
      "language": "ga",
      "name": "irlandés"
    },
    {
      "language": "is",
      "name": "islandés"
    },
    {
      "language": "it",
      "name": "italiano"
    },
    {
      "language": "ja",
      "name": "japonés"
    },
    {
      "language": "jw",
      "name": "javanés"
    },
    {
      "language": "kk",
      "name": "kazajo"
    },
    {
      "language": "rw",
      "name": "kinyarwanda"
    },
    {
      "language": "ky",
      "name": "kirguís"
    },
    {
      "language": "gom",
      "name": "konkaní"
    },
    {
      "language": "kri",
      "name": "krio"
    },
    {
      "language": "ku",
      "name": "kurdo (kurmanyi)"
    },
    {
      "language": "ckb",
      "name": "kurdo (sorani)"
    },
    {
      "language": "lo",
      "name": "lao"
    },
    {
      "language": "la",
      "name": "latín"
    },
    {
      "language": "lv",
      "name": "letón"
    },
    {
      "language": "ln",
      "name": "lingala"
    },
    {
      "language": "lt",
      "name": "lituano"
    },
    {
      "language": "lg",
      "name": "luganda"
    },
    {
      "language": "lb",
      "name": "luxemburgués"
    },
    {
      "language": "mk",
      "name": "macedonio"
    },
    {
      "language": "mai",
      "name": "maithili"
    },
    {
      "language": "ml",
      "name": "malayalam"
    },
    {
      "language": "ms",
      "name": "malayo"
    },
    {
      "language": "mg",
      "name": "malgache"
    },
    {
      "language": "mt",
      "name": "maltés"
    },
    {
      "language": "mi",
      "name": "maorí"
    },
    {
      "language": "mr",
      "name": "maratí"
    },
    {
      "language": "mni-Mtei",
      "name": "meiteilon (manipuri)"
    },
    {
      "language": "lus",
      "name": "mizo"
    },
    {
      "language": "mn",
      "name": "mongol"
    },
    {
      "language": "nl",
      "name": "neerlandés"
    },
    {
      "language": "ne",
      "name": "nepalí"
    },
    {
      "language": "no",
      "name": "noruego"
    },
    {
      "language": "or",
      "name": "oriya"
    },
    {
      "language": "om",
      "name": "oromo"
    },
    {
      "language": "pa",
      "name": "panyabí"
    },
    {
      "language": "ps",
      "name": "pastún"
    },
    {
      "language": "fa",
      "name": "persa"
    },
    {
      "language": "pl",
      "name": "polaco"
    },
    {
      "language": "pt",
      "name": "portugués"
    },
    {
      "language": "qu",
      "name": "quechua"
    },
    {
      "language": "ro",
      "name": "rumano"
    },
    {
      "language": "ru",
      "name": "ruso"
    },
    {
      "language": "sm",
      "name": "samoano"
    },
    {
      "language": "sa",
      "name": "sánscrito"
    },
    {
      "language": "nso",
      "name": "sepedi"
    },
    {
      "language": "sr",
      "name": "serbio"
    },
    {
      "language": "st",
      "name": "sesoto"
    },
    {
      "language": "sn",
      "name": "shona"
    },
    {
      "language": "sd",
      "name": "sindhi"
    },
    {
      "language": "so",
      "name": "somalí"
    },
    {
      "language": "sw",
      "name": "suajili"
    },
    {
      "language": "sv",
      "name": "sueco"
    },
    {
      "language": "su",
      "name": "sundanés"
    },
    {
      "language": "tl",
      "name": "tagalo"
    },
    {
      "language": "th",
      "name": "tailandés"
    },
    {
      "language": "ta",
      "name": "tamil"
    },
    {
      "language": "tt",
      "name": "tártaro"
    },
    {
      "language": "tg",
      "name": "tayiko"
    },
    {
      "language": "te",
      "name": "telugu"
    },
    {
      "language": "ti",
      "name": "tigriña"
    },
    {
      "language": "ts",
      "name": "tsonga"
    },
    {
      "language": "tr",
      "name": "turco"
    },
    {
      "language": "tk",
      "name": "turkmeno"
    },
    {
      "language": "ak",
      "name": "twi"
    },
    {
      "language": "uk",
      "name": "ucraniano"
    },
    {
      "language": "ug",
      "name": "uigur"
    },
    {
      "language": "ur",
      "name": "urdu"
    },
    {
      "language": "uz",
      "name": "uzbeco"
    },
    {
      "language": "vi",
      "name": "vietnamita"
    },
    {
      "language": "xh",
      "name": "xhosa"
    },
    {
      "language": "yi",
      "name": "yidis"
    },
    {
      "language": "yo",
      "name": "yoruba"
    },
    {
      "language": "zu",
      "name": "zulú"
    }
  ]
}
);