import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';

// POST /api/ocr - OCR文字识别
export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // 验证用户认证
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const { image, language = 'auto' } = body;

    // 验证必填字段
    if (!image) {
      return NextResponse.json({ error: 'Image data is required' }, { status: 400 });
    }

    // 验证图片格式
    if (!image.startsWith('data:image/')) {
      return NextResponse.json({ error: 'Invalid image format' }, { status: 400 });
    }

    const startTime = Date.now();

    try {
      // 调用OCR服务
      const ocrResult = await performOCR(image, language);
      
      const responseTime = Date.now() - startTime;

      // 记录用量
      await supabase.from('usage_logs').insert({
        user_id: user.id,
        model_id: null,
        request_type: 'ocr',
        characters_count: ocrResult.text.length,
        source_language: ocrResult.language,
        target_language: null,
        success: true,
        response_time_ms: responseTime
      });

      return NextResponse.json({
        success: true,
        data: {
          ...ocrResult,
          processing_time_ms: responseTime
        }
      });

    } catch (ocrError: any) {
      const responseTime = Date.now() - startTime;
      
      // 记录失败的用量
      await supabase.from('usage_logs').insert({
        user_id: user.id,
        model_id: null,
        request_type: 'ocr',
        characters_count: 0,
        source_language: language,
        target_language: null,
        success: false,
        error_message: ocrError.message,
        response_time_ms: responseTime
      });

      console.error('OCR error:', ocrError);
      return NextResponse.json({ 
        error: 'OCR recognition failed', 
        details: ocrError.message 
      }, { status: 500 });
    }

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// OCR识别函数
async function performOCR(imageData: string, language: string) {
  // 这里应该集成真实的OCR服务，比如：
  // 1. Google Cloud Vision API
  // 2. Azure Computer Vision
  // 3. AWS Textract
  // 4. 百度OCR API
  // 5. 腾讯OCR API
  
  // 为了演示，这里使用模拟的OCR结果
  return await simulateOCR(imageData, language);
}

// 模拟OCR识别（实际应该替换为真实的OCR服务）
async function simulateOCR(imageData: string, language: string) {
  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // 根据语言返回不同的模拟结果
  const sampleTexts = {
    'chi_sim': '这是中文OCR识别的示例文本\n包含多行内容\n识别准确度较高',
    'eng': 'This is a sample OCR text recognition\nMultiple lines of content\nHigh accuracy recognition',
    'jpn': 'これはOCR文字認識のサンプルテキストです\n複数行のコンテンツ\n高精度認識',
    'kor': '이것은 OCR 문자 인식 샘플 텍스트입니다\n여러 줄의 내용\n높은 정확도 인식',
    'auto': 'Mixed language text 混合语言文本\nAutomatic language detection\n自动语言检测'
  };

  const detectedLanguages = {
    'chi_sim': 'zh',
    'eng': 'en', 
    'jpn': 'ja',
    'kor': 'ko',
    'auto': 'mixed'
  };

  const text = sampleTexts[language as keyof typeof sampleTexts] || sampleTexts['auto'];
  const detectedLang = detectedLanguages[language as keyof typeof detectedLanguages] || 'auto';

  return {
    text,
    confidence: 0.92,
    language: detectedLang
  };
}

// 真实的Google Cloud Vision OCR实现示例（需要配置API密钥）
async function performGoogleCloudOCR(imageData: string, language: string) {
  const apiKey = process.env.GOOGLE_CLOUD_API_KEY;
  if (!apiKey) {
    throw new Error('Google Cloud API key not configured');
  }

  // 移除data URL前缀
  const base64Image = imageData.replace(/^data:image\/[a-z]+;base64,/, '');

  const response = await fetch(`https://vision.googleapis.com/v1/images:annotate?key=${apiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      requests: [{
        image: {
          content: base64Image
        },
        features: [{
          type: 'TEXT_DETECTION',
          maxResults: 1
        }],
        imageContext: {
          languageHints: language === 'auto' ? [] : [language]
        }
      }]
    })
  });

  if (!response.ok) {
    throw new Error(`Google Cloud Vision API error: ${response.status}`);
  }

  const data = await response.json();
  const textAnnotations = data.responses[0]?.textAnnotations;

  if (!textAnnotations || textAnnotations.length === 0) {
    return {
      text: '',
      confidence: 0,
      language: 'unknown'
    };
  }

  const fullText = textAnnotations[0].description;
  const confidence = textAnnotations[0].confidence || 0.8;

  return {
    text: fullText,
    confidence,
    language: detectLanguage(fullText)
  };
}

// 简单的语言检测
function detectLanguage(text: string): string {
  if (/[\u4e00-\u9fff]/.test(text)) return 'zh';
  if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return 'ja';
  if (/[\uac00-\ud7af]/.test(text)) return 'ko';
  if (/[\u0600-\u06ff]/.test(text)) return 'ar';
  if (/[\u0400-\u04ff]/.test(text)) return 'ru';
  return 'en';
}
