### Attention

* For the installation of the expansion pack in ZIP format for Firefox browser, please refer to [here](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/en/ToFirefoxUsers.md)!

* In order to further improve the translation experience, we have established an Edge Translate user communication group. Welcome to join: [Edge Translate](https://t.me/EdgeTranslate)

### New

* Pronouncing errors are now displayed in a popup notification which will not replace the translating result anymore.

### Fix

* Fix the problem that Google page translate always translates pages into Chinese.

* Fix the problem that translating result cannot be displayed in the Notice page on Firefox.

* Fix the problem that if `Translate After Select` is enabled, translating may be unexpectedly triggered in some cases.

### Sponsor

It took us much time and energy to develop this project. If it truly helped you in some way, you could reward us with cans of Coke to support us to keep improving it: [PayPal](https://paypal.me/EdgeTranslate).

But, this is completely __voluntary__. Sponsoring won't bring any special treatment and you can still use Edge Translate freely without sponsoring. Do it according to your capability!