### 翻译接口

每个翻译接口需要导出一个`Translator`对象，它具有如下三个基本方法：

* supportedLanguages

  用于获取支持的语言，函数原型：`supportedLanguages()`。其返回相应接口支持的语言集合(`Set<String>`)，语言使用谷歌翻译接口的语言简写表示。

* detect

  用于检测语言，函数原型：`detect(text)`。其返回一个表示检测结果的`Promise`对象，其完成状态(then)的传入值是检测语言的结果，例如:"en"

* translate

  用于翻译文本，函数原型：`translate(text, from, to)`。其返回一个表示翻译结果的`Promise`对象。

  > 当from为"auto"时，translate函数需要自行解决(翻译接口本身支持"auto"或调用detect函数进行检测)

  ```json
    {
        "originalText": 原始文本,
        "mainMeaning": 主要意思,
        "tPronunciation": 翻译读音,
        "sPronunciation": 原文读音,
        "detailedMeanings": [
          	{
				        "pos": 词性,
            	  "meaning": 意思,
            	  "synonyms": [近义词]
          	}
        ],
        "definitions": [
			      {
				        "pos": 词性,
            	  "meaning": 意思,
				        "synonyms": [近义词],
				        "example": 例句
			     }
		    ],
        "examples": [
			      {
				        "source": 源语言例句,
				        "target": 目的语言例句
			     }
		   ]
    }
  ```

* pronounce

  用于实现文本转语音，函数原型：`pronounce(text, language, speed)`。`speed`的取值为`"fast"`或`"slow"`，函数返回一个`Promise`对象用于错误处理。

* stopPronounce

  用于停止语音朗读，函数原型：`stopPronounce()`。没有返回值。

### 错误处理

需要进行错误处理的接口有：`detect`, `translate`, `pronounce`。当发生错误时，需要返回的错误信息为：

```json
{
  "errorType": "error type [NET_ERR|API_ERR]",
  "errorCode": "error code",
  "errorMsg": "error message",
  "errorAct": {
    "api": "error api",
    "action": "error action, enum{detect, translate, pronounce}",
    "text": "text",
    "from": "source language",
    "to": "target language",
  }
}
```

其中`errorType`用于指出错误的类型，其取值有：

* NET_ERR

  表示网络错误，即由于网络问题无法正常发出请求。出现此问题应要求用户检查网络状况，包括尝试手动访问相应的翻译接口主页等；

  此时`errorCode`为0，`errorMsg`为手动设置的错误说明，包括对用户的提示等；

* API_ERR

  表示翻译接口错误，即虽然成功发出了请求，但翻译接口没有返回正确的结果。出现此问题应检查翻译接口的参数设置等；

  此时`errorCode`为相应翻译接口返回的错误码，`errorMsg`为翻译接口返回的错误信息；
