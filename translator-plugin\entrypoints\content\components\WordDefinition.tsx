import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  CircularProgress, 
  Alert,
  Chip,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  VolumeUp as VolumeUpIcon,
  ContentCopy as CopyIcon
} from '@mui/icons-material';
import { useThemeStore } from '../../../shared/stores/useThemeStore';

interface WordDefinitionProps {
  word: string;
  translation?: string; // 添加翻译结果
}

interface Definition {
  partOfSpeech: string;
  definitions: Array<{
    definition: string;
    example?: string;
    synonyms?: string[];
    antonyms?: string[];
  }>;
}

interface WordData {
  word: string;
  phonetic?: string;
  phonetics?: Array<{
    text?: string;
    audio?: string;
  }>;
  meanings: Definition[];
}

export const WordDefinition: React.FC<WordDefinitionProps> = ({ word, translation }) => {
  const { currentTheme } = useThemeStore();
  const [wordData, setWordData] = useState<WordData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (word && word.trim() && /^[a-zA-Z]+$/.test(word.trim())) {
      fetchWordDefinition(word.trim().toLowerCase());
    }
  }, [word]);

  const fetchWordDefinition = async (searchWord: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // 使用免费的Dictionary API
      const response = await fetch(`https://api.dictionaryapi.dev/api/v2/entries/en/${searchWord}`);
      
      if (!response.ok) {
        throw new Error('单词未找到');
      }
      
      const data = await response.json();
      if (data && data.length > 0) {
        setWordData(data[0]);
      } else {
        throw new Error('无法获取单词定义');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取单词定义失败');
      setWordData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSpeak = (text: string) => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'en-US';
      speechSynthesis.speak(utterance);
    }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (!word || !/^[a-zA-Z]+$/.test(word.trim())) {
    return null;
  }

  return (
    <Box sx={{ mt: 2 }}>
      <Divider sx={{ mb: 2 }} />
      
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
        单词详解
      </Typography>

      {/* 显示翻译结果 */}
      {translation && (
        <Box sx={{
          mb: 2,
          p: 1.5,
          backgroundColor: currentTheme === 'dark' ? 'rgba(76, 175, 80, 0.1)' : 'rgba(76, 175, 80, 0.05)',
          borderRadius: 1,
          border: '1px solid',
          borderColor: currentTheme === 'dark' ? 'rgba(76, 175, 80, 0.3)' : 'rgba(76, 175, 80, 0.2)',
        }}>
          <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'success.main' }}>
            翻译: {translation}
          </Typography>
        </Box>
      )}

      {loading && (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 2 }}>
          <CircularProgress size={20} sx={{ mr: 1 }} />
          <Typography variant="body2" color="text.secondary">
            查询中...
          </Typography>
        </Box>
      )}

      {error && (
        <Alert severity="warning" sx={{ mb: 2, fontSize: '0.875rem' }}>
          {error}
        </Alert>
      )}

      {wordData && (
        <Box>
          {/* 单词标题和发音 */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {wordData.word}
              </Typography>
              <IconButton 
                size="small" 
                onClick={() => handleSpeak(wordData.word)}
                title="发音"
              >
                <VolumeUpIcon sx={{ fontSize: 16 }} />
              </IconButton>
              <IconButton 
                size="small" 
                onClick={() => handleCopy(wordData.word)}
                title="复制"
              >
                <CopyIcon sx={{ fontSize: 16 }} />
              </IconButton>
            </Box>
            
            {wordData.phonetic && (
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                {wordData.phonetic}
              </Typography>
            )}
          </Box>

          {/* 词义和例句 */}
          {wordData.meanings.map((meaning, index) => (
            <Accordion 
              key={index}
              sx={{
                mb: 1,
                '&:before': { display: 'none' },
                boxShadow: 'none',
                border: '1px solid',
                borderColor: currentTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
              }}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Chip 
                  label={meaning.partOfSpeech} 
                  size="small" 
                  variant="outlined"
                  sx={{ mr: 1 }}
                />
                <Typography variant="body2">
                  {meaning.definitions[0]?.definition.substring(0, 50)}...
                </Typography>
              </AccordionSummary>
              
              <AccordionDetails>
                {meaning.definitions.slice(0, 3).map((def, defIndex) => (
                  <Box key={defIndex} sx={{ mb: 2 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      {defIndex + 1}. {def.definition}
                    </Typography>
                    
                    {def.example && (
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          fontStyle: 'italic', 
                          color: 'text.secondary',
                          pl: 2,
                          borderLeft: '2px solid',
                          borderLeftColor: 'primary.main',
                          mb: 1
                        }}
                      >
                        例句: {def.example}
                      </Typography>
                    )}
                    
                    {def.synonyms && def.synonyms.length > 0 && (
                      <Box sx={{ mb: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                          同义词: 
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                          {def.synonyms.slice(0, 5).map((synonym, synIndex) => (
                            <Chip 
                              key={synIndex}
                              label={synonym}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem', height: '20px' }}
                            />
                          ))}
                        </Box>
                      </Box>
                    )}
                    
                    {def.antonyms && def.antonyms.length > 0 && (
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          反义词: 
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                          {def.antonyms.slice(0, 5).map((antonym, antIndex) => (
                            <Chip 
                              key={antIndex}
                              label={antonym}
                              size="small"
                              variant="outlined"
                              color="secondary"
                              sx={{ fontSize: '0.7rem', height: '20px' }}
                            />
                          ))}
                        </Box>
                      </Box>
                    )}
                  </Box>
                ))}
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      )}
    </Box>
  );
};
