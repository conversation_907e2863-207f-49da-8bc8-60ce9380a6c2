// API callback
callback({
  "sourceLanguages": [
    {
      "language": "auto",
      "name": "Deteksi basa"
    },
    {
      "language": "af",
      "name": "Basa Afrikaans"
    },
    {
      "language": "sq",
      "name": "Basa Albania"
    },
    {
      "language": "am",
      "name": "Basa Amharik"
    },
    {
      "language": "ar",
      "name": "Basa Arab"
    },
    {
      "language": "hy",
      "name": "Basa Armenia"
    },
    {
      "language": "as",
      "name": "Basa Assam"
    },
    {
      "language": "ay",
      "name": "Basa Aymara"
    },
    {
      "language": "az",
      "name": "Basa Azerbaijan"
    },
    {
      "language": "bm",
      "name": "Basa Bambara"
    },
    {
      "language": "eu",
      "name": "Basa Basque"
    },
    {
      "language": "be",
      "name": "Basa Belarusia"
    },
    {
      "language": "bn",
      "name": "Basa Bengali"
    },
    {
      "language": "bho",
      "name": "Basa Bhojpuri"
    },
    {
      "language": "bs",
      "name": "Basa Bosnia"
    },
    {
      "language": "bg",
      "name": "Basa Bulgaria"
    },
    {
      "language": "ceb",
      "name": "Basa Cebuano"
    },
    {
      "language": "cs",
      "name": "Basa Ceko"
    },
    {
      "language": "ny",
      "name": "Basa Chichewa"
    },
    {
      "language": "zh-CN",
      "name": "Basa Cina"
    },
    {
      "language": "da",
      "name": "Basa Denmark"
    },
    {
      "language": "dv",
      "name": "Basa Divehi"
    },
    {
      "language": "doi",
      "name": "Basa Dogri"
    },
    {
      "language": "eo",
      "name": "Basa Esperanto"
    },
    {
      "language": "et",
      "name": "Basa Estonia"
    },
    {
      "language": "ee",
      "name": "Basa Ewe"
    },
    {
      "language": "fy",
      "name": "Basa Farisi"
    },
    {
      "language": "fi",
      "name": "Basa Finlandia"
    },
    {
      "language": "gl",
      "name": "Basa Galisia"
    },
    {
      "language": "ka",
      "name": "Basa Georgia"
    },
    {
      "language": "gn",
      "name": "Basa Guarani"
    },
    {
      "language": "gu",
      "name": "Basa Gujarat"
    },
    {
      "language": "ht",
      "name": "Basa Haiti Kreol"
    },
    {
      "language": "ha",
      "name": "Basa Hausa"
    },
    {
      "language": "haw",
      "name": "Basa Hawai"
    },
    {
      "language": "hmn",
      "name": "Basa Hmong"
    },
    {
      "language": "hu",
      "name": "Basa Hungaria"
    },
    {
      "language": "iw",
      "name": "Basa Ibrani"
    },
    {
      "language": "ig",
      "name": "Basa Igbo"
    },
    {
      "language": "ilo",
      "name": "Basa Iloko"
    },
    {
      "language": "hi",
      "name": "Basa India"
    },
    {
      "language": "id",
      "name": "Basa Indonesia"
    },
    {
      "language": "en",
      "name": "Basa Inggris"
    },
    {
      "language": "ga",
      "name": "Basa Irlandia"
    },
    {
      "language": "is",
      "name": "Basa Islandia"
    },
    {
      "language": "it",
      "name": "Basa Italia"
    },
    {
      "language": "jw",
      "name": "Basa Jawa"
    },
    {
      "language": "ja",
      "name": "Basa Jepang"
    },
    {
      "language": "de",
      "name": "Basa Jerman"
    },
    {
      "language": "kn",
      "name": "Basa Kannada"
    },
    {
      "language": "ca",
      "name": "Basa Katala"
    },
    {
      "language": "kk",
      "name": "Basa Kazakhstan"
    },
    {
      "language": "km",
      "name": "Basa Khmer"
    },
    {
      "language": "rw",
      "name": "Basa Kinyarwanda"
    },
    {
      "language": "ky",
      "name": "Basa Kirgiz"
    },
    {
      "language": "gom",
      "name": "Basa Konkani"
    },
    {
      "language": "ko",
      "name": "Basa Korea"
    },
    {
      "language": "co",
      "name": "Basa Korsika"
    },
    {
      "language": "kri",
      "name": "Basa Krio"
    },
    {
      "language": "hr",
      "name": "Basa Kroasia"
    },
    {
      "language": "ku",
      "name": "Basa Kurdi (Kurmanji)"
    },
    {
      "language": "ckb",
      "name": "Basa Kurdi (Sorani)"
    },
    {
      "language": "lo",
      "name": "Basa Laos"
    },
    {
      "language": "la",
      "name": "Basa Latin"
    },
    {
      "language": "lv",
      "name": "Basa Latvia"
    },
    {
      "language": "ln",
      "name": "Basa Lingala"
    },
    {
      "language": "lt",
      "name": "Basa Lituania"
    },
    {
      "language": "lg",
      "name": "Basa Luganda"
    },
    {
      "language": "lb",
      "name": "Basa Luksembourg"
    },
    {
      "language": "mai",
      "name": "Basa Maithili"
    },
    {
      "language": "mk",
      "name": "Basa Makedonia"
    },
    {
      "language": "mg",
      "name": "Basa Malagasi"
    },
    {
      "language": "ml",
      "name": "Basa Malayalam"
    },
    {
      "language": "ms",
      "name": "Basa Malaysia"
    },
    {
      "language": "mt",
      "name": "Basa Malta"
    },
    {
      "language": "mi",
      "name": "Basa Maori"
    },
    {
      "language": "mr",
      "name": "Basa Marathi"
    },
    {
      "language": "mni-Mtei",
      "name": "Basa Meiteilon (Manipuri)"
    },
    {
      "language": "lus",
      "name": "Basa Mizo"
    },
    {
      "language": "mn",
      "name": "Basa Mongol"
    },
    {
      "language": "my",
      "name": "Basa Myanmar (Burma)"
    },
    {
      "language": "ne",
      "name": "Basa Nepal"
    },
    {
      "language": "no",
      "name": "Basa Norwegia"
    },
    {
      "language": "or",
      "name": "Basa Odia (Oriya)"
    },
    {
      "language": "om",
      "name": "Basa Oromo"
    },
    {
      "language": "ps",
      "name": "Basa Pashto"
    },
    {
      "language": "fa",
      "name": "Basa Persia"
    },
    {
      "language": "pl",
      "name": "Basa Polandia"
    },
    {
      "language": "pt",
      "name": "Basa Portugis"
    },
    {
      "language": "fr",
      "name": "Basa Prancis"
    },
    {
      "language": "pa",
      "name": "Basa Punjabi"
    },
    {
      "language": "qu",
      "name": "Basa Quechua"
    },
    {
      "language": "ro",
      "name": "Basa Rumania"
    },
    {
      "language": "ru",
      "name": "Basa Rusia"
    },
    {
      "language": "sm",
      "name": "Basa Samoa"
    },
    {
      "language": "sa",
      "name": "Basa Sansekerta"
    },
    {
      "language": "gd",
      "name": "Basa Scots Gaelik"
    },
    {
      "language": "nso",
      "name": "Basa Sepedi"
    },
    {
      "language": "sr",
      "name": "Basa Serbia"
    },
    {
      "language": "st",
      "name": "Basa Sesotho"
    },
    {
      "language": "sn",
      "name": "Basa Shona"
    },
    {
      "language": "sd",
      "name": "Basa Sindhi"
    },
    {
      "language": "si",
      "name": "Basa Sinhala"
    },
    {
      "language": "sk",
      "name": "Basa Slovakia"
    },
    {
      "language": "sl",
      "name": "Basa Slovenia"
    },
    {
      "language": "so",
      "name": "Basa Somalia"
    },
    {
      "language": "es",
      "name": "Basa Spanyol"
    },
    {
      "language": "su",
      "name": "Basa Sunda"
    },
    {
      "language": "sw",
      "name": "Basa Swahili"
    },
    {
      "language": "sv",
      "name": "Basa Swedia"
    },
    {
      "language": "tl",
      "name": "Basa Tagalog"
    },
    {
      "language": "tg",
      "name": "Basa Tajik"
    },
    {
      "language": "ta",
      "name": "Basa Tamil"
    },
    {
      "language": "tt",
      "name": "Basa Tatar"
    },
    {
      "language": "te",
      "name": "Basa Telugu"
    },
    {
      "language": "th",
      "name": "Basa Thailand"
    },
    {
      "language": "ti",
      "name": "Basa Tigrinya"
    },
    {
      "language": "ts",
      "name": "Basa Tsonga"
    },
    {
      "language": "tr",
      "name": "Basa Turki"
    },
    {
      "language": "tk",
      "name": "Basa Turkmen"
    },
    {
      "language": "ak",
      "name": "Basa Twi"
    },
    {
      "language": "uk",
      "name": "Basa Ukrania"
    },
    {
      "language": "ur",
      "name": "Basa Urdu"
    },
    {
      "language": "ug",
      "name": "Basa Uyghur"
    },
    {
      "language": "uz",
      "name": "Basa Uzbekistan"
    },
    {
      "language": "vi",
      "name": "Basa Vietnam"
    },
    {
      "language": "nl",
      "name": "Basa Walanda"
    },
    {
      "language": "cy",
      "name": "Basa Wales"
    },
    {
      "language": "xh",
      "name": "Basa Xhosa"
    },
    {
      "language": "yi",
      "name": "Basa Yahudi"
    },
    {
      "language": "yo",
      "name": "Basa Yoruba"
    },
    {
      "language": "el",
      "name": "Basa Yunani"
    },
    {
      "language": "zu",
      "name": "Basa Zulu"
    }
  ],
  "targetLanguages": [
    {
      "language": "af",
      "name": "Basa Afrikaans"
    },
    {
      "language": "sq",
      "name": "Basa Albania"
    },
    {
      "language": "am",
      "name": "Basa Amharik"
    },
    {
      "language": "ar",
      "name": "Basa Arab"
    },
    {
      "language": "hy",
      "name": "Basa Armenia"
    },
    {
      "language": "as",
      "name": "Basa Assam"
    },
    {
      "language": "ay",
      "name": "Basa Aymara"
    },
    {
      "language": "az",
      "name": "Basa Azerbaijan"
    },
    {
      "language": "bm",
      "name": "Basa Bambara"
    },
    {
      "language": "eu",
      "name": "Basa Basque"
    },
    {
      "language": "be",
      "name": "Basa Belarusia"
    },
    {
      "language": "bn",
      "name": "Basa Bengali"
    },
    {
      "language": "bho",
      "name": "Basa Bhojpuri"
    },
    {
      "language": "bs",
      "name": "Basa Bosnia"
    },
    {
      "language": "bg",
      "name": "Basa Bulgaria"
    },
    {
      "language": "ceb",
      "name": "Basa Cebuano"
    },
    {
      "language": "cs",
      "name": "Basa Ceko"
    },
    {
      "language": "ny",
      "name": "Basa Chichewa"
    },
    {
      "language": "zh-CN",
      "name": "Basa Cina (Anyaran)"
    },
    {
      "language": "zh-TW",
      "name": "Basa Cina (Kuna)"
    },
    {
      "language": "da",
      "name": "Basa Denmark"
    },
    {
      "language": "dv",
      "name": "Basa Divehi"
    },
    {
      "language": "doi",
      "name": "Basa Dogri"
    },
    {
      "language": "eo",
      "name": "Basa Esperanto"
    },
    {
      "language": "et",
      "name": "Basa Estonia"
    },
    {
      "language": "ee",
      "name": "Basa Ewe"
    },
    {
      "language": "fy",
      "name": "Basa Farisi"
    },
    {
      "language": "fi",
      "name": "Basa Finlandia"
    },
    {
      "language": "gl",
      "name": "Basa Galisia"
    },
    {
      "language": "ka",
      "name": "Basa Georgia"
    },
    {
      "language": "gn",
      "name": "Basa Guarani"
    },
    {
      "language": "gu",
      "name": "Basa Gujarat"
    },
    {
      "language": "ht",
      "name": "Basa Haiti Kreol"
    },
    {
      "language": "ha",
      "name": "Basa Hausa"
    },
    {
      "language": "haw",
      "name": "Basa Hawai"
    },
    {
      "language": "hmn",
      "name": "Basa Hmong"
    },
    {
      "language": "hu",
      "name": "Basa Hungaria"
    },
    {
      "language": "iw",
      "name": "Basa Ibrani"
    },
    {
      "language": "ig",
      "name": "Basa Igbo"
    },
    {
      "language": "ilo",
      "name": "Basa Iloko"
    },
    {
      "language": "hi",
      "name": "Basa India"
    },
    {
      "language": "id",
      "name": "Basa Indonesia"
    },
    {
      "language": "en",
      "name": "Basa Inggris"
    },
    {
      "language": "ga",
      "name": "Basa Irlandia"
    },
    {
      "language": "is",
      "name": "Basa Islandia"
    },
    {
      "language": "it",
      "name": "Basa Italia"
    },
    {
      "language": "jw",
      "name": "Basa Jawa"
    },
    {
      "language": "ja",
      "name": "Basa Jepang"
    },
    {
      "language": "de",
      "name": "Basa Jerman"
    },
    {
      "language": "kn",
      "name": "Basa Kannada"
    },
    {
      "language": "ca",
      "name": "Basa Katala"
    },
    {
      "language": "kk",
      "name": "Basa Kazakhstan"
    },
    {
      "language": "km",
      "name": "Basa Khmer"
    },
    {
      "language": "rw",
      "name": "Basa Kinyarwanda"
    },
    {
      "language": "ky",
      "name": "Basa Kirgiz"
    },
    {
      "language": "gom",
      "name": "Basa Konkani"
    },
    {
      "language": "ko",
      "name": "Basa Korea"
    },
    {
      "language": "co",
      "name": "Basa Korsika"
    },
    {
      "language": "kri",
      "name": "Basa Krio"
    },
    {
      "language": "hr",
      "name": "Basa Kroasia"
    },
    {
      "language": "ku",
      "name": "Basa Kurdi (Kurmanji)"
    },
    {
      "language": "ckb",
      "name": "Basa Kurdi (Sorani)"
    },
    {
      "language": "lo",
      "name": "Basa Laos"
    },
    {
      "language": "la",
      "name": "Basa Latin"
    },
    {
      "language": "lv",
      "name": "Basa Latvia"
    },
    {
      "language": "ln",
      "name": "Basa Lingala"
    },
    {
      "language": "lt",
      "name": "Basa Lituania"
    },
    {
      "language": "lg",
      "name": "Basa Luganda"
    },
    {
      "language": "lb",
      "name": "Basa Luksembourg"
    },
    {
      "language": "mai",
      "name": "Basa Maithili"
    },
    {
      "language": "mk",
      "name": "Basa Makedonia"
    },
    {
      "language": "mg",
      "name": "Basa Malagasi"
    },
    {
      "language": "ml",
      "name": "Basa Malayalam"
    },
    {
      "language": "ms",
      "name": "Basa Malaysia"
    },
    {
      "language": "mt",
      "name": "Basa Malta"
    },
    {
      "language": "mi",
      "name": "Basa Maori"
    },
    {
      "language": "mr",
      "name": "Basa Marathi"
    },
    {
      "language": "mni-Mtei",
      "name": "Basa Meiteilon (Manipuri)"
    },
    {
      "language": "lus",
      "name": "Basa Mizo"
    },
    {
      "language": "mn",
      "name": "Basa Mongol"
    },
    {
      "language": "my",
      "name": "Basa Myanmar (Burma)"
    },
    {
      "language": "ne",
      "name": "Basa Nepal"
    },
    {
      "language": "no",
      "name": "Basa Norwegia"
    },
    {
      "language": "or",
      "name": "Basa Odia (Oriya)"
    },
    {
      "language": "om",
      "name": "Basa Oromo"
    },
    {
      "language": "ps",
      "name": "Basa Pashto"
    },
    {
      "language": "fa",
      "name": "Basa Persia"
    },
    {
      "language": "pl",
      "name": "Basa Polandia"
    },
    {
      "language": "pt",
      "name": "Basa Portugis"
    },
    {
      "language": "fr",
      "name": "Basa Prancis"
    },
    {
      "language": "pa",
      "name": "Basa Punjabi"
    },
    {
      "language": "qu",
      "name": "Basa Quechua"
    },
    {
      "language": "ro",
      "name": "Basa Rumania"
    },
    {
      "language": "ru",
      "name": "Basa Rusia"
    },
    {
      "language": "sm",
      "name": "Basa Samoa"
    },
    {
      "language": "sa",
      "name": "Basa Sansekerta"
    },
    {
      "language": "gd",
      "name": "Basa Scots Gaelik"
    },
    {
      "language": "nso",
      "name": "Basa Sepedi"
    },
    {
      "language": "sr",
      "name": "Basa Serbia"
    },
    {
      "language": "st",
      "name": "Basa Sesotho"
    },
    {
      "language": "sn",
      "name": "Basa Shona"
    },
    {
      "language": "sd",
      "name": "Basa Sindhi"
    },
    {
      "language": "si",
      "name": "Basa Sinhala"
    },
    {
      "language": "sk",
      "name": "Basa Slovakia"
    },
    {
      "language": "sl",
      "name": "Basa Slovenia"
    },
    {
      "language": "so",
      "name": "Basa Somalia"
    },
    {
      "language": "es",
      "name": "Basa Spanyol"
    },
    {
      "language": "su",
      "name": "Basa Sunda"
    },
    {
      "language": "sw",
      "name": "Basa Swahili"
    },
    {
      "language": "sv",
      "name": "Basa Swedia"
    },
    {
      "language": "tl",
      "name": "Basa Tagalog"
    },
    {
      "language": "tg",
      "name": "Basa Tajik"
    },
    {
      "language": "ta",
      "name": "Basa Tamil"
    },
    {
      "language": "tt",
      "name": "Basa Tatar"
    },
    {
      "language": "te",
      "name": "Basa Telugu"
    },
    {
      "language": "th",
      "name": "Basa Thailand"
    },
    {
      "language": "ti",
      "name": "Basa Tigrinya"
    },
    {
      "language": "ts",
      "name": "Basa Tsonga"
    },
    {
      "language": "tr",
      "name": "Basa Turki"
    },
    {
      "language": "tk",
      "name": "Basa Turkmen"
    },
    {
      "language": "ak",
      "name": "Basa Twi"
    },
    {
      "language": "uk",
      "name": "Basa Ukrania"
    },
    {
      "language": "ur",
      "name": "Basa Urdu"
    },
    {
      "language": "ug",
      "name": "Basa Uyghur"
    },
    {
      "language": "uz",
      "name": "Basa Uzbekistan"
    },
    {
      "language": "vi",
      "name": "Basa Vietnam"
    },
    {
      "language": "nl",
      "name": "Basa Walanda"
    },
    {
      "language": "cy",
      "name": "Basa Wales"
    },
    {
      "language": "xh",
      "name": "Basa Xhosa"
    },
    {
      "language": "yi",
      "name": "Basa Yahudi"
    },
    {
      "language": "yo",
      "name": "Basa Yoruba"
    },
    {
      "language": "el",
      "name": "Basa Yunani"
    },
    {
      "language": "zu",
      "name": "Basa Zulu"
    }
  ]
}
);