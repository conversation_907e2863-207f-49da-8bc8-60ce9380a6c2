import { useState, useEffect, useCallback, useRef } from 'react';

export interface TextSelection {
  text: string;
  position: { x: number; y: number };
  rect: DOMRect;
}

export interface UseTextSelectionOptions {
  minLength?: number;
  maxLength?: number;
  enableDoubleClick?: boolean;
  enableTripleClick?: boolean;
  buttonPosition?: 'TopLeft' | 'TopRight' | 'BottomLeft' | 'BottomRight';
  enabled?: boolean;
}

const defaultOptions: UseTextSelectionOptions = {
  minLength: 1,
  maxLength: 1000,
  enableDoubleClick: true,
  enableTripleClick: true,
  buttonPosition: 'TopRight',
  enabled: true,
};

export const useTextSelection = (
  onTextSelected: (selection: TextSelection) => void,
  options: UseTextSelectionOptions = {}
) => {
  const opts = { ...defaultOptions, ...options };
  const [currentSelection, setCurrentSelection] = useState<TextSelection | null>(null);
  const selectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSelectionRef = useRef<string>('');

  // 获取选中文本和位置信息
  const getSelectionInfo = useCallback((): TextSelection | null => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      return null;
    }

    const text = selection.toString().trim();
    
    // 检查文本长度
    if (text.length < opts.minLength! || text.length > opts.maxLength!) {
      return null;
    }

    // 检查是否是PDF.js环境，需要处理换行符
    const isPDFjs = window.location.href.includes('pdf') || 
                    document.querySelector('#viewer') !== null ||
                    document.querySelector('.page') !== null;
    
    const cleanText = isPDFjs ? text.replace(/\n/g, ' ') : text;

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    // 计算按钮位置
    let buttonX = rect.right + 10;
    let buttonY = rect.top;

    switch (opts.buttonPosition) {
      case 'TopLeft':
        buttonX = rect.left - 40;
        buttonY = rect.top - 40;
        break;
      case 'TopRight':
        buttonX = rect.right + 10;
        buttonY = rect.top - 10;
        break;
      case 'BottomLeft':
        buttonX = rect.left - 40;
        buttonY = rect.bottom + 10;
        break;
      case 'BottomRight':
        buttonX = rect.right + 10;
        buttonY = rect.bottom + 10;
        break;
    }

    // 确保按钮不会超出视窗边界
    const maxX = window.innerWidth - 50;
    const maxY = window.innerHeight - 50;
    
    buttonX = Math.max(10, Math.min(buttonX, maxX));
    buttonY = Math.max(10, Math.min(buttonY, maxY));

    return {
      text: cleanText,
      position: { x: buttonX, y: buttonY },
      rect,
    };
  }, [opts.minLength, opts.maxLength, opts.buttonPosition]);

  // 处理文本选择
  const handleTextSelection = useCallback((event?: MouseEvent) => {
    if (!opts.enabled) return;

    // 清除之前的定时器
    if (selectionTimeoutRef.current) {
      clearTimeout(selectionTimeoutRef.current);
    }

    // 延迟处理选择，确保选择操作完成
    selectionTimeoutRef.current = setTimeout(() => {
      const selectionInfo = getSelectionInfo();
      
      if (selectionInfo && selectionInfo.text !== lastSelectionRef.current) {
        lastSelectionRef.current = selectionInfo.text;
        setCurrentSelection(selectionInfo);
        onTextSelected(selectionInfo);
      } else if (!selectionInfo) {
        setCurrentSelection(null);
        lastSelectionRef.current = '';
      }
    }, 100);
  }, [opts.enabled, getSelectionInfo, onTextSelected]);

  // 处理双击选择
  const handleDoubleClick = useCallback((event: MouseEvent) => {
    if (!opts.enabled || !opts.enableDoubleClick) return;
    
    // 延迟处理，确保双击选择完成
    setTimeout(() => {
      handleTextSelection(event);
    }, 50);
  }, [opts.enabled, opts.enableDoubleClick, handleTextSelection]);

  // 处理三击选择
  const handleTripleClick = useCallback((event: MouseEvent) => {
    if (!opts.enabled || !opts.enableTripleClick) return;
    
    // 检查是否是三击
    if ((event as any).detail === 3) {
      setTimeout(() => {
        handleTextSelection(event);
      }, 50);
    }
  }, [opts.enabled, opts.enableTripleClick, handleTextSelection]);

  // 清除选择
  const clearSelection = useCallback(() => {
    setCurrentSelection(null);
    lastSelectionRef.current = '';
    
    // 清除浏览器选择
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
    }
  }, []);

  // 检查是否应该忽略选择（在某些元素内）
  const shouldIgnoreSelection = useCallback((target: EventTarget | null): boolean => {
    if (!target || !(target instanceof Element)) return false;

    // 忽略的元素类型
    const ignoredElements = [
      'input', 'textarea', 'select', 'button',
      'video', 'audio', 'canvas', 'svg'
    ];

    const tagName = target.tagName.toLowerCase();
    if (ignoredElements.includes(tagName)) return true;

    // 忽略可编辑元素
    if (target.isContentEditable) return true;

    // 忽略特定类名的元素
    const ignoredClasses = [
      'translator-sidebar',
      'translation-button',
      'edge-translate-button',
      'mui-dialog',
      'mui-popover',
      'mui-menu'
    ];

    for (const className of ignoredClasses) {
      if (target.classList.contains(className) || target.closest(`.${className}`)) {
        return true;
      }
    }

    return false;
  }, []);

  // 设置事件监听器
  useEffect(() => {
    if (!opts.enabled) return;

    const handleMouseUp = (event: MouseEvent) => {
      if (shouldIgnoreSelection(event.target)) return;
      handleTextSelection(event);
    };

    const handleDoubleClickEvent = (event: MouseEvent) => {
      if (shouldIgnoreSelection(event.target)) return;
      handleDoubleClick(event);
    };

    const handleClickEvent = (event: MouseEvent) => {
      if (shouldIgnoreSelection(event.target)) return;
      handleTripleClick(event);
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      // ESC键清除选择
      if (event.key === 'Escape') {
        clearSelection();
      }
    };

    // 监听滚动事件，隐藏翻译按钮
    const handleScroll = () => {
      if (currentSelection) {
        setCurrentSelection(null);
      }
    };

    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('dblclick', handleDoubleClickEvent);
    document.addEventListener('click', handleClickEvent);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('scroll', handleScroll, true);

    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('dblclick', handleDoubleClickEvent);
      document.removeEventListener('click', handleClickEvent);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('scroll', handleScroll, true);
      
      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current);
      }
    };
  }, [
    opts.enabled,
    shouldIgnoreSelection,
    handleTextSelection,
    handleDoubleClick,
    handleTripleClick,
    clearSelection,
    currentSelection
  ]);

  return {
    currentSelection,
    clearSelection,
  };
};
