import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../lib/supabase';

// GET /api/words/[id] - 获取单个生词详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id');
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const { data: word, error } = await supabase
      .from('user_words')
      .select('*')
      .eq('id', params.id)
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Word not found' }, { status: 404 });
      }
      console.error('Get word error:', error);
      return NextResponse.json({ error: 'Failed to fetch word' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: word });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/words/[id] - 更新生词
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    const body = await request.json();
    
    const {
      user_id,
      word,
      translation,
      source_language,
      target_language,
      context,
      pronunciation,
      part_of_speech,
      difficulty_level,
      review_count,
      last_reviewed_at,
    } = body;

    if (!user_id) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // 构建更新数据
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (word !== undefined) updateData.word = word.toLowerCase();
    if (translation !== undefined) updateData.translation = translation;
    if (source_language !== undefined) updateData.source_language = source_language;
    if (target_language !== undefined) updateData.target_language = target_language;
    if (context !== undefined) updateData.context = context;
    if (pronunciation !== undefined) updateData.pronunciation = pronunciation;
    if (part_of_speech !== undefined) updateData.part_of_speech = part_of_speech;
    if (difficulty_level !== undefined) updateData.difficulty_level = difficulty_level;
    if (review_count !== undefined) updateData.review_count = review_count;
    if (last_reviewed_at !== undefined) updateData.last_reviewed_at = last_reviewed_at;

    // 更新生词（确保只能更新自己的生词）
    const { data: updatedWord, error } = await supabase
      .from('user_words')
      .update(updateData)
      .eq('id', params.id)
      .eq('user_id', user_id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Word not found' }, { status: 404 });
      }
      console.error('Update word error:', error);
      return NextResponse.json({ error: 'Failed to update word' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: updatedWord });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/words/[id] - 删除单个生词
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // 删除生词（确保只能删除自己的生词）
    const { error } = await supabase
      .from('user_words')
      .delete()
      .eq('id', params.id)
      .eq('user_id', userId);

    if (error) {
      console.error('Delete word error:', error);
      return NextResponse.json({ error: 'Failed to delete word' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Word deleted successfully' });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/words/[id]/review - 更新复习记录
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createClient();
    const body = await request.json();
    
    const { user_id, difficulty_adjustment = 0 } = body;

    if (!user_id) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // 获取当前生词信息
    const { data: currentWord, error: fetchError } = await supabase
      .from('user_words')
      .select('review_count, difficulty_level')
      .eq('id', params.id)
      .eq('user_id', user_id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Word not found' }, { status: 404 });
      }
      console.error('Fetch word error:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch word' }, { status: 500 });
    }

    // 更新复习记录
    const newReviewCount = currentWord.review_count + 1;
    const newDifficultyLevel = Math.max(1, Math.min(5, 
      currentWord.difficulty_level + difficulty_adjustment
    ));

    const { data: updatedWord, error } = await supabase
      .from('user_words')
      .update({
        review_count: newReviewCount,
        difficulty_level: newDifficultyLevel,
        last_reviewed_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .eq('user_id', user_id)
      .select()
      .single();

    if (error) {
      console.error('Update review error:', error);
      return NextResponse.json({ error: 'Failed to update review' }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: updatedWord });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
