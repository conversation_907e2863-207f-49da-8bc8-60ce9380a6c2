import { useEffect, useCallback, useRef } from 'react';

export interface HotkeyConfig {
  key: string;
  modifiers: ('ctrl' | 'alt' | 'shift' | 'meta')[];
  handler: (event: KeyboardEvent) => void;
  preventDefault?: boolean;
  stopPropagation?: boolean;
  enabled?: boolean;
  description?: string;
  ignoreInputs?: boolean; // 是否在输入框中忽略快捷键
}

export interface HotkeyMatch {
  key: string;
  ctrl: boolean;
  alt: boolean;
  shift: boolean;
  meta: boolean;
}

// 检查快捷键是否匹配
const matchesHotkey = (event: KeyboardEvent, config: HotkeyConfig): boolean => {
  const { key, modifiers } = config;
  
  // 检查主键
  if (event.key.toLowerCase() !== key.toLowerCase()) {
    return false;
  }
  
  // 检查修饰键
  const requiredModifiers = {
    ctrl: modifiers.includes('ctrl'),
    alt: modifiers.includes('alt'),
    shift: modifiers.includes('shift'),
    meta: modifiers.includes('meta'),
  };
  
  return (
    event.ctrlKey === requiredModifiers.ctrl &&
    event.altKey === requiredModifiers.alt &&
    event.shiftKey === requiredModifiers.shift &&
    event.metaKey === requiredModifiers.meta
  );
};

// 检查是否在输入元素中
const isInInputElement = (): boolean => {
  const activeElement = document.activeElement;
  
  if (!activeElement) return false;
  
  const tagName = activeElement.tagName.toLowerCase();
  const isInput = tagName === 'input' || tagName === 'textarea';
  const isContentEditable = activeElement.getAttribute('contenteditable') === 'true';
  const isDesignMode = document.designMode === 'on';
  
  return isInput || isContentEditable || isDesignMode;
};

// 格式化快捷键显示
export const formatHotkey = (config: HotkeyConfig): string => {
  const { key, modifiers } = config;
  const parts: string[] = [];
  
  if (modifiers.includes('ctrl')) parts.push('Ctrl');
  if (modifiers.includes('alt')) parts.push('Alt');
  if (modifiers.includes('shift')) parts.push('Shift');
  if (modifiers.includes('meta')) parts.push('Cmd');
  
  parts.push(key.toUpperCase());
  
  return parts.join(' + ');
};

// 预定义的常用快捷键
export const commonHotkeys = {
  TOGGLE_SIDEBAR: {
    key: 'q',
    modifiers: ['alt'] as const,
    description: '显示/隐藏翻译侧边栏',
  },
  COPY_TRANSLATION: {
    key: 'c',
    modifiers: ['ctrl', 'shift'] as const,
    description: '复制翻译结果',
  },
  NEXT_TAB: {
    key: 'Tab',
    modifiers: ['ctrl'] as const,
    description: '切换到下一个翻译引擎',
  },
  PREV_TAB: {
    key: 'Tab',
    modifiers: ['ctrl', 'shift'] as const,
    description: '切换到上一个翻译引擎',
  },
  TOGGLE_PIN: {
    key: 'p',
    modifiers: ['ctrl', 'shift'] as const,
    description: '钉住/取消钉住侧边栏',
  },
  TOGGLE_THEME: {
    key: 't',
    modifiers: ['ctrl', 'shift'] as const,
    description: '切换深浅色主题',
  },
};

// 快捷键Hook
export const useHotkeys = (
  configs: HotkeyConfig[],
  dependencies: React.DependencyList = []
) => {
  const configsRef = useRef<HotkeyConfig[]>(configs);
  
  // 更新配置引用
  useEffect(() => {
    configsRef.current = configs;
  }, [configs]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const currentConfigs = configsRef.current;
    
    for (const config of currentConfigs) {
      // 检查是否启用
      if (config.enabled === false) continue;
      
      // 检查是否在输入元素中且需要忽略
      if (config.ignoreInputs !== false && isInInputElement()) continue;
      
      // 检查快捷键是否匹配
      if (matchesHotkey(event, config)) {
        // 阻止默认行为
        if (config.preventDefault !== false) {
          event.preventDefault();
        }
        
        // 阻止事件冒泡
        if (config.stopPropagation !== false) {
          event.stopPropagation();
        }
        
        // 执行处理函数
        try {
          config.handler(event);
        } catch (error) {
          console.error('Hotkey handler error:', error);
        }
        
        // 只处理第一个匹配的快捷键
        break;
      }
    }
  }, []);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, ...dependencies]);

  return {
    formatHotkey,
    isInInputElement,
  };
};

// 单个快捷键Hook
export const useHotkey = (
  key: string,
  modifiers: ('ctrl' | 'alt' | 'shift' | 'meta')[],
  handler: (event: KeyboardEvent) => void,
  options: Omit<HotkeyConfig, 'key' | 'modifiers' | 'handler'> = {}
) => {
  const config: HotkeyConfig = {
    key,
    modifiers,
    handler,
    ...options,
  };
  
  return useHotkeys([config]);
};

// 快捷键管理器类
export class HotkeyManager {
  private static instance: HotkeyManager;
  private registeredHotkeys: Map<string, HotkeyConfig> = new Map();
  private conflictResolver?: (conflicts: HotkeyConfig[]) => HotkeyConfig | null;

  static getInstance(): HotkeyManager {
    if (!HotkeyManager.instance) {
      HotkeyManager.instance = new HotkeyManager();
    }
    return HotkeyManager.instance;
  }

  // 注册快捷键
  register(id: string, config: HotkeyConfig): boolean {
    const conflicts = this.detectConflicts(config);
    
    if (conflicts.length > 0) {
      console.warn(`Hotkey conflict detected for ${formatHotkey(config)}:`, conflicts);
      
      if (this.conflictResolver) {
        const resolved = this.conflictResolver([config, ...conflicts]);
        if (resolved) {
          this.registeredHotkeys.set(id, resolved);
          return true;
        }
      }
      
      return false;
    }
    
    this.registeredHotkeys.set(id, config);
    return true;
  }

  // 注销快捷键
  unregister(id: string): boolean {
    return this.registeredHotkeys.delete(id);
  }

  // 获取所有注册的快捷键
  getAll(): Map<string, HotkeyConfig> {
    return new Map(this.registeredHotkeys);
  }

  // 检测冲突
  private detectConflicts(config: HotkeyConfig): HotkeyConfig[] {
    const conflicts: HotkeyConfig[] = [];
    
    for (const [, existingConfig] of this.registeredHotkeys) {
      if (this.configsMatch(config, existingConfig)) {
        conflicts.push(existingConfig);
      }
    }
    
    return conflicts;
  }

  // 检查两个配置是否匹配
  private configsMatch(config1: HotkeyConfig, config2: HotkeyConfig): boolean {
    if (config1.key.toLowerCase() !== config2.key.toLowerCase()) {
      return false;
    }
    
    const modifiers1 = new Set(config1.modifiers);
    const modifiers2 = new Set(config2.modifiers);
    
    if (modifiers1.size !== modifiers2.size) {
      return false;
    }
    
    for (const modifier of modifiers1) {
      if (!modifiers2.has(modifier)) {
        return false;
      }
    }
    
    return true;
  }

  // 设置冲突解决器
  setConflictResolver(resolver: (conflicts: HotkeyConfig[]) => HotkeyConfig | null) {
    this.conflictResolver = resolver;
  }
}
