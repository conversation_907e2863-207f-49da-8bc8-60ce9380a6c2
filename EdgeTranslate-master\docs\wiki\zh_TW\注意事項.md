## 側邊翻譯 Q & A

* __Q: 這個插件有沒有xxx功能? / 這個插件為什麼不能xxx?__
  
  __A:__ 本插件的詳細功能列表請看[這裡](./插件介紹.md)；

* __Q: 我在使用過程中遇到了問題，該怎麼辦？__

  __A:__ 請給我們發郵件([nickyc975](mailto:<EMAIL>), [<PERSON>](mailto:<EMAIL>))或在本插件的github倉庫中給我們提issue；

* __Q: 插件安裝/更新後無法使用？__

  __A:__ 在安裝或更新插件後，請刷新需要翻譯的頁面以（重新）啟用翻譯功能；

* __Q: 選中文本後翻譯按鈕不彈出？__

  __A:__ 你需要在設置中選中`啟用劃詞翻譯`選項並刷新需要翻譯的頁面；

* __Q: 如何在PDF文件中啟用劃詞翻譯和雙擊翻譯？__

  __A:__ 首先你需要授予本插件`允許訪問文件網址`的權限， __圖一__ 給出了授予方法。然後請在本插件的設置中選中`使用內置的PDF查看器`並刷新需要翻譯的PDF文件。

* __Q: 翻譯結果老是彈出收回影響閱讀怎麼辦？__

  __A:__ 點擊翻譯結果展示框右上角的圖釘按鈕即可固定展示框。

* __Q:為什麼我不能安裝從“Releases”頁面下載的* .crx檔案？__

  __A:__ 如果你的錯誤資訊是“``無法從該網站添加應用、擴展程式和用戶腳本``”，可以嘗試以下步驟：

  1. 在你的chrome裏打開url: ``chrome://flags/#extensions-on-chrome-urls``。

  2. 如果``Extensions on chrome:// URLs``是關閉的，請打開這個開關並且重啓chrome。

  3. 打開這個url: ``chrome://extensions/``。

  4. 請確認``開發者模式``是打開的。如果這個開關是關閉的，請打開開關並且重繪這個頁面。

  5. 現在，您可以嘗試將* .crx檔案再次拖放到此頁面上。

* __Q:翻譯框為什麼變成懸浮的了？怎麼讓它變回右側（左側）固定？__

  __A:__ 這是側邊翻譯2.0引入的新功能，即支持懸浮框顯示。當你將滑鼠光標移動到翻譯框頂部的藍色區域時，按住滑鼠左鍵即可拖動翻譯框。

  當翻譯框固定在側面時，拖動翻譯框可以讓它變成懸浮的。

  如果你想讓它變回側邊固定，請__向右側（左側）拖動翻譯框直到滑鼠光標到達邊界__，即可讓它變回側邊固定。

  參考圖二的演示（gif文件較大，請耐心等待）。

* __Q:翻譯框超出介面導致我看不見翻譯結果怎麼辦？__

  如果懸浮的翻譯框超出了頁面並且拖不回來，可以嘗試改變頁面縮放使它回到頁面內，具體操作為：按住Ctrl鍵同時滾動滑鼠滾輪，即可調整頁面縮放。

### 圖一：允許側邊翻譯訪問文件鏈接

![grant_access](../../images/grant_access.gif)

### 圖二：在懸浮模式與貼邊模式之間轉換

![floating_fixed_switch](../../images/floating_fixed_switch.gif)