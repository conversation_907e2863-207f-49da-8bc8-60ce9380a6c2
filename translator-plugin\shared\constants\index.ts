// API相关常量
export const API_ENDPOINTS = {
  TRANSLATE: '/api/translate',
  MODELS: '/api/models',
  USERS: '/api/users',
  USAGE: '/api/usage',
  LANGUAGES: '/api/languages',
  AUTH: '/api/auth',
} as const;

// 消息类型常量
export const MESSAGE_TYPES = {
  TRANSLATE_TEXT: 'TRANSLATE_TEXT',
  OCR_TEXT: 'OCR_TEXT',
  UPDATE_SETTINGS: 'UPDATE_SETTINGS',
  GET_SETTINGS: 'GET_SETTINGS',
  SYNC_DATA: 'SYNC_DATA',
  SHOW_SIDEBAR: 'SHOW_SIDEBAR',
  HIDE_SIDEBAR: 'HIDE_SIDEBAR',
  TOGGLE_SIDEBAR: 'TOGGLE_SIDEBAR',
} as const;

// 存储键常量
export const STORAGE_KEYS = {
  USER_SETTINGS: 'user_settings',
  TRANSLATION_CACHE: 'translation_cache',
  USER_INFO: 'user_info',
  LAST_SYNC: 'last_sync',
  API_CACHE: 'api_cache',
} as const;

// 默认设置
export const DEFAULT_SETTINGS = {
  source_language: 'auto',
  target_language: 'zh',
  theme: 'system' as const,
  auto_translate: true,
  show_pronunciation: true,
  show_definitions: false,
  show_examples: false,
  sidebar_position: 'right' as const,
  hotkey_enabled: true,
  hotkey_combination: 'Alt+S',
};

// UI常量
export const UI_CONSTANTS = {
  SIDEBAR_WIDTH: 260,
  SIDEBAR_MAX_HEIGHT: 400,
  SIDEBAR_MIN_HEIGHT: 200,
  TRIGGER_DELAY: 400,
  ANIMATION_DURATION: 120,
  BORDER_RADIUS: 8,
  SHADOW_ELEVATION: 4,
} as const;

// 性能常量
export const PERFORMANCE_LIMITS = {
  MAX_TEXT_LENGTH: 1000,
  MAX_CACHE_SIZE: 100,
  MAX_RETRY_ATTEMPTS: 3,
  REQUEST_TIMEOUT: 10000,
  DEBOUNCE_DELAY: 300,
} as const;

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  { code: 'auto', name: '自动检测', native_name: 'Auto Detect' },
  { code: 'en', name: '英语', native_name: 'English' },
  { code: 'zh', name: '中文', native_name: '中文' },
  { code: 'zh-cn', name: '简体中文', native_name: '简体中文' },
  { code: 'zh-tw', name: '繁体中文', native_name: '繁體中文' },
  { code: 'ja', name: '日语', native_name: '日本語' },
  { code: 'ko', name: '韩语', native_name: '한국어' },
  { code: 'fr', name: '法语', native_name: 'Français' },
  { code: 'de', name: '德语', native_name: 'Deutsch' },
  { code: 'es', name: '西班牙语', native_name: 'Español' },
  { code: 'ru', name: '俄语', native_name: 'Русский' },
  { code: 'it', name: '意大利语', native_name: 'Italiano' },
  { code: 'pt', name: '葡萄牙语', native_name: 'Português' },
  { code: 'ar', name: '阿拉伯语', native_name: 'العربية' },
  { code: 'hi', name: '印地语', native_name: 'हिन्दी' },
  { code: 'th', name: '泰语', native_name: 'ไทย' },
  { code: 'vi', name: '越南语', native_name: 'Tiếng Việt' },
] as const;

// OCR支持的语言
export const OCR_SUPPORTED_LANGUAGES = [
  'eng', 'chi_sim', 'chi_tra', 'jpn', 'kor', 'fra', 'deu', 'spa', 'rus'
] as const;

// 错误代码
export const ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  API_ERROR: 'API_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  PREMIUM_REQUIRED: 'PREMIUM_REQUIRED',
  INVALID_INPUT: 'INVALID_INPUT',
  OCR_FAILED: 'OCR_FAILED',
  TRANSLATION_FAILED: 'TRANSLATION_FAILED',
  STORAGE_ERROR: 'STORAGE_ERROR',
} as const;

// 主题颜色
export const THEME_COLORS = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  error: '#d32f2f',
  info: '#0288d1',
  background: {
    light: '#ffffff',
    dark: '#121212',
  },
  text: {
    light: '#000000',
    dark: '#ffffff',
  },
  border: {
    light: '#e0e0e0',
    dark: '#424242',
  },
} as const;

// 快捷键组合
export const HOTKEY_COMBINATIONS = [
  'Alt+S',
  'Ctrl+Shift+T',
  'Alt+T',
  'Ctrl+Alt+T',
  'Shift+Alt+T',
] as const;

// 浏览器兼容性
export const BROWSER_SUPPORT = {
  CHROME: { min_version: 88, manifest_version: 3 },
  FIREFOX: { min_version: 109, manifest_version: 2 },
  EDGE: { min_version: 88, manifest_version: 3 },
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  TRANSLATION_TTL: 24 * 60 * 60 * 1000, // 24小时
  API_CACHE_TTL: 5 * 60 * 1000, // 5分钟
  USER_SETTINGS_TTL: 7 * 24 * 60 * 60 * 1000, // 7天
} as const;
