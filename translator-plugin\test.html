<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译插件测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-text {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>🌐 智能翻译助手测试页面</h1>
    
    <div class="instructions">
        <h3>📋 测试说明</h3>
        <ol>
            <li><strong>划词翻译测试</strong>：选中下面的文本，等待400ms后应该出现翻译侧边栏</li>
            <li><strong>右键菜单测试</strong>：选中文本后右键，应该看到"翻译选中文本"选项</li>
            <li><strong>截图翻译测试</strong>：右键空白处，应该看到"截图翻译"选项</li>
            <li><strong>插件弹窗测试</strong>：点击浏览器工具栏的插件图标，应该显示控制面板</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🇺🇸 英文测试文本</h2>
        <div class="test-text">
            <p><strong>测试1：</strong>Hello, this is a test sentence for translation. The intelligent translation assistant should be able to translate this text into Chinese automatically.</p>
        </div>
        <div class="test-text">
            <p><strong>测试2：</strong>Machine learning and artificial intelligence are transforming the way we interact with technology. These advanced systems can process natural language and provide accurate translations in real-time.</p>
        </div>
        <div class="test-text">
            <p><strong>测试3：</strong>Good morning! How are you today?</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🇯🇵 日文测试文本</h2>
        <div class="test-text">
            <p>こんにちは、これは翻訳のためのテスト文章です。人工知能翻訳アシスタントがこのテキストを中国語に翻訳できるはずです。</p>
        </div>
        <div class="test-text">
            <p>機械学習と人工知能は、私たちがテクノロジーと相互作用する方法を変革しています。これらの高度なシステムは自然言語を処理し、リアルタイムで正確な翻訳を提供できます。</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🇰🇷 韩文测试文本</h2>
        <div class="test-text">
            <p>안녕하세요, 이것은 번역을 위한 테스트 문장입니다. 지능형 번역 도우미가 이 텍스트를 중국어로 자동 번역할 수 있어야 합니다.</p>
        </div>
        <div class="test-text">
            <p>기계 학습과 인공 지능은 우리가 기술과 상호 작용하는 방식을 변화시키고 있습니다. 이러한 고급 시스템은 자연어를 처리하고 실시간으로 정확한 번역을 제공할 수 있습니다.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🇫🇷 法文测试文本</h2>
        <div class="test-text">
            <p>Bonjour, ceci est une phrase de test pour la traduction. L'assistant de traduction intelligent devrait être capable de traduire ce texte en chinois automatiquement.</p>
        </div>
        <div class="test-text">
            <p>L'apprentissage automatique et l'intelligence artificielle transforment la façon dont nous interagissons avec la technologie. Ces systèmes avancés peuvent traiter le langage naturel et fournir des traductions précises en temps réel.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 功能测试区域</h2>
        <div class="instructions">
            <h4>测试步骤：</h4>
            <ol>
                <li>选中任意上方文本，等待翻译侧边栏出现</li>
                <li>右键选中的文本，点击"翻译选中文本"</li>
                <li>右键空白处，点击"截图翻译"，然后拖拽选择区域</li>
                <li>点击插件图标，查看弹窗是否正常显示</li>
            </ol>
        </div>
        
        <div class="test-text">
            <p><strong>混合语言测试：</strong>This is English text mixed with 中文文本 and some 日本語テキスト for comprehensive testing.</p>
        </div>
        
        <div class="test-text">
            <p><strong>长文本测试：</strong>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 调试信息</h2>
        <div class="instructions">
            <p><strong>如果功能不正常，请检查：</strong></p>
            <ul>
                <li>浏览器开发者工具的Console面板是否有错误信息</li>
                <li>插件是否已正确安装并启用</li>
                <li>插件权限是否已授予</li>
                <li>网络连接是否正常（翻译需要网络）</li>
            </ul>
        </div>
    </div>

    <script>
        // 添加一些调试信息
        console.log('翻译插件测试页面已加载');
        console.log('当前时间:', new Date().toLocaleString());
        
        // 检测插件是否存在
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            console.log('Chrome扩展API可用');
        } else {
            console.warn('Chrome扩展API不可用，请确保在Chrome浏览器中打开此页面');
        }
        
        // 监听选择事件
        document.addEventListener('mouseup', function() {
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                console.log('文本已选中:', selection.toString().trim());
            }
        });
    </script>
</body>
</html>
