{"name": "__MSG_AppName__", "description": "__MSG_Description__", "version": "2.4.3", "default_locale": "en", "background": {"scripts": ["./background/background.js"]}, "browser_action": {"default_popup": "./popup/popup.html", "default_icon": {"16": "icon/icon16.png", "48": "icon/icon48.png", "128": "icon/icon128.png"}}, "commands": {"_execute_browser_action": {"suggested_key": {"default": "Alt+Q"}}, "change_language_setting": {"description": "__MSG_ChangeLanguageSetting__"}, "translate_selected": {"description": "__MSG_TranslateSelectedText__"}, "fix_result_frame": {"suggested_key": {"default": "Alt+X"}, "description": "__MSG_FixResultFrame__"}, "close_result_frame": {"suggested_key": {"default": "Alt+C"}, "description": "__MSG_CloseResultFrame__"}, "exchange_source_target_lang": {"suggested_key": {"default": "Alt+S"}, "description": "__MSG_ExchangeSourceAndTargetLanguage__"}, "pronounce_selected": {"description": "__MSG_PronounceSelected__"}, "pronounce_original": {"description": "__MSG_PronounceOriginal__"}, "pronounce_translated": {"description": "__MSG_PronounceTranslated__"}, "copy_result": {"description": "__MS<PERSON>_CopyResult__"}, "change_mutual_translate": {"description": "__MSG_MutualTranslation__"}, "translate_page": {"description": "__MSG_TranslatePage__"}, "cancel_page_translate": {"description": "__MSG_CancelPageTranslate__"}, "toggle_page_translate_banner": {"description": "__MSG_TogglePageTranslateBanner__"}}, "web_accessible_resources": ["**/*.css", "**/*.png", "google/*"], "icons": {"16": "icon/icon16.png", "48": "icon/icon48.png", "128": "icon/icon128.png"}, "options_ui": {"page": "./options/options.html", "open_in_tab": true, "chrome_style": false}, "manifest_version": 2}