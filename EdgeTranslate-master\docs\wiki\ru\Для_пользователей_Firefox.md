## Приветствую пользователей Firefox

Я один из разработчиков Edge Translate. В основном за управление Edge Translate в браузере Firefox я отвечаю.

Прежде всего, хочу сказать спасибо за вашу любовь к Edge Translate. Спасибо за вашу поддержку, Edge Translate сегодня такой благодаря вам. Огромное спасибо!

Но мне жаль сообщать вам, что мы должны удалить функции, связанные с веб-переводом, в Edge Translate в браузере Firefox (версии на других платформах не затрагиваются). Нам жаль это делать, но у нас нет выбора.

Если говорить по простому, для реализации функции перевода веб-страниц нам необходимо использовать компоненты перевода веб-страниц Youdao и Google, но эти компоненты являются закрытыми, и мы не можем получить их исходный код. Теперь Firefox требует, чтобы мы предоставили исходный код для всех сторонних компонентов, которые мы используем, иначе наши расширения будут удалены из магазина. Проверяющие предположили, что мы можем поискать реализации с открытым исходным кодом, но мы не смогли их найти, поэтому чтобы пройти проверку, мы должны удалить эту функцию.

Это расширение является работой, которой мы занимались в свободное время, в которую вложено много нашего тяжелого труда. Нас очень огорчает необходимость удалить эту тяжело реализованную функцию, а также множество проблем самого Firefox (например, отсутствие возможности использования расширения, встроенного в программу чтения PDF-файлов, из-за чего функция формулировки PDF в Edge Translate стала недействительной; добавление кнопок в уведомления, из-за чего мы тратим дополнительное время на обеспечение совместимости и т. д.), это приводит к дополнительным затратам на разработку, мы не можем добавлять новые функции в Edge Translate в Firefox. Мы настоятельно рекомендуем пользователям, которым нравится Edge Translate, попробовать использовать браузеры на основе Chromium, например Chrome, новую версию Edge или Chromium с открытым исходным кодом.

Если вам действительно нужна функция веб-перевода, можете рассмотреть следующие два варианта:

1. По возможности, мы предоставим пакеты с бета-подписью, которые можно загрузить и установить напрямую. Действия:
   
   1. Откройте [Releases](https://github.com/EdgeTranslate/EdgeTranslate/releases/latest).
   
   2. Если вы найдете пакет с примерно таким названием `EdgeTranslate_vx.x.x.x_firefox.xpi` (__4__ – номер версии) в параметрах загрузки внизу страницы, тогда просто скачайте и установите его.
   
   3. Если вы не находите такоой пакет, значит у нас не получилось предоставить такой вид пакетов. Вам нужно перейти к следующему варианту.

2. Вручную загрузите и установите неподписанную полную версию, выполнив следующие действия:
   
   1. Скачайте и установите [версию ESR](https://www.mozilla.org/ru/firefox/enterprise/), [версию Developer](https://www.mozilla.org/ru/firefox/developer/) или [версию Nightly](https://www.mozilla.org/ru/firefox/channel/desktop/#nightly) Firefox. Вы можете выбрать любую, но рекомендется такой приоритет ESR > Developer > Nightly.
   
   2. Загрузите последнюю версию с переводчика страниц Edge Translate для Firefox из [Releases](https://github.com/EdgeTranslate/EdgeTranslate/releases/latest), название пакета в формате `EdgeTranslate_vx.x.x_firefox.zip`.
   
   3. Откройте радактор настроек Firefox (Перейдите к `about:config`), Найдите `xpinstall.signatures.required` и измените значение на `false` (Нажмите на стрелку в конце, чтобы переключиться).
   
   4. Откройте страницу управления дополнениями Firefox (Перейдите к `about:addons`). Нажмите меню настроек в правом верхнем углу. Выберите `Установить дополнение из файла...` и выберите скачанный пакет Edge Translate.

Спасибо, что продолжаете поддерживать!
