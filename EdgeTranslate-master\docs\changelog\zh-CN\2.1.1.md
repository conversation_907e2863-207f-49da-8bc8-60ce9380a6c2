### 注意

* 关于火狐浏览器安装zip格式的扩展包请参考[这里](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/zh_CN/%E8%87%B4%E7%81%AB%E7%8B%90%E7%94%A8%E6%88%B7.md)！

* 为了进一步改善翻译体验，我们建立了侧边翻译用户交流群，欢迎大家加入：[侧边翻译用户交流QQ群](https://jq.qq.com/?_wv=1027&k=gT5EYfFB)

### 优化

* 优化腾讯翻译的请求速度；

* 优化翻译结果中的原文展示策略，当原文过长时自动折叠，避免其影响阅读，点击原文可展开；

* 优化侧边栏弹出失败时的处理策略，当当前页面无法展示翻译结果时，跳转到一个新页面展示结果，并在页面上给出必要的说明信息；

### 修复

* 修复侧边栏对溢出文本进行换行时将单词断开的问题；

* 修复在某些黑色页面或黑色模式下，侧边栏字体变成白色导致内容不可见的问题(#125)；

* 修复黑名单标识在Mac OS下显示为问号的问题；

* 修复用于换行的\</br\>标签被转义导致换行失效的问题；

* 修复第一次发音语速为慢速的问题；

* 修复百度和腾讯翻译多行句子时结果中只显示第一行的问题；

* 修复单词定义中没有例句时仍然显示“例句”标题的问题；

* 修复腾讯翻译结果中自带的HTML标签字符被转义的问题；

* 修复某些情况下百度翻译结果解析错误的问题；

### 关于打赏

开发这个项目花费了我们许多的时间和精力，如果你真的觉得这个项目对你有帮助，不妨请我们喝罐可乐，支持我们继续做下去！

当然，这 __纯属自愿__，打赏并不能获得什么优待，不打赏也不会有任何影响，请量力而为！

| 微信 | 支付宝 |
| :-: | :-: |
| <img src="https://user-images.githubusercontent.com/25877145/80864662-b6617c00-8cb6-11ea-915a-582ca046118c.png" height=200 alt="微信支付"/> | <img src="https://user-images.githubusercontent.com/25877145/80864685-ced19680-8cb6-11ea-94e5-f5ca8e4389b9.jpg" height=200 alt="支付宝支付"/> |