import React from 'react';
import ReactDOM from 'react-dom/client';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import {
  Box,
  Typography,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  Divider,
  IconButton,
  Chip,
  Card,
  CardContent,
  Tabs,
  Tab
} from '@mui/material';
import {
  Translate as TranslateIcon,
  Settings as SettingsIcon,
  CameraAlt as CameraAltIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  BookmarkBorder as BookmarkIcon,
  History as HistoryIcon,
  Language as LanguageIcon
} from '@mui/icons-material';
import { createMuiTheme } from '../../shared/theme/themeConfig';
import { useThemeStore } from '../../shared/stores/useThemeStore';
import { useSidebarStore } from '../../shared/stores/useSidebarStore';

import './style.css';

// Chrome API类型声明
declare const chrome: any;

// 现代化Popup组件
function ModernPopup() {
  const { currentTheme, toggleTheme } = useThemeStore();
  const { isVisible, toggleVisibility } = useSidebarStore();
  const [enabled, setEnabled] = React.useState(true);
  const [message, setMessage] = React.useState<string | null>(null);
  const [activeTab, setActiveTab] = React.useState(0);

  // 创建动态主题
  const theme = React.useMemo(() => createMuiTheme(currentTheme), [currentTheme]);

  React.useEffect(() => {
    console.log('Modern Popup component mounted');
    // 加载用户设置
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const result = await chrome.storage.local.get(['enabled']);
      setEnabled(result.enabled !== false);
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  };

  const saveSettings = async (newEnabled: boolean) => {
    try {
      await chrome.storage.local.set({ enabled: newEnabled });
      setEnabled(newEnabled);
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  const handleToggleSidebar = async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab?.id) {
        await chrome.tabs.sendMessage(tab.id, {
          type: 'TOGGLE_SIDEBAR'
        });
        setMessage(isVisible ? '侧边栏已隐藏' : '侧边栏已显示');
        setTimeout(() => setMessage(null), 2000);
      }
    } catch (error) {
      console.error('Toggle sidebar failed:', error);
      setMessage('操作失败，请确保在网页中使用');
    }
  };

  return (
    <Box sx={{
      width: 360,
      height: 500,
      background: currentTheme === 'dark'
        ? 'linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%)'
        : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      borderRadius: '12px',
      overflow: 'hidden'
    }}>
      {/* 现代化标题栏 */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        p: 2,
        background: currentTheme === 'dark'
          ? 'rgba(66, 66, 66, 0.8)'
          : 'rgba(25, 118, 210, 0.9)',
        color: 'white',
        backdropFilter: 'blur(10px)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TranslateIcon sx={{ mr: 1 }} />
          <Typography variant="h6" component="h1" fontWeight="medium">
            翻译助手
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <IconButton
            size="small"
            onClick={toggleTheme}
            sx={{ color: 'inherit' }}
            title="切换主题"
          >
            {currentTheme === 'dark' ? <LightModeIcon /> : <DarkModeIcon />}
          </IconButton>

          <Chip
            label={enabled ? '已启用' : '已禁用'}
            size="small"
            color={enabled ? 'success' : 'default'}
            sx={{
              fontSize: '0.7rem',
              height: '20px',
              backgroundColor: enabled ? 'rgba(76, 175, 80, 0.2)' : 'rgba(158, 158, 158, 0.2)',
              color: 'white'
            }}
          />
        </Box>
      </Box>

      {/* Tab导航 */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={activeTab}
          onChange={(_, newValue) => setActiveTab(newValue)}
          variant="fullWidth"
          sx={{
            '& .MuiTab-root': {
              minHeight: '40px',
              fontSize: '0.875rem',
              textTransform: 'none',
              color: currentTheme === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.7)',
            },
            '& .Mui-selected': {
              color: currentTheme === 'dark' ? '#90caf9' : '#1976d2',
            },
          }}
        >
          <Tab label="翻译" />
          <Tab label="设置" />
          <Tab label="生词本" />
        </Tabs>
      </Box>

      {/* Tab内容 */}
      <Box sx={{ p: 2, flex: 1 }}>
        {/* 消息提示 */}
        {message && (
          <Alert
            severity="info"
            sx={{ mb: 2, fontSize: '0.875rem' }}
            onClose={() => setMessage(null)}
          >
            {message}
          </Alert>
        )}

        {/* 翻译Tab */}
        {activeTab === 0 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* 功能开关 */}
            <Card sx={{
              background: currentTheme === 'dark'
                ? 'rgba(42, 42, 42, 0.8)'
                : 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(10px)',
              border: '1px solid',
              borderColor: currentTheme === 'dark'
                ? 'rgba(255, 255, 255, 0.1)'
                : 'rgba(0, 0, 0, 0.1)',
            }}>
              <CardContent sx={{ p: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={enabled}
                      onChange={(e) => saveSettings(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="启用翻译功能"
                  sx={{ mb: 1 }}
                />
                <Typography variant="caption" color="text.secondary" display="block">
                  开启后可使用划词翻译和快捷键功能
                </Typography>
              </CardContent>
            </Card>

            {/* 快捷操作 */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
              <Button
                variant="contained"
                startIcon={isVisible ? <VisibilityOffIcon /> : <VisibilityIcon />}
                fullWidth
                disabled={!enabled}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleToggleSidebar();
                }}
                sx={{
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontWeight: 'medium',
                  py: 1.2,
                }}
              >
                {isVisible ? '隐藏侧边栏' : '显示侧边栏'}
              </Button>

              <Button
                variant="contained"
                startIcon={<CameraAltIcon />}
                fullWidth
                disabled={!enabled}
                color="secondary"
                onClick={async (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  try {
                    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
                    if (tab?.id) {
                      await chrome.tabs.sendMessage(tab.id, {
                        type: 'START_SCREENSHOT_TRANSLATION'
                      });
                      setMessage('请在页面上拖拽选择要翻译的区域');
                      setTimeout(() => window.close(), 1000);
                    }
                  } catch (error) {
                    console.error('Screenshot translation failed:', error);
                    setMessage('启动截图翻译失败，请重试');
                  }
                }}
                sx={{
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontWeight: 'medium',
                  py: 1.2,
                }}
              >
                截图翻译
              </Button>

              <Button
                variant="outlined"
                startIcon={<TranslateIcon />}
                fullWidth
                disabled={!enabled}
                onClick={async (e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  try {
                    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
                    if (tab?.id) {
                      await chrome.tabs.sendMessage(tab.id, {
                        type: 'SIDEBAR_TRANSLATE',
                        payload: { text: '示例文本' }
                      });
                      setMessage('请选择文本进行翻译');
                    }
                  } catch (error) {
                    setMessage('请在网页中选择文本进行翻译');
                  }
                }}
                sx={{
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontWeight: 'medium',
                  py: 1.2,
                }}
              >
                划词翻译
              </Button>
            </Box>

            {/* 快捷键提示 */}
            <Card sx={{
              background: currentTheme === 'dark'
                ? 'rgba(42, 42, 42, 0.6)'
                : 'rgba(248, 249, 250, 0.8)',
              border: '1px solid',
              borderColor: currentTheme === 'dark'
                ? 'rgba(255, 255, 255, 0.05)'
                : 'rgba(0, 0, 0, 0.05)',
            }}>
              <CardContent sx={{ p: 1.5 }}>
                <Typography variant="caption" color="text.secondary" fontWeight="medium">
                  快捷键
                </Typography>
                <Box sx={{ mt: 0.5, display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                  <Typography variant="caption" color="text.secondary">
                    Alt + Q: 显示/隐藏侧边栏
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Ctrl + Shift + P: 钉住/取消钉住
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Ctrl + Shift + T: 切换主题
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Box>
        )}

        {/* 设置Tab */}
        {activeTab === 1 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<SettingsIcon />}
              fullWidth
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                chrome.runtime.openOptionsPage();
              }}
              sx={{
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 'medium',
                py: 1.2,
              }}
            >
              打开设置页面
            </Button>

            <Card sx={{
              background: currentTheme === 'dark'
                ? 'rgba(42, 42, 42, 0.8)'
                : 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(10px)',
            }}>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  当前设置
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      主题模式
                    </Typography>
                    <Chip
                      label={currentTheme === 'dark' ? '深色' : '浅色'}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      翻译功能
                    </Typography>
                    <Chip
                      label={enabled ? '已启用' : '已禁用'}
                      size="small"
                      color={enabled ? 'success' : 'default'}
                    />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      侧边栏状态
                    </Typography>
                    <Chip
                      label={isVisible ? '显示中' : '已隐藏'}
                      size="small"
                      color={isVisible ? 'primary' : 'default'}
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Box>
        )}

        {/* 生词本Tab */}
        {activeTab === 2 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, alignItems: 'center', py: 4 }}>
            <BookmarkIcon sx={{ fontSize: 48, color: 'text.secondary', opacity: 0.5 }} />
            <Typography variant="body2" color="text.secondary" textAlign="center">
              生词本功能即将上线
            </Typography>
            <Typography variant="caption" color="text.secondary" textAlign="center">
              收藏的单词和短语将在这里显示
            </Typography>
          </Box>
        )}
      </Box>

      {/* 底部状态栏 */}
      <Box sx={{
        p: 1.5,
        borderTop: '1px solid',
        borderTopColor: 'divider',
        background: currentTheme === 'dark'
          ? 'rgba(30, 30, 30, 0.8)'
          : 'rgba(248, 249, 250, 0.8)',
        backdropFilter: 'blur(10px)'
      }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            翻译助手 v1.0.0
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box sx={{
              width: 6,
              height: 6,
              borderRadius: '50%',
              backgroundColor: enabled ? 'success.main' : 'grey.400'
            }} />
            <Typography variant="caption" color="text.secondary">
              {enabled ? '运行中' : '已暂停'}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

// 错误边界组件
class ErrorBoundary extends React.Component<{children: React.ReactNode}, {hasError: boolean}> {
  constructor(props: {children: React.ReactNode}) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Popup error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="error">插件加载失败</Typography>
          <Button onClick={() => this.setState({ hasError: false })}>
            重试
          </Button>
        </Box>
      );
    }

    return this.props.children;
  }
}

// 渲染应用
const root = ReactDOM.createRoot(document.getElementById('app')!);

// 主应用组件
function PopupApp() {
  const { currentTheme } = useThemeStore();
  const theme = React.useMemo(() => createMuiTheme(currentTheme), [currentTheme]);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <ModernPopup />
    </ThemeProvider>
  );
}

root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <PopupApp />
    </ErrorBoundary>
  </React.StrictMode>
);
