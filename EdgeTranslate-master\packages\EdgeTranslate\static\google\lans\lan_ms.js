// API callback
callback({
  "sourceLanguages": [
    {
      "language": "auto",
      "name": "Kesan bahasa"
    },
    {
      "language": "af",
      "name": "Afrikaans"
    },
    {
      "language": "sq",
      "name": "Albania"
    },
    {
      "language": "am",
      "name": "Amhara"
    },
    {
      "language": "ar",
      "name": "Arab"
    },
    {
      "language": "hy",
      "name": "Armenia"
    },
    {
      "language": "as",
      "name": "Assam"
    },
    {
      "language": "ay",
      "name": "Aymara"
    },
    {
      "language": "az",
      "name": "Azerbaijan"
    },
    {
      "language": "ms",
      "name": "Bahasa Melayu"
    },
    {
      "language": "bm",
      "name": "Bambara"
    },
    {
      "language": "eu",
      "name": "Basque"
    },
    {
      "language": "nl",
      "name": "Belanda"
    },
    {
      "language": "be",
      "name": "Belarus"
    },
    {
      "language": "bn",
      "name": "Bengali"
    },
    {
      "language": "bho",
      "name": "Bhojpuri"
    },
    {
      "language": "bs",
      "name": "Bosnia"
    },
    {
      "language": "bg",
      "name": "Bulgaria"
    },
    {
      "language": "ca",
      "name": "Catalan"
    },
    {
      "language": "ceb",
      "name": "Cebu"
    },
    {
      "language": "ny",
      "name": "Chichewa"
    },
    {
      "language": "zh-CN",
      "name": "Cina"
    },
    {
      "language": "co",
      "name": "Corsica"
    },
    {
      "language": "hr",
      "name": "Croatia"
    },
    {
      "language": "cs",
      "name": "Czech"
    },
    {
      "language": "da",
      "name": "Denmark"
    },
    {
      "language": "dv",
      "name": "Dhivehi"
    },
    {
      "language": "doi",
      "name": "Dogri"
    },
    {
      "language": "eo",
      "name": "Esperanto"
    },
    {
      "language": "et",
      "name": "Estonia"
    },
    {
      "language": "ee",
      "name": "Ewe"
    },
    {
      "language": "fi",
      "name": "Finland"
    },
    {
      "language": "fy",
      "name": "Frisia"
    },
    {
      "language": "gd",
      "name": "Gaelic Scotland"
    },
    {
      "language": "gl",
      "name": "Galicia"
    },
    {
      "language": "ka",
      "name": "Georgia"
    },
    {
      "language": "el",
      "name": "Greek"
    },
    {
      "language": "gn",
      "name": "Guarani"
    },
    {
      "language": "gu",
      "name": "Gujerat"
    },
    {
      "language": "ha",
      "name": "Hausa"
    },
    {
      "language": "haw",
      "name": "Hawaii"
    },
    {
      "language": "hi",
      "name": "Hindi"
    },
    {
      "language": "hmn",
      "name": "Hmong"
    },
    {
      "language": "hu",
      "name": "Hungary"
    },
    {
      "language": "iw",
      "name": "Ibrani"
    },
    {
      "language": "is",
      "name": "Iceland"
    },
    {
      "language": "ig",
      "name": "Igbo"
    },
    {
      "language": "ilo",
      "name": "Ilocano"
    },
    {
      "language": "id",
      "name": "Indonesia"
    },
    {
      "language": "en",
      "name": "Inggeris"
    },
    {
      "language": "ga",
      "name": "Ireland"
    },
    {
      "language": "it",
      "name": "Itali"
    },
    {
      "language": "jw",
      "name": "Jawa"
    },
    {
      "language": "ja",
      "name": "Jepun"
    },
    {
      "language": "de",
      "name": "Jerman"
    },
    {
      "language": "kn",
      "name": "Kannada"
    },
    {
      "language": "kk",
      "name": "Kazakh"
    },
    {
      "language": "km",
      "name": "Khmer"
    },
    {
      "language": "rw",
      "name": "Kinyarwanda"
    },
    {
      "language": "gom",
      "name": "Konkani"
    },
    {
      "language": "ko",
      "name": "Korea"
    },
    {
      "language": "ht",
      "name": "Kreol Haiti"
    },
    {
      "language": "kri",
      "name": "Krio"
    },
    {
      "language": "ku",
      "name": "Kurdistan (Kurmanji)"
    },
    {
      "language": "ckb",
      "name": "Kurdistan (Sorani)"
    },
    {
      "language": "ky",
      "name": "Kyrgyz"
    },
    {
      "language": "lo",
      "name": "Lao"
    },
    {
      "language": "la",
      "name": "Latin"
    },
    {
      "language": "lv",
      "name": "Latvia"
    },
    {
      "language": "ln",
      "name": "Lingala"
    },
    {
      "language": "lt",
      "name": "Lithuania"
    },
    {
      "language": "lg",
      "name": "Luganda"
    },
    {
      "language": "lb",
      "name": "Luxembourg"
    },
    {
      "language": "mk",
      "name": "Macedonia"
    },
    {
      "language": "mai",
      "name": "Maithili"
    },
    {
      "language": "mg",
      "name": "Malagasy"
    },
    {
      "language": "ml",
      "name": "Malayalam"
    },
    {
      "language": "mt",
      "name": "Malta"
    },
    {
      "language": "mi",
      "name": "Maori"
    },
    {
      "language": "mr",
      "name": "Marathi"
    },
    {
      "language": "mni-Mtei",
      "name": "Meiteilon (Manipuri)"
    },
    {
      "language": "lus",
      "name": "Mizo"
    },
    {
      "language": "mn",
      "name": "Mongolia"
    },
    {
      "language": "my",
      "name": "Myanmar (Burma)"
    },
    {
      "language": "ne",
      "name": "Nepal"
    },
    {
      "language": "no",
      "name": "Norway"
    },
    {
      "language": "or",
      "name": "Odia (Oriya)"
    },
    {
      "language": "om",
      "name": "Oromo"
    },
    {
      "language": "fa",
      "name": "Parsi"
    },
    {
      "language": "ps",
      "name": "Pashto"
    },
    {
      "language": "fr",
      "name": "Perancis"
    },
    {
      "language": "pl",
      "name": "Poland"
    },
    {
      "language": "pt",
      "name": "Portugis"
    },
    {
      "language": "pa",
      "name": "Punjabi"
    },
    {
      "language": "qu",
      "name": "Quechua"
    },
    {
      "language": "ro",
      "name": "Romania"
    },
    {
      "language": "ru",
      "name": "Rusia"
    },
    {
      "language": "sm",
      "name": "Samoa"
    },
    {
      "language": "sa",
      "name": "Sanskrit"
    },
    {
      "language": "es",
      "name": "Sepanyol"
    },
    {
      "language": "nso",
      "name": "Sepedi"
    },
    {
      "language": "sr",
      "name": "Serbia"
    },
    {
      "language": "st",
      "name": "Sesotho"
    },
    {
      "language": "sn",
      "name": "Shona"
    },
    {
      "language": "sd",
      "name": "Sindhi"
    },
    {
      "language": "si",
      "name": "Sinhala"
    },
    {
      "language": "sk",
      "name": "Slovak"
    },
    {
      "language": "sl",
      "name": "Slovenia"
    },
    {
      "language": "so",
      "name": "Somali"
    },
    {
      "language": "su",
      "name": "Sunda"
    },
    {
      "language": "sw",
      "name": "Swahili"
    },
    {
      "language": "sv",
      "name": "Sweden"
    },
    {
      "language": "tl",
      "name": "Tagalog"
    },
    {
      "language": "tg",
      "name": "Tajik"
    },
    {
      "language": "ta",
      "name": "Tamil"
    },
    {
      "language": "tt",
      "name": "Tatar"
    },
    {
      "language": "te",
      "name": "Telugu"
    },
    {
      "language": "th",
      "name": "Thai"
    },
    {
      "language": "ti",
      "name": "Tigrinya"
    },
    {
      "language": "ts",
      "name": "Tsonga"
    },
    {
      "language": "tr",
      "name": "Turki"
    },
    {
      "language": "tk",
      "name": "Turkmen"
    },
    {
      "language": "ak",
      "name": "Twi"
    },
    {
      "language": "uk",
      "name": "Ukraine"
    },
    {
      "language": "ur",
      "name": "Urdu"
    },
    {
      "language": "ug",
      "name": "Uyghur"
    },
    {
      "language": "uz",
      "name": "Uzbek"
    },
    {
      "language": "vi",
      "name": "Vietnam"
    },
    {
      "language": "cy",
      "name": "Wales"
    },
    {
      "language": "xh",
      "name": "Xhosa"
    },
    {
      "language": "yi",
      "name": "Yiddish"
    },
    {
      "language": "yo",
      "name": "Yoruba"
    },
    {
      "language": "zu",
      "name": "Zulu"
    }
  ],
  "targetLanguages": [
    {
      "language": "af",
      "name": "Afrikaans"
    },
    {
      "language": "sq",
      "name": "Albania"
    },
    {
      "language": "am",
      "name": "Amhara"
    },
    {
      "language": "ar",
      "name": "Arab"
    },
    {
      "language": "hy",
      "name": "Armenia"
    },
    {
      "language": "as",
      "name": "Assam"
    },
    {
      "language": "ay",
      "name": "Aymara"
    },
    {
      "language": "az",
      "name": "Azerbaijan"
    },
    {
      "language": "ms",
      "name": "Bahasa Melayu"
    },
    {
      "language": "bm",
      "name": "Bambara"
    },
    {
      "language": "eu",
      "name": "Basque"
    },
    {
      "language": "nl",
      "name": "Belanda"
    },
    {
      "language": "be",
      "name": "Belarus"
    },
    {
      "language": "bn",
      "name": "Bengali"
    },
    {
      "language": "bho",
      "name": "Bhojpuri"
    },
    {
      "language": "bs",
      "name": "Bosnia"
    },
    {
      "language": "bg",
      "name": "Bulgaria"
    },
    {
      "language": "ca",
      "name": "Catalan"
    },
    {
      "language": "ceb",
      "name": "Cebu"
    },
    {
      "language": "ny",
      "name": "Chichewa"
    },
    {
      "language": "zh-CN",
      "name": "Cina (Mudah)"
    },
    {
      "language": "zh-TW",
      "name": "Cina (Tradisional)"
    },
    {
      "language": "co",
      "name": "Corsica"
    },
    {
      "language": "hr",
      "name": "Croatia"
    },
    {
      "language": "cs",
      "name": "Czech"
    },
    {
      "language": "da",
      "name": "Denmark"
    },
    {
      "language": "dv",
      "name": "Dhivehi"
    },
    {
      "language": "doi",
      "name": "Dogri"
    },
    {
      "language": "eo",
      "name": "Esperanto"
    },
    {
      "language": "et",
      "name": "Estonia"
    },
    {
      "language": "ee",
      "name": "Ewe"
    },
    {
      "language": "fi",
      "name": "Finland"
    },
    {
      "language": "fy",
      "name": "Frisia"
    },
    {
      "language": "gd",
      "name": "Gaelic Scotland"
    },
    {
      "language": "gl",
      "name": "Galicia"
    },
    {
      "language": "ka",
      "name": "Georgia"
    },
    {
      "language": "el",
      "name": "Greek"
    },
    {
      "language": "gn",
      "name": "Guarani"
    },
    {
      "language": "gu",
      "name": "Gujerat"
    },
    {
      "language": "ha",
      "name": "Hausa"
    },
    {
      "language": "haw",
      "name": "Hawaii"
    },
    {
      "language": "hi",
      "name": "Hindi"
    },
    {
      "language": "hmn",
      "name": "Hmong"
    },
    {
      "language": "hu",
      "name": "Hungary"
    },
    {
      "language": "iw",
      "name": "Ibrani"
    },
    {
      "language": "is",
      "name": "Iceland"
    },
    {
      "language": "ig",
      "name": "Igbo"
    },
    {
      "language": "ilo",
      "name": "Ilocano"
    },
    {
      "language": "id",
      "name": "Indonesia"
    },
    {
      "language": "en",
      "name": "Inggeris"
    },
    {
      "language": "ga",
      "name": "Ireland"
    },
    {
      "language": "it",
      "name": "Itali"
    },
    {
      "language": "jw",
      "name": "Jawa"
    },
    {
      "language": "ja",
      "name": "Jepun"
    },
    {
      "language": "de",
      "name": "Jerman"
    },
    {
      "language": "kn",
      "name": "Kannada"
    },
    {
      "language": "kk",
      "name": "Kazakh"
    },
    {
      "language": "km",
      "name": "Khmer"
    },
    {
      "language": "rw",
      "name": "Kinyarwanda"
    },
    {
      "language": "gom",
      "name": "Konkani"
    },
    {
      "language": "ko",
      "name": "Korea"
    },
    {
      "language": "ht",
      "name": "Kreol Haiti"
    },
    {
      "language": "kri",
      "name": "Krio"
    },
    {
      "language": "ku",
      "name": "Kurdistan (Kurmanji)"
    },
    {
      "language": "ckb",
      "name": "Kurdistan (Sorani)"
    },
    {
      "language": "ky",
      "name": "Kyrgyz"
    },
    {
      "language": "lo",
      "name": "Lao"
    },
    {
      "language": "la",
      "name": "Latin"
    },
    {
      "language": "lv",
      "name": "Latvia"
    },
    {
      "language": "ln",
      "name": "Lingala"
    },
    {
      "language": "lt",
      "name": "Lithuania"
    },
    {
      "language": "lg",
      "name": "Luganda"
    },
    {
      "language": "lb",
      "name": "Luxembourg"
    },
    {
      "language": "mk",
      "name": "Macedonia"
    },
    {
      "language": "mai",
      "name": "Maithili"
    },
    {
      "language": "mg",
      "name": "Malagasy"
    },
    {
      "language": "ml",
      "name": "Malayalam"
    },
    {
      "language": "mt",
      "name": "Malta"
    },
    {
      "language": "mi",
      "name": "Maori"
    },
    {
      "language": "mr",
      "name": "Marathi"
    },
    {
      "language": "mni-Mtei",
      "name": "Meiteilon (Manipuri)"
    },
    {
      "language": "lus",
      "name": "Mizo"
    },
    {
      "language": "mn",
      "name": "Mongolia"
    },
    {
      "language": "my",
      "name": "Myanmar (Burma)"
    },
    {
      "language": "ne",
      "name": "Nepal"
    },
    {
      "language": "no",
      "name": "Norway"
    },
    {
      "language": "or",
      "name": "Odia (Oriya)"
    },
    {
      "language": "om",
      "name": "Oromo"
    },
    {
      "language": "fa",
      "name": "Parsi"
    },
    {
      "language": "ps",
      "name": "Pashto"
    },
    {
      "language": "fr",
      "name": "Perancis"
    },
    {
      "language": "pl",
      "name": "Poland"
    },
    {
      "language": "pt",
      "name": "Portugis"
    },
    {
      "language": "pa",
      "name": "Punjabi"
    },
    {
      "language": "qu",
      "name": "Quechua"
    },
    {
      "language": "ro",
      "name": "Romania"
    },
    {
      "language": "ru",
      "name": "Rusia"
    },
    {
      "language": "sm",
      "name": "Samoa"
    },
    {
      "language": "sa",
      "name": "Sanskrit"
    },
    {
      "language": "es",
      "name": "Sepanyol"
    },
    {
      "language": "nso",
      "name": "Sepedi"
    },
    {
      "language": "sr",
      "name": "Serbia"
    },
    {
      "language": "st",
      "name": "Sesotho"
    },
    {
      "language": "sn",
      "name": "Shona"
    },
    {
      "language": "sd",
      "name": "Sindhi"
    },
    {
      "language": "si",
      "name": "Sinhala"
    },
    {
      "language": "sk",
      "name": "Slovak"
    },
    {
      "language": "sl",
      "name": "Slovenia"
    },
    {
      "language": "so",
      "name": "Somali"
    },
    {
      "language": "su",
      "name": "Sunda"
    },
    {
      "language": "sw",
      "name": "Swahili"
    },
    {
      "language": "sv",
      "name": "Sweden"
    },
    {
      "language": "tl",
      "name": "Tagalog"
    },
    {
      "language": "tg",
      "name": "Tajik"
    },
    {
      "language": "ta",
      "name": "Tamil"
    },
    {
      "language": "tt",
      "name": "Tatar"
    },
    {
      "language": "te",
      "name": "Telugu"
    },
    {
      "language": "th",
      "name": "Thai"
    },
    {
      "language": "ti",
      "name": "Tigrinya"
    },
    {
      "language": "ts",
      "name": "Tsonga"
    },
    {
      "language": "tr",
      "name": "Turki"
    },
    {
      "language": "tk",
      "name": "Turkmen"
    },
    {
      "language": "ak",
      "name": "Twi"
    },
    {
      "language": "uk",
      "name": "Ukraine"
    },
    {
      "language": "ur",
      "name": "Urdu"
    },
    {
      "language": "ug",
      "name": "Uyghur"
    },
    {
      "language": "uz",
      "name": "Uzbek"
    },
    {
      "language": "vi",
      "name": "Vietnam"
    },
    {
      "language": "cy",
      "name": "Wales"
    },
    {
      "language": "xh",
      "name": "Xhosa"
    },
    {
      "language": "yi",
      "name": "Yiddish"
    },
    {
      "language": "yo",
      "name": "Yoruba"
    },
    {
      "language": "zu",
      "name": "Zulu"
    }
  ]
}
);