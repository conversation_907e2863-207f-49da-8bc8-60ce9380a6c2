浏览器翻译插件 · 最终完整方案
项目定位
一个「零服务器」浏览器翻译插件（Chrome / Firefox / 360），划词 + 截图翻译，支持免费/付费模型动态切换，后台集中管理。全部由「WXT + Next.js + Supabase」完成，不额外部署服务器。
技术栈
• WXT：浏览器插件脚手架（Manifest V3，热更新，跨浏览器打包）
• Next.js：管理后台（Vercel Serverless）+ 插件静态资源
• UI 框架：MUI（管理后台 + 插件所有界面统一风格）
• 后端：Supabase（PostgreSQL + Auth + Storage）
• OCR：Tesseract.js（前端，离线）
• 翻译源：Google 免费接口（MVP），可在管理后台管理AI模型，可切换 OpenAI-GPT-4o-mini、Kimi 等

功能总览
A. 用户端（插件，具体可看下方的浏览器插件功能清单）
划词悬浮侧边栏
– 触发：鼠标悬停 400 ms
– 位置：光标右侧，可拖拽固定
– 内容：翻译结果、发音、定义、例句（MUI 折叠卡片）
截图翻译
– 快捷键 Alt+S 或右键菜单
– 选区半透明遮罩 → Tesseract.js OCR → 结果叠加层
自定义设置
– 插件图标点击 → MUI 弹窗
– 设置：源/目标语言、显示项开关、主题色、快捷键
登录
– Google / Microsoft OAuth（Supabase Auth）
– 未登录限制高级模型
B. 管理后台（Next.js + MUI）
AI模型管理
– 实时增删改查：名称、provider、endpoint、API Key（加密）、是否免费、优先级、启用开关
– 保存立即生效，无需重启
用户管理
– 用户列表、用户订阅状态
登录页面
-账号密码登录，创建账号登录
用量
– 按日 / 模型统计调用量
系统
– 语言列表维护
– 全局主题、Logo 上传

交互与视觉规范（统一 MUI）
• 侧边栏宽度 260 px，圆角 8 px，阴影 4 dp
• 折叠卡片动画 120 ms ease-out
• 主题色：#1976d2（主）、#dc004e（次）
• 深色模式自动跟随系统

本地开发流程
① npm create wxt@latest translator-plugin
② 选 React + TypeScript 模板
③ 安装 MUI：
npm i @mui/material @emotion/react @emotion/styled
④ 并行启动：
– WXT：npm run dev（插件热重载）
– Next.js：cd admin && npm run dev（管理后台 3000 端口）
⑤ 配置环境变量（Supabase URL / ANON_KEY）
⑥ Chrome 加载已解压扩展 → 指向 dist/（WXT 产出）
上线 checklist
• Vercel 一键部署：GitHub → Vercel → 获得 https://xxx.vercel.app
• 修改插件内 API & 资源地址为生产域名
• npm run build → 生成 zip → Chrome Web Store / Firefox Add-ons 提交
• 首包体积 < 100 MB（语言包懒加载 + gzip）
后续扩展预留
• Stripe 订阅接入（免费 / 付费模型自动切换）
• Edge Functions 迁移（隐藏密钥、缓存、计费）
• 云端 OCR 按需加载
至此，开发团队只需按「WXT 插件外壳 + Next.js 管理后台 + Supabase 后端」三条主线即可启动全部工作，无需再部署任何额外服务器。

浏览器插件功能清单
划词悬浮侧边栏
• 触发：鼠标悬停任意单词/句子 400 ms 自动识别
• 位置：光标右侧固定偏移 10 px；超出视口自动翻转
• 交互：
– 关闭按钮（×）
– 最小化按钮（⎯）→ 仅保留标题栏
– 固定按钮（📌）→ 拖拽后可锁定位置（localStorage 记录坐标）
• 内容块（可折叠）：
– 翻译结果（默认展开）
– 发音（IPA + 播放键）
– 定义
– 例句
简洁结果样式
• 面板宽度 260 px，高度自适应 ≤ 400 px
• CSS 变量：--bg-panel #fff，--border #e0e0e0，--primary #1976d2
• 折叠动画 120 ms ease-out
• 深色模式 media-query 自动切换
自定义翻译内容
• 入口：点击插件图标 → 弹出 320×420 设置页（MUI Drawer）
• 设置项：
– 源语言 / 目标语言下拉（读取 /api/languages）
– 显示开关：发音、定义、例句（独立 Checkbox）
– 面板主题：浅色 / 深色 / 跟随系统
• 保存后即时生效（localStorage + 消息广播 content-script 重绘）
截图翻译
• 触发：快捷键（Alt+S，可在设置页修改）/ 右键菜单「截图翻译」
• 交互：
– 进入十字光标 → 用户框选区域
– 选区半透明遮罩（rgba 0,0,0,.35）
– OCR 完成后在选区下方叠加半透明结果层（不遮挡原文）
• 支持 Esc 取消
登录入口
• 触发：点击插件图标 → 设置页顶部「登录」按钮
• 支持：Google OAuth、Microsoft OAuth（使用 Supabase Auth）
• 状态：未登录时禁用自定义语言 & 云端高级模型
本地开发约定
• 统一域名：
– 前端 Next.js 本地 http://localhost:3000
– 插件 manifest "content_security_policy": "script-src 'self' http://localhost:3000"
• 环境变量：
– NEXT_PUBLIC_SUPABASE_URL
– NEXT_PUBLIC_SUPABASE_ANON_KEY
• 热重载：
– Next.js npm run dev
– 插件 chrome://extensions 加载已解压扩展，勾选「允许访问本地文件」
