### Attention

-   For the installation of the expansion pack in ZIP format for Firefox browser, please refer to [here](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/en/ToFirefoxUsers.md)!

-   In order to further improve the translation experience, we have established an Edge Translate user communication group. Welcome to join: [Edge Translate](https://t.me/EdgeTranslate)

### New

-   Update the built-in PDF viewer with **PDF annotation support**, thanks to [pdf.js](https://github.com/mozilla/pdf.js) community;
-   Add a button to get the link of the currently reading PDF file in the PDF viewer;

### Improvements

-   Update Russian documentation, thanks to @ViktorOn ;

### Fix

-   Fixed Baidu translator;
-   Fixed Google translator problems due to Google exiting Mainland China, see #400 for details;
-   Fixed DeepL translator problems due its inner usage of Google translator;

### Sponsor

It took us much time and energy to develop this project. If it truly helped you in some way, you could reward us with cans of Coke to support us to keep improving it: [PayPal](https://paypal.me/EdgeTranslate).

But, this is completely **voluntary**. Sponsoring won't bring any special treatment and you can still use Edge Translate freely without sponsoring. Do it according to your capability!
