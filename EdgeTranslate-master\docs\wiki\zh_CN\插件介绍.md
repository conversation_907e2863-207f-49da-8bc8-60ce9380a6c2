## 安装或更新后请刷新需要翻译的页面！！！

## 使用前请详细阅读[使用说明](./使用说明.md)！！！

## 当你遇到问题时请先查看[侧边翻译 Q&A](./注意事项.md)！！！

## 简介

欢迎使用侧边翻译！

侧边翻译是一个简单实用的翻译插件，支持[Chrome 浏览器](https://chrome.google.com/webstore/detail/bocbaocobfecmglnmeaeppambideimao)、[火狐浏览器](https://addons.mozilla.org/en-US/firefox/addon/edge_translate)、[360 安全浏览器](https://ext.se.360.cn/webstore/detail/jkhojcaggkaojlhfddocjkkphfdkejeg)等主流浏览器。我们开发它的主要目的是辅助用户阅读外文文献。为此，我们一直遵循着用户阅读体验第一的原则，并做出了如下努力：

-   **谷歌翻译接口**

    我们使用了谷歌翻译提供的接口来进行单词和句子的翻译，在一定程度上保证了翻译结果的准确性；

-   **支持 PDF 文件**

    我们支持了 PDF 文件中的划词翻译，打破了很多用户阅读 PDF 格式的文献时的阅读障碍（由于火狐浏览器的 bug，该功能在火狐浏览器上暂不可用）；

-   **友好的翻译结果展示方式**

    我们选择了侧边弹出这种相对友好的方式来展示翻译结果。弹出的展示栏会将用户正在阅读内容推开，避免遮挡内容影响阅读；

-   **简洁清晰的界面**

    我们设计了简洁清晰的翻译结果展示栏，对重要内容进行了突出显示，保证用户的注意力集中于展示的内容而不是展示框等无关紧要的东西；

-   **展示栏可固定**

    如果用户使用翻译的频率比较高，可以选择固定展示栏，避免展示栏频繁弹出影响阅读；

-   **充分可自定义**

    我们允许用户自己决定需要展示翻译结果中的哪些内容。例如：如果你只是想要知道单词的意思，那可以选择只查看单词常见意思，如果你还希望学习某个单词的具体用法，我们也提供了单词的读音、定义，详解、例句等更加详细的内容供你查看；

-   **高效的快捷键**

    我们提供了丰富的快捷键操作，使操作效率获得了巨大提升。现在你只需要使用键盘就可以完成翻译选定词、展开查词面板、固定和取消固定翻译结果等操作；

-   **网页黑名单**

    我们提供了实用的黑名单功能，你可以很方便地将正在浏览的页面添加到黑名单中以禁用该页面上的划词翻译和双击翻译，或者将正在浏览的页面从黑名单中移出以重新启用该页面上的划词翻译和双击翻译。

-   **网页翻译**

    我们支持了网页翻译，可以直接将整个网页翻译成你需要的语言。**火狐用户请看[这里](./致火狐用户.md)**

侧边翻译的诞生离不开开源社区，在侧边翻译的开发过程中，我们使用了 [Mozilla](https://github.com/mozilla) 开源的 [pdf.js](https://github.com/mozilla/pdf.js) 作为内置的 PDF 阅读器以支持 PDF 文件中的划词翻译；在遇到困难时我们也参考了 [crimx](https://github.com/crimx) 的 [saladict](https://github.com/crimx/ext-saladict) 以寻求解决方案；另外还使用了[gulp](https://github.com/gulpjs/gulp) 、[webpack](https://github.com/webpack/webpack) 等优秀的开源工具辅助开发，在此一并表示感谢。

同时也感谢从 0.2.0 开始就使用侧边翻译并为我们提供了反馈的用户，你们为我们提供了很多非常好的意见和建议，没有你们或许就不会有现在的侧边翻译了。

## 使用说明

[侧边翻译使用说明](./使用说明.md)

## 注意事项

[侧边翻译注意事项](./注意事项.md)

## 隐私政策

[侧边翻译隐私政策](./隐私政策.md)

## 问题与反馈

侧边翻译是我们（[nickyc975](https://github.com/nickyc975) 和 [Mark Fenng](https://github.com/Mark-Fenng)）利用业余时间开发的，难免会有一些问题。如果你有什么意见或者建议欢迎第一时间向我们反馈，帮助我们把它做得更好。

-   反馈地址：[侧边翻译 issue](https://github.com/EdgeTranslate/EdgeTranslate/issues/new/choose)

-   电子邮件：[nickyc975](mailto:<EMAIL>), [Mark Fenng](mailto:<EMAIL>)

-   Chrome 应用商店：[侧边翻译](https://chrome.google.com/webstore/detail/bocbaocobfecmglnmeaeppambideimao/reviews)

-   Firefox 附加组件商店：[侧边翻译](https://addons.mozilla.org/en-US/firefox/addon/edge_translate/reviews)

另外，欢迎开发者们给我们提 issue 和 PR，如果你喜欢这个项目，欢迎 fork & star.

## 关于打赏

开发这个项目花费了我们许多的时间和精力，如果你真的觉得这个项目对你有帮助，不妨请我们喝罐可乐，支持我们继续做下去！

当然，这 **纯属自愿**，打赏并不能获得什么优待，不打赏也不会有任何影响，请量力而为！

|                                                                    微信                                                                     |                                                                    支付宝                                                                     |
| :-----------------------------------------------------------------------------------------------------------------------------------------: | :-------------------------------------------------------------------------------------------------------------------------------------------: |
| <img src="https://user-images.githubusercontent.com/25877145/80864662-b6617c00-8cb6-11ea-915a-582ca046118c.png" height=200 alt="微信支付"/> | <img src="https://user-images.githubusercontent.com/25877145/80864685-ced19680-8cb6-11ea-94e5-f5ca8e4389b9.jpg" height=200 alt="支付宝支付"/> |
