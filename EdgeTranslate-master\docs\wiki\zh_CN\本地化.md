### 本地化

我们开发了此扩展程序来帮助更多的人克服语言障碍。但是世界上有太多的语言，而我们只会说英语和中文。到目前为止，我们的软件仅支持以下语言：

- 英语
  
- 简体中文
  
- 繁体中文

- 俄语（感谢[ElectroLom](https://github.com/electrolom42) 和 [Viktor](https://github.com/ViktorOn)）

- 日语 (感谢[ykyuki](https://github.com/ykyuki))

如果您的母语不在上面的列表中，并且您愿意帮我们增加对您母语的支持，我们将不胜感激。

### 如何添加语言支持

1. Fork并克隆此[项目](https://github.com/EdgeTranslate/EdgeTranslate)。

2. 在`static/_locales`下添加一个名为`localeCode`的文件夹，其中`localeCode`是诸如`en`（代表英语）和`ru`（代表俄语）之类的代码，您可以在[此处](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/src/popup/languages.js)找到大多数语言对应的代码；

3. 将文件`static/_locales/en/messages.json`复制到刚创建的`localeCode`文件夹中；

4. 翻译`messages.json`的内容

   例如，在`AppName`标签中有`"messages": "Edge Translate"`和`"description": "App Name"`这两个内容，您需要将`Edge Translate`和`App Name`这两个内容翻译成您的语言。顺便说一句，从标签`Afrikaans`开始的语言名称可以不用翻译；

5. 提交您的更改并将其推送到您Fork过来的仓库中；

6. 向我们的仓库创建一个新的[pull request](https://github.com/EdgeTranslate/EdgeTranslate/pulls)。我们将尽快合并您的拉取请求。

### 更多信息

请前往[Chrome开发者文档](https://developer.chrome.com/extensions/i18n)获取更多信息。
