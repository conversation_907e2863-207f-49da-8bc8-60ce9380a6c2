import React from 'react';
import { Tabs, Tab, Box, CircularProgress, Chip, IconButton } from '@mui/material';
import { 
  Google as GoogleIcon,
  Psychology as OpenAIIcon,
  AutoAwesome as KimiIcon,
  ContentCopy as CopyIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useSidebarStore, TranslationEngine } from '../../../shared/stores/useSidebarStore';
import { useTranslationStore } from '../../../shared/stores/useTranslationStore';

interface EngineConfig {
  id: TranslationEngine;
  name: string;
  icon: React.ReactNode;
  color: string;
  description: string;
}

const engineConfigs: EngineConfig[] = [
  {
    id: 'google',
    name: 'Google翻译',
    icon: <GoogleIcon />,
    color: '#4285f4',
    description: '免费、快速、准确'
  },
  {
    id: 'openai',
    name: 'OpenAI GPT-4o-mini',
    icon: <OpenAIIcon />,
    color: '#10a37f',
    description: '智能、上下文理解'
  },
  {
    id: 'kimi',
    name: '<PERSON><PERSON>',
    icon: <KimiIcon />,
    color: '#ff6b35',
    description: '中文优化、本土化'
  }
];

export const TranslationTabs: React.FC = () => {
  const { activeTab, setActiveTab } = useSidebarStore();
  const { results, isLoading, errors, retryTranslation } = useTranslationStore();

  const handleTabChange = (event: React.SyntheticEvent, newValue: TranslationEngine) => {
    setActiveTab(newValue);
  };

  const handleCopyText = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // 可以添加一个toast提示
      console.log('Text copied to clipboard');
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const handleRetry = (engine: TranslationEngine) => {
    retryTranslation(engine);
  };

  const getEngineStatus = (engine: TranslationEngine) => {
    const loading = isLoading.get(engine);
    const error = errors.get(engine);
    const result = results.get(engine);

    if (loading) return 'loading';
    if (error) return 'error';
    if (result) return 'success';
    return 'idle';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'loading': return '#1976d2';
      case 'error': return '#d32f2f';
      case 'success': return '#2e7d32';
      default: return '#757575';
    }
  };

  const getStatusIcon = (engine: TranslationEngine) => {
    const status = getEngineStatus(engine);
    
    switch (status) {
      case 'loading':
        return <CircularProgress size={16} sx={{ color: 'inherit' }} />;
      case 'error':
        return (
          <IconButton 
            size="small" 
            onClick={(e) => {
              e.stopPropagation();
              handleRetry(engine);
            }}
            sx={{ color: 'inherit', p: 0.5 }}
          >
            <RefreshIcon fontSize="small" />
          </IconButton>
        );
      case 'success':
        return (
          <Chip 
            size="small" 
            label="✓" 
            sx={{ 
              height: 16, 
              fontSize: '10px',
              backgroundColor: 'rgba(46, 125, 50, 0.1)',
              color: '#2e7d32'
            }} 
          />
        );
      default:
        return null;
    }
  };

  const getResponseTime = (engine: TranslationEngine) => {
    const result = results.get(engine);
    return result?.responseTime ? `${Math.round(result.responseTime)}ms` : '';
  };

  return (
    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        variant="fullWidth"
        sx={{
          minHeight: 48,
          '& .MuiTab-root': {
            minHeight: 48,
            fontSize: '0.75rem',
            textTransform: 'none',
            padding: '6px 8px',
          },
          '& .MuiTabs-indicator': {
            height: 2,
          },
        }}
      >
        {engineConfigs.map((config) => {
          const status = getEngineStatus(config.id);
          const responseTime = getResponseTime(config.id);
          
          return (
            <Tab
              key={config.id}
              value={config.id}
              label={
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Box sx={{ color: config.color, display: 'flex', alignItems: 'center' }}>
                      {config.icon}
                    </Box>
                    <Box sx={{ fontSize: '0.75rem', fontWeight: 'medium' }}>
                      {config.name}
                    </Box>
                    {getStatusIcon(config.id)}
                  </Box>
                  
                  {responseTime && (
                    <Box sx={{ fontSize: '0.6rem', color: 'text.secondary' }}>
                      {responseTime}
                    </Box>
                  )}
                </Box>
              }
              sx={{
                borderColor: status === 'error' ? '#d32f2f' : 'transparent',
                borderWidth: 1,
                borderStyle: 'solid',
                borderRadius: '4px 4px 0 0',
                margin: '0 1px',
                '&.Mui-selected': {
                  backgroundColor: 'rgba(25, 118, 210, 0.04)',
                },
              }}
            />
          );
        })}
      </Tabs>

      {/* Tab Content */}
      <Box sx={{ p: 2, minHeight: 120 }}>
        {engineConfigs.map((config) => {
          if (config.id !== activeTab) return null;
          
          const loading = isLoading.get(config.id);
          const error = errors.get(config.id);
          const result = results.get(config.id);
          
          return (
            <Box key={config.id}>
              {loading && (
                <Box sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  py: 3,
                  gap: 1
                }}>
                  <CircularProgress size={20} />
                  <Box sx={{ fontSize: '0.875rem', color: 'text.secondary' }}>
                    {config.name} 翻译中...
                  </Box>
                </Box>
              )}
              
              {error && (
                <Box sx={{ 
                  p: 2, 
                  backgroundColor: 'rgba(211, 47, 47, 0.04)',
                  border: '1px solid rgba(211, 47, 47, 0.2)',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <Box>
                    <Box sx={{ fontSize: '0.875rem', color: '#d32f2f', fontWeight: 'medium' }}>
                      翻译失败
                    </Box>
                    <Box sx={{ fontSize: '0.75rem', color: 'text.secondary', mt: 0.5 }}>
                      {error}
                    </Box>
                  </Box>
                  <IconButton 
                    size="small" 
                    onClick={() => handleRetry(config.id)}
                    sx={{ color: '#d32f2f' }}
                  >
                    <RefreshIcon />
                  </IconButton>
                </Box>
              )}
              
              {result && (
                <Box>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between',
                    mb: 1
                  }}>
                    <Box sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
                      {config.description}
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <IconButton 
                        size="small" 
                        onClick={() => handleCopyText(result.translatedText)}
                        title="复制翻译结果"
                      >
                        <CopyIcon fontSize="small" />
                      </IconButton>
                      <IconButton 
                        size="small" 
                        title="收藏到生词本"
                      >
                        <StarBorderIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </Box>
                  
                  <Box sx={{ 
                    p: 1.5,
                    backgroundColor: 'rgba(25, 118, 210, 0.04)',
                    border: '1px solid rgba(25, 118, 210, 0.2)',
                    borderRadius: 1,
                    fontSize: '0.875rem',
                    lineHeight: 1.5
                  }}>
                    {result.translatedText}
                  </Box>
                  
                  {result.confidence && (
                    <Box sx={{ 
                      mt: 1, 
                      fontSize: '0.7rem', 
                      color: 'text.secondary',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <Box>置信度: {Math.round(result.confidence * 100)}%</Box>
                      {result.responseTime && (
                        <Box>响应时间: {Math.round(result.responseTime)}ms</Box>
                      )}
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};
