// Image cropping utility for content script
export async function cropImage(dataUrl: string, x: number, y: number, width: number, height: number): Promise<string> {
  console.log('✂️ Cropping image in content script:', { x, y, width, height });
  
  return new Promise((resolve, reject) => {
    try {
      // 创建canvas进行图片裁剪
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        console.warn('Canvas context not available');
        reject(new Error('Canvas not supported'));
        return;
      }

      // 设置canvas尺寸为裁剪区域大小
      canvas.width = width;
      canvas.height = height;

      const img = new Image();
      img.onload = () => {
        try {
          console.log('🖼️ Image loaded, size:', img.width, 'x', img.height);
          console.log('✂️ Cropping area:', { x, y, width, height });
          
          // 绘制裁剪后的图片
          // drawImage(image, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight)
          ctx.drawImage(img, x, y, width, height, 0, 0, width, height);
          
          // 转换为dataURL
          const croppedDataUrl = canvas.toDataURL('image/png');
          console.log('✅ Image cropped successfully, size:', croppedDataUrl.length);
          
          resolve(croppedDataUrl);
        } catch (error) {
          console.error('Canvas drawing failed:', error);
          reject(error);
        }
      };
      
      img.onerror = (error) => {
        console.error('Failed to load image:', error);
        reject(new Error('Failed to load image'));
      };
      
      img.src = dataUrl;
    } catch (error) {
      console.error('Image cropping setup failed:', error);
      reject(error);
    }
  });
}

// 获取设备像素比，用于高DPI屏幕的坐标转换
export function getDevicePixelRatio(): number {
  return window.devicePixelRatio || 1;
}

// 转换屏幕坐标到图片坐标
export function convertScreenToImageCoords(
  screenX: number, 
  screenY: number, 
  screenWidth: number, 
  screenHeight: number,
  imageWidth: number, 
  imageHeight: number
): { x: number; y: number; width: number; height: number } {
  
  const scaleX = imageWidth / window.innerWidth;
  const scaleY = imageHeight / window.innerHeight;
  
  return {
    x: Math.round(screenX * scaleX),
    y: Math.round(screenY * scaleY),
    width: Math.round(screenWidth * scaleX),
    height: Math.round(screenHeight * scaleY)
  };
}
