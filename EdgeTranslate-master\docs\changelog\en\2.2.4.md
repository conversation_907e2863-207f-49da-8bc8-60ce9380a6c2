### Attention

* For the installation of the expansion pack in ZIP format for Firefox browser, please refer to [here](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/en/ToFirefoxUsers.md)!

* In order to further improve the translation experience, we have established an Edge Translate user communication group. Welcome to join: [Edge Translate](https://t.me/EdgeTranslate)

### Improvements

* Add `role` attributes for clickable elements in the result panel so that shortcut extensions like Vimuim-C can recognize them (#267);

### Fix

* Fixed broken Google Translate API (#275, #276, #278);

* Replace extra line-breaks with spaces in texts selected from pdf files (#236);

* Fixed error when opening pdf files on www.yuque.com (#261);

### Sponsor

It took us much time and energy to develop this project. If it truly helped you in some way, you could reward us with cans of Coke to support us to keep improving it: [PayPal](https://paypal.me/EdgeTranslate).

But, this is completely __voluntary__. Sponsoring won't bring any special treatment and you can still use Edge Translate freely without sponsoring. Do it according to your capability!