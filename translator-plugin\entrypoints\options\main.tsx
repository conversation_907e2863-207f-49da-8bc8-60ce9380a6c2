import React, { useState, useEffect } from 'react';
import <PERSON>actD<PERSON> from 'react-dom/client';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import {
  Container,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Box,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Translate as TranslateIcon,
  Api as ApiIcon,
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon
} from '@mui/icons-material';

// 创建主题
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
  },
});

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function OptionsPage() {
  const [tabValue, setTabValue] = useState(0);
  const [settings, setSettings] = useState({
    source_language: 'auto',
    target_language: 'zh',
    theme: 'system',
    auto_translate: true,
    show_pronunciation: true,
    show_definitions: false,
    show_examples: false,
    sidebar_position: 'right',
    hotkey_enabled: true,
    hotkey_combination: 'Alt+S'
  });
  const [apiSettings, setApiSettings] = useState({
    google_api_key: '',
    openai_api_key: '',
    openai_endpoint: 'https://api.openai.com/v1/chat/completions',
    kimi_api_key: '',
    kimi_endpoint: 'https://api.moonshot.cn/v1/chat/completions',
    preferred_provider: 'google'
  });
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // 加载基本设置
      const result = await chrome.storage.sync.get([
        'source_language',
        'target_language', 
        'theme',
        'auto_translate',
        'show_pronunciation',
        'show_definitions',
        'show_examples',
        'sidebar_position',
        'hotkey_enabled',
        'hotkey_combination'
      ]);

      setSettings({
        source_language: result.source_language || 'auto',
        target_language: result.target_language || 'zh',
        theme: result.theme || 'system',
        auto_translate: result.auto_translate !== false,
        show_pronunciation: result.show_pronunciation !== false,
        show_definitions: result.show_definitions || false,
        show_examples: result.show_examples || false,
        sidebar_position: result.sidebar_position || 'right',
        hotkey_enabled: result.hotkey_enabled !== false,
        hotkey_combination: result.hotkey_combination || 'Alt+S'
      });

      // 加载API设置
      const apiResult = await chrome.storage.local.get([
        'google_api_key',
        'openai_api_key',
        'openai_endpoint',
        'kimi_api_key',
        'kimi_endpoint',
        'preferred_provider'
      ]);

      setApiSettings({
        google_api_key: apiResult.google_api_key || '',
        openai_api_key: apiResult.openai_api_key || '',
        openai_endpoint: apiResult.openai_endpoint || 'https://api.openai.com/v1/chat/completions',
        kimi_api_key: apiResult.kimi_api_key || '',
        kimi_endpoint: apiResult.kimi_endpoint || 'https://api.moonshot.cn/v1/chat/completions',
        preferred_provider: apiResult.preferred_provider || 'google'
      });
    } catch (error) {
      console.error('Load settings error:', error);
      setMessage({ type: 'error', text: '加载设置失败' });
    }
  };

  const saveSettings = async () => {
    try {
      // 保存基本设置
      await chrome.storage.sync.set(settings);
      
      // 保存API设置
      await chrome.storage.local.set(apiSettings);
      
      setMessage({ type: 'success', text: '设置已保存' });
      
      // 3秒后清除消息
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Save settings error:', error);
      setMessage({ type: 'error', text: '保存设置失败' });
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      {/* 页面标题 */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <SettingsIcon sx={{ fontSize: 32, mr: 2, color: 'primary.main' }} />
        <Typography variant="h4" component="h1">
          智能翻译助手设置
        </Typography>
      </Box>

      {/* 消息提示 */}
      {message && (
        <Alert 
          severity={message.type} 
          sx={{ mb: 3 }} 
          onClose={() => setMessage(null)}
        >
          {message.text}
        </Alert>
      )}

      {/* 标签页 */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab icon={<TranslateIcon />} label="翻译设置" />
            <Tab icon={<ApiIcon />} label="API配置" />
          </Tabs>
        </Box>

        {/* 翻译设置 */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* 语言设置 */}
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">语言设置</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                  <FormControl fullWidth>
                    <InputLabel>源语言</InputLabel>
                    <Select
                      value={settings.source_language}
                      label="源语言"
                      onChange={(e) => setSettings({...settings, source_language: e.target.value})}
                    >
                      <MenuItem value="auto">自动检测</MenuItem>
                      <MenuItem value="en">英语</MenuItem>
                      <MenuItem value="zh">中文</MenuItem>
                      <MenuItem value="ja">日语</MenuItem>
                      <MenuItem value="ko">韩语</MenuItem>
                      <MenuItem value="fr">法语</MenuItem>
                      <MenuItem value="de">德语</MenuItem>
                      <MenuItem value="es">西班牙语</MenuItem>
                      <MenuItem value="ru">俄语</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControl fullWidth>
                    <InputLabel>目标语言</InputLabel>
                    <Select
                      value={settings.target_language}
                      label="目标语言"
                      onChange={(e) => setSettings({...settings, target_language: e.target.value})}
                    >
                      <MenuItem value="zh">中文</MenuItem>
                      <MenuItem value="en">英语</MenuItem>
                      <MenuItem value="ja">日语</MenuItem>
                      <MenuItem value="ko">韩语</MenuItem>
                      <MenuItem value="fr">法语</MenuItem>
                      <MenuItem value="de">德语</MenuItem>
                      <MenuItem value="es">西班牙语</MenuItem>
                      <MenuItem value="ru">俄语</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </AccordionDetails>
            </Accordion>

            {/* 功能设置 */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">功能设置</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.auto_translate}
                        onChange={(e) => setSettings({...settings, auto_translate: e.target.checked})}
                      />
                    }
                    label="自动翻译（划词即翻译）"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.show_pronunciation}
                        onChange={(e) => setSettings({...settings, show_pronunciation: e.target.checked})}
                      />
                    }
                    label="显示发音"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.show_definitions}
                        onChange={(e) => setSettings({...settings, show_definitions: e.target.checked})}
                      />
                    }
                    label="显示词典释义"
                  />
                  
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.show_examples}
                        onChange={(e) => setSettings({...settings, show_examples: e.target.checked})}
                      />
                    }
                    label="显示例句"
                  />
                </Box>
              </AccordionDetails>
            </Accordion>
          </Box>
        </TabPanel>

        {/* API配置 */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Alert severity="info">
              配置API密钥后可以使用更高质量的翻译服务。如果不配置，将使用免费的Google翻译服务。
            </Alert>

            {/* 首选提供商 */}
            <FormControl fullWidth>
              <InputLabel>首选翻译提供商</InputLabel>
              <Select
                value={apiSettings.preferred_provider}
                label="首选翻译提供商"
                onChange={(e) => setApiSettings({...apiSettings, preferred_provider: e.target.value})}
              >
                <MenuItem value="google">Google翻译（免费）</MenuItem>
                <MenuItem value="openai">OpenAI GPT</MenuItem>
                <MenuItem value="kimi">Kimi AI</MenuItem>
              </Select>
            </FormControl>

            <Divider />

            {/* Google API */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Google翻译API</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <TextField
                  fullWidth
                  label="Google API Key"
                  type="password"
                  value={apiSettings.google_api_key}
                  onChange={(e) => setApiSettings({...apiSettings, google_api_key: e.target.value})}
                  helperText="可选：配置后可使用官方Google翻译API"
                />
              </AccordionDetails>
            </Accordion>

            {/* OpenAI API */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">OpenAI API</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <TextField
                    fullWidth
                    label="OpenAI API Key"
                    type="password"
                    value={apiSettings.openai_api_key}
                    onChange={(e) => setApiSettings({...apiSettings, openai_api_key: e.target.value})}
                  />
                  <TextField
                    fullWidth
                    label="API端点"
                    value={apiSettings.openai_endpoint}
                    onChange={(e) => setApiSettings({...apiSettings, openai_endpoint: e.target.value})}
                    helperText="默认：https://api.openai.com/v1/chat/completions"
                  />
                </Box>
              </AccordionDetails>
            </Accordion>

            {/* Kimi API */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Kimi AI API</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <TextField
                    fullWidth
                    label="Kimi API Key"
                    type="password"
                    value={apiSettings.kimi_api_key}
                    onChange={(e) => setApiSettings({...apiSettings, kimi_api_key: e.target.value})}
                  />
                  <TextField
                    fullWidth
                    label="API端点"
                    value={apiSettings.kimi_endpoint}
                    onChange={(e) => setApiSettings({...apiSettings, kimi_endpoint: e.target.value})}
                    helperText="默认：https://api.moonshot.cn/v1/chat/completions"
                  />
                </Box>
              </AccordionDetails>
            </Accordion>
          </Box>
        </TabPanel>
      </Card>

      {/* 保存按钮 */}
      <Box sx={{ mt: 3, textAlign: 'center' }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<SaveIcon />}
          onClick={saveSettings}
        >
          保存设置
        </Button>
      </Box>
    </Container>
  );
}

// 渲染应用
const root = ReactDOM.createRoot(document.getElementById('app')!);
root.render(
  <React.StrictMode>
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <OptionsPage />
    </ThemeProvider>
  </React.StrictMode>
);
