## 侧边翻译 Q & A

* __Q: 这个插件有没有xxx功能? / 这个插件为什么不能xxx?__
  
  __A:__ 本插件的详细功能列表请看[这里](./插件介绍.md)；

* __Q: 我在使用过程中遇到了问题，该怎么办？__

  __A:__ 请给我们发邮件([nickyc975](mailto:<EMAIL>), [<PERSON>](mailto:<EMAIL>))或在本插件的github仓库中给我们提issue；

* __Q: 插件安装/更新后无法使用？__

  __A:__ 在安装或更新插件后，请刷新需要翻译的页面以（重新）启用翻译功能；

* __Q: 选中文本后翻译按钮不弹出？__
  
  __A:__ 你需要在设置中选中`启用划词翻译`选项并刷新需要翻译的页面；

* __Q: 如何在PDF文件中启用划词翻译和双击翻译？__

  __A:__ 首先你需要授予本插件`允许访问文件网址`的权限， __图一__ 给出了授予方法。然后请在本插件的设置中选中`使用内置的PDF查看器`并刷新需要翻译的PDF文件。

* __Q: 翻译结果老是弹出收回影响阅读怎么办？__

  __A:__ 点击翻译结果展示框右上角的图钉按钮即可固定展示框。

* __Q: 为什么我不能安装从"Releases"页面下载的* .crx文件？__

  __A:__ 如果你的错误信息是 "``无法从该网站添加应用、扩展程序和用户脚本``"，可以尝试以下步骤:

  1. 在你的chrome里打开url: ``chrome://flags/#extensions-on-chrome-urls``。

  2. 如果"``Extensions on chrome:// URLs``"是关闭的，请打开这个开关并且重启chrome。

  3. 打开这个url: ``chrome://extensions/``。

  4. 请确认``开发者模式``是打开的。如果这个开关是关闭的，请打开开关并且刷新这个页面。

  5. 现在，您可以尝试将* .crx文件再次拖放到此页面上。

* __Q: 翻译框为什么变成悬浮的了？怎么让它变回右侧（左侧）固定？__

  __A:__ 这是侧边翻译2.0引入的新功能，即支持悬浮框显示。当你将鼠标光标移动到翻译框顶部的蓝色区域时，按住鼠标左键即可拖动翻译框。

  当翻译框固定在侧面时，拖动翻译框可以让它变成悬浮的。

  如果你想让它变回侧边固定，请 __向右侧（左侧）拖动翻译框直到鼠标光标到达边界__，即可让它变回侧边固定。

  参考图二的演示（gif文件较大，请耐心等待）。

* __Q: 翻译框超出界面导致我看不见翻译结果怎么办？__

  如果悬浮的翻译框超出了页面并且拖不回来，可以尝试改变页面缩放使它回到页面内，具体操作为：按住Ctrl键同时滚动鼠标滚轮，即可调整页面缩放。

### 图一：允许侧边翻译访问文件链接

![grant_access](../../images/grant_access.gif)

### 图二：在悬浮模式和贴边模式之间转换

![floating_fixed_switch](../../images/floating_fixed_switch.gif)