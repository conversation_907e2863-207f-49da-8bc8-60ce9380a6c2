@import '../common/style/input.styl';

html, body {
    width: 100%;
    padding: 0;
    margin: 0;
    background-color: #eeeeee;
}

#option {
    padding: 0 10%;
    margin: auto;
    max-width: 840px;
    margin: 0px auto;
    box-shadow: 0px 0px 50px rgb(200, 200, 200, 0.5);
    background-color: white;
}

header {
    width: 100%;
    height: 25%;
    padding: 5% 0;
    font-size: 64px;
    font-weight: bold;
    color: color-primary;
    display: flex;
    align-items: center;
}

.row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 4em;
    margin: 3% 5%;
    flex: 1 1;
}

.column {
    display: flex;
    align-items: center;
    min-width: 30%;
    max-width: 70%;
}

.checked-label {
    cursor: pointer;
    font-size: medium;
    flex: 3 3 50%;
    justify-content: space-between;
}

.radio-label {
    cursor: pointer;
    font-size: medium;
}

.setting-title {
    font-size: large;
    font-weight: bold;
}

.space {
    display: inline-block;
    width: 5px;
}

footer {
    padding: 5% 0;
    text-align: center;
    font-size: medium;
}
