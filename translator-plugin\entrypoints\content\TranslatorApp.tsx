import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { TranslatorSidebar } from './TranslatorSidebar';
import { useSidebarStore } from '../../shared/stores/useSidebarStore';
import { useThemeStore } from '../../shared/stores/useThemeStore';
import { createMuiTheme } from '../../shared/theme/themeConfig';

export const TranslatorApp: React.FC = () => {
  const { isVisible } = useSidebarStore();
  const { currentTheme } = useThemeStore();

  // 创建MUI主题
  const theme = createMuiTheme(currentTheme);

  // 添加调试信息
  React.useEffect(() => {
    console.log('🎨 TranslatorApp state:', {
      isVisible,
      currentTheme,
      themeMode: theme.palette.mode
    });
  }, [isVisible, currentTheme, theme]);

  if (!isVisible) {
    console.log('🚫 TranslatorApp: Sidebar not visible, returning null');
    return null;
  }

  console.log('✅ TranslatorApp: Rendering sidebar');

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <TranslatorSidebar />
    </ThemeProvider>
  );
};
