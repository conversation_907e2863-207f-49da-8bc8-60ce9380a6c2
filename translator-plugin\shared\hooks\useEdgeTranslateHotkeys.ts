// EdgeTranslate快捷键系统
// 复制EdgeTranslate的所有快捷键功能

export interface EdgeTranslateHotkeyActions {
  togglePanel: () => void;
  pinPanel: () => void;
  closePanel: () => void;
  swapLanguages: () => void;
  translateSelectedText: () => void;
  showSettings: () => void;
  toggleSidebar: () => void;
  focusInput: () => void;
  clearInput: () => void;
  copyResult: () => void;
  speakOriginal: () => void;
  speakTranslation: () => void;
}

export interface EdgeTranslateHotkey {
  key: string;
  altKey?: boolean;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  action: keyof EdgeTranslateHotkeyActions;
  description: string;
  enabled: boolean;
}

// EdgeTranslate默认快捷键配置
export const defaultEdgeTranslateHotkeys: EdgeTranslateHotkey[] = [
  {
    key: 'q',
    altKey: true,
    action: 'togglePanel',
    description: '显示/隐藏翻译面板',
    enabled: true,
  },
  {
    key: 'x',
    altKey: true,
    action: 'pinPanel',
    description: '固定/取消固定面板',
    enabled: true,
  },
  {
    key: 'c',
    altKey: true,
    action: 'closePanel',
    description: '关闭翻译面板',
    enabled: true,
  },
  {
    key: 's',
    altKey: true,
    action: 'swapLanguages',
    description: '交换源语言和目标语言',
    enabled: true,
  },
  {
    key: 't',
    altKey: true,
    action: 'translateSelectedText',
    description: '翻译选中的文本',
    enabled: true,
  },
  {
    key: 'o',
    altKey: true,
    action: 'showSettings',
    description: '打开设置页面',
    enabled: true,
  },
  {
    key: 'w',
    altKey: true,
    action: 'toggleSidebar',
    description: '显示/隐藏侧边栏',
    enabled: true,
  },
  {
    key: 'f',
    altKey: true,
    action: 'focusInput',
    description: '聚焦到输入框',
    enabled: true,
  },
  {
    key: 'Delete',
    ctrlKey: true,
    action: 'clearInput',
    description: '清空输入框',
    enabled: true,
  },
  {
    key: 'c',
    ctrlKey: true,
    shiftKey: true,
    action: 'copyResult',
    description: '复制翻译结果',
    enabled: true,
  },
  {
    key: 'r',
    altKey: true,
    action: 'speakOriginal',
    description: '朗读原文',
    enabled: true,
  },
  {
    key: 'e',
    altKey: true,
    action: 'speakTranslation',
    description: '朗读译文',
    enabled: true,
  },
];

// 快捷键匹配函数
export const matchesHotkey = (event: KeyboardEvent, hotkey: EdgeTranslateHotkey): boolean => {
  return (
    event.key.toLowerCase() === hotkey.key.toLowerCase() &&
    !!event.altKey === !!hotkey.altKey &&
    !!event.ctrlKey === !!hotkey.ctrlKey &&
    !!event.shiftKey === !!hotkey.shiftKey &&
    !!event.metaKey === !!hotkey.metaKey
  );
};

// 快捷键描述生成器
export const getHotkeyDescription = (hotkey: EdgeTranslateHotkey): string => {
  const keys: string[] = [];
  
  if (hotkey.ctrlKey) keys.push('Ctrl');
  if (hotkey.altKey) keys.push('Alt');
  if (hotkey.shiftKey) keys.push('Shift');
  if (hotkey.metaKey) keys.push('Meta');
  
  keys.push(hotkey.key.toUpperCase());
  
  return keys.join(' + ');
};

// 快捷键管理器类
export class EdgeTranslateHotkeyManager {
  private hotkeys: EdgeTranslateHotkey[];
  private actions: EdgeTranslateHotkeyActions;
  private isEnabled: boolean = true;
  private listeners: Set<(event: KeyboardEvent) => void> = new Set();

  constructor(actions: EdgeTranslateHotkeyActions, customHotkeys?: EdgeTranslateHotkey[]) {
    this.hotkeys = customHotkeys || [...defaultEdgeTranslateHotkeys];
    this.actions = actions;
    this.bindEvents();
  }

  private bindEvents() {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!this.isEnabled) return;

      // 忽略在输入框中的快捷键
      const target = event.target as HTMLElement;
      if (this.shouldIgnoreTarget(target)) return;

      // 查找匹配的快捷键
      const matchedHotkey = this.hotkeys.find(hotkey => 
        hotkey.enabled && matchesHotkey(event, hotkey)
      );

      if (matchedHotkey) {
        event.preventDefault();
        event.stopPropagation();
        
        // 执行对应的动作
        const action = this.actions[matchedHotkey.action];
        if (action && typeof action === 'function') {
          action();
        }

        // 通知监听器
        this.listeners.forEach(listener => listener(event));
      }
    };

    document.addEventListener('keydown', handleKeyDown, true);
    
    // 保存引用以便后续清理
    this.cleanup = () => {
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }

  private cleanup: (() => void) | null = null;

  private shouldIgnoreTarget(target: HTMLElement): boolean {
    // 忽略的元素类型
    const ignoredTags = ['input', 'textarea', 'select'];
    const tagName = target.tagName.toLowerCase();
    
    if (ignoredTags.includes(tagName)) return true;
    
    // 忽略可编辑元素
    if (target.isContentEditable) return true;
    
    // 忽略特定类名的元素
    const ignoredClasses = [
      'translator-input',
      'translation-input',
      'editable',
    ];
    
    for (const className of ignoredClasses) {
      if (target.classList.contains(className) || target.closest(`.${className}`)) {
        return true;
      }
    }
    
    return false;
  }

  // 启用/禁用快捷键
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  // 更新快捷键配置
  updateHotkeys(hotkeys: EdgeTranslateHotkey[]) {
    this.hotkeys = [...hotkeys];
  }

  // 更新单个快捷键
  updateHotkey(action: keyof EdgeTranslateHotkeyActions, hotkey: Partial<EdgeTranslateHotkey>) {
    const index = this.hotkeys.findIndex(h => h.action === action);
    if (index !== -1) {
      this.hotkeys[index] = { ...this.hotkeys[index], ...hotkey };
    }
  }

  // 启用/禁用特定快捷键
  setHotkeyEnabled(action: keyof EdgeTranslateHotkeyActions, enabled: boolean) {
    const hotkey = this.hotkeys.find(h => h.action === action);
    if (hotkey) {
      hotkey.enabled = enabled;
    }
  }

  // 获取所有快捷键
  getHotkeys(): EdgeTranslateHotkey[] {
    return [...this.hotkeys];
  }

  // 获取特定动作的快捷键
  getHotkey(action: keyof EdgeTranslateHotkeyActions): EdgeTranslateHotkey | undefined {
    return this.hotkeys.find(h => h.action === action);
  }

  // 添加事件监听器
  addListener(listener: (event: KeyboardEvent) => void) {
    this.listeners.add(listener);
  }

  // 移除事件监听器
  removeListener(listener: (event: KeyboardEvent) => void) {
    this.listeners.delete(listener);
  }

  // 销毁管理器
  destroy() {
    if (this.cleanup) {
      this.cleanup();
    }
    this.listeners.clear();
  }
}

// 创建快捷键管理器的工厂函数
export const createEdgeTranslateHotkeyManager = (
  actions: EdgeTranslateHotkeyActions,
  customHotkeys?: EdgeTranslateHotkey[]
): EdgeTranslateHotkeyManager => {
  return new EdgeTranslateHotkeyManager(actions, customHotkeys);
};

// 快捷键存储键名
export const HOTKEYS_STORAGE_KEY = 'edge-translate-hotkeys';

// 从存储加载快捷键配置
export const loadHotkeysFromStorage = async (): Promise<EdgeTranslateHotkey[]> => {
  try {
    const result = await chrome.storage.local.get(HOTKEYS_STORAGE_KEY);
    const stored = result[HOTKEYS_STORAGE_KEY];
    
    if (stored && Array.isArray(stored)) {
      // 合并默认配置和存储的配置
      return defaultEdgeTranslateHotkeys.map(defaultHotkey => {
        const storedHotkey = stored.find((h: EdgeTranslateHotkey) => h.action === defaultHotkey.action);
        return storedHotkey ? { ...defaultHotkey, ...storedHotkey } : defaultHotkey;
      });
    }
  } catch (error) {
    console.error('Failed to load hotkeys from storage:', error);
  }
  
  return [...defaultEdgeTranslateHotkeys];
};

// 保存快捷键配置到存储
export const saveHotkeysToStorage = async (hotkeys: EdgeTranslateHotkey[]): Promise<void> => {
  try {
    await chrome.storage.local.set({
      [HOTKEYS_STORAGE_KEY]: hotkeys
    });
  } catch (error) {
    console.error('Failed to save hotkeys to storage:', error);
  }
};

// 重置快捷键为默认配置
export const resetHotkeysToDefault = async (): Promise<EdgeTranslateHotkey[]> => {
  const defaultHotkeys = [...defaultEdgeTranslateHotkeys];
  await saveHotkeysToStorage(defaultHotkeys);
  return defaultHotkeys;
};
