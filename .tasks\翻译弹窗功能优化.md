侧边栏 UI
• 位置：页面右侧固定抽屉，宽度 300 px，不遮挡正文，用户可自由自动，并且可拖拽。可拉大、拉小。点击页面其他地方，自动关闭弹窗。当用户将弹窗移动到屏幕右侧或者屏幕左侧时，弹窗会自动上下会占满浏览器页面屏幕。当用户点击钉住按钮时，弹窗会固定在屏幕上，用户点击页面其他地方，弹窗不会自动关闭，并且用户点击滑动其他字，弹窗还是固定位置在那，显示对应的翻译结果内容。
• 支持「最小化」「关闭」「钉住」按钮。
• 背景半透明磨砂，圆角 8 px，跟随系统深浅色。
多引擎结果区
• 弹窗左侧上方，替代现在的翻译结果的位置，切换 Tab：Google 翻译 / OpenAI GPT-4o-mini（免费）/ kimi…
• 每条结果带“星级”或“复制”按钮（占位符即可，后期可接入评分）。
折叠卡片
• 三折叠卡片：
– 翻译结果并显示发音按钮、音标，增加复制按钮，图标标识
– 如果是单词，请显示单词详解，如显示名词、近同义词、名词等，可点击展开
- 如果是单词还是可以单词定义
– 例句（点击展开）
• 用 MUI Accordion 实现，动画 120 ms。
生词本入口——可后期做为按钮
• 结果栏右侧“⭐”图标 → 调用 Supabase user_words 表插入，未登录则提示登录。
• 仅做按钮与空状态提示，列表页放到管理后台后期再做。
整体网页翻译开关
• 侧边栏底部加「翻译整页」按钮（占位 UI），后期接入 Google Webpage Translate API。
• 当前版本点击后 toast「即将上线」即可。
快捷键 & 右键菜单
• 快捷键：Alt+Q 呼出/隐藏侧边栏（避免与截图 Alt+S 冲突）。
• 右键菜单：增加「侧边翻译」项 → 直接打开侧边栏并翻译选中文本。
深浅色自适应
• 监听 prefers-color-scheme，CSS 变量两套配色。
• 侧边栏顶部加「🌗」一键切换。

有一个想法：是否能让用户再使用插件时选择插件的角色，例如是英语教师，我可以在弹窗显示滑动的单词语句，显示对应语法教学以及其他用途，当是营销大师时，可以基于用户滑动的单词语句做营销话术生成，后面再考虑是否要作为功能点上线。