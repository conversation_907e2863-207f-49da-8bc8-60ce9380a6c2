### Структура проекта
```
+-- config
| +-- webpack.base.config.js
| +-- webpack.dev.config.js
| +-- webpack.prod.config.js
+-- src
| +-- contents
| | +-- pdf.js
| | +-- select.js
| +-- display
| | +-- display.js
| | +-- engine.js
| | +-- template.js
| +-- options
| +-- popup
| +-- background.js
| +-- translate.js
+-- static
| +-- _locales
| +-- icon
| +-- pdf
```
#### config

Хранит три файла конфигурации веб-пакета,

+ `webpack.base.config.js` - общая конфигурация

+ `webpack.dev.config.js` - конфигурация среды разработки

+ `webpack.prod.config.js` - конфигурация рабочей среды

#### contents

Содержит два скрипта контента для расширения

+ `pdf.js` используется для автоматического перехода к встроенной программе чтения PDF, при просмотре PDF-файла в браузере (представлено [pdf.js](https://github.com/mozilla/pdf.js))

+ `select.js` используется для реализации перевода выбранного текста
  + диаграмма, демонстрирующая логику перевода выбранного текста![diagram](../../images/selecting_translate_diagram.jpg)

#### display

Отвечает за создание боковой панели в текущем окне для отображения результатов перевода.
В этом модуле мы использовали наш собственный простой движок рендеринга шаблонов, отвечающий за рендеринг структурированных данных и статичных страниц результатов перевода, генерирющих HTML-контент для отображения.

+ `engine.js` - простой движок рендеринга (функция рендеринга).
+ `tempalte.js` - хранит контент-шаблон.

+ `display.js` использует функцию `render()` из `engine.js` для рендеринга страницы на боковой панели. Отвечает за создание боковой панели, всплывающего окна (popup) боковой панели и закрытие этой панели.

#### options

Отвечает за страницу параметров расширения.

#### popup

Отвечает за страницу всплывающего окна (popup).
Включает поле ввода для запроса слов и параметров для исходного и целевого языков.

#### background.js

Отвечает за инициализацию настроек по умолчанию при установке расширения и пересылку сообщений между разными частями расширения во время работы расширения.

#### translate.js

Отвечает за основные функции перевода.

+ Отправка запросов перевода.

+ Обработка полученных данных и их упаковка.

+ Передача объект результата перевода в отображаемый модуль.

#### _locales

Отвечает за интернационализацию расширения.

Сейчас поддерживаются Китайский упрощенный, Китайский традиционный, Английский и Русский.

#### icon

Хранит файлов иконок.

#### pdf

Хранит файлы [pdf.js](https://github.com/mozilla/pdf.js). Во время упаковки они будут представлены как статические файлы.
