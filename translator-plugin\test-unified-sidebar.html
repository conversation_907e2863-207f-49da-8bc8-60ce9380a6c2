<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一侧边栏测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-text {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            text-align: center;
        }
        
        h2 {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <h1>🔧 统一侧边栏功能测试</h1>
    
    <div class="instructions">
        <h3>📋 测试说明</h3>
        <p>本页面用于测试修复后的翻译功能。现在应该只显示一个现代化的侧边栏，不再有任何调试指示器。</p>
        <ul>
            <li><strong>选择文本</strong>：选择下面的任意文本，应该只显示新的侧边栏</li>
            <li><strong>无调试元素</strong>：不应该看到"Sidebar Active"或"EdgeTranslate Style Active"等调试元素</li>
            <li><strong>默认拖拽</strong>：侧边栏出现后立即可以拖拽移动，无需先钉住</li>
            <li><strong>快捷键</strong>：按 Alt+Q 切换侧边栏显示/隐藏</li>
            <li><strong>多引擎</strong>：侧边栏应支持 Google、OpenAI、Kimi 三个翻译引擎</li>
            <li><strong>边缘吸附</strong>：拖拽到屏幕边缘时自动吸附并调整大小</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🇬🇧 英文测试文本</h2>
        <div class="test-text">
            <p>Hello world! This is a simple English sentence for testing the translation functionality.</p>
        </div>
        <div class="test-text">
            <p>The quick brown fox jumps over the lazy dog. This sentence contains every letter of the English alphabet.</p>
        </div>
        <div class="test-text">
            <p>Artificial intelligence is transforming the way we work, learn, and communicate in the modern world.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🇨🇳 中文测试文本</h2>
        <div class="test-text">
            <p>你好世界！这是一个用于测试翻译功能的简单中文句子。</p>
        </div>
        <div class="test-text">
            <p>人工智能正在改变我们在现代世界中工作、学习和交流的方式。</p>
        </div>
        <div class="test-text">
            <p>科技发展日新月异，我们需要不断学习新知识来适应时代的变化。</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🔤 单词测试</h2>
        <div class="test-text">
            <p>测试单词详解功能：<span class="highlight">beautiful</span>, <span class="highlight">technology</span>, <span class="highlight">innovation</span></p>
        </div>
        <div class="test-text">
            <p>中文单词测试：<span class="highlight">科技</span>, <span class="highlight">创新</span>, <span class="highlight">发展</span></p>
        </div>
    </div>

    <div class="test-section">
        <h2>📝 长文本测试</h2>
        <div class="test-text">
            <p>In the rapidly evolving landscape of modern technology, artificial intelligence has emerged as one of the most transformative forces of our time. From machine learning algorithms that can predict consumer behavior to natural language processing systems that can understand and generate human-like text, AI is reshaping industries and redefining what's possible in the digital age.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ 预期结果</h2>
        <ul>
            <li>✅ 选择任意文本后，只显示一个现代化的侧边栏</li>
            <li>✅ 不再出现任何调试指示器（"Sidebar Active"、"EdgeTranslate Style Active"等）</li>
            <li>✅ 不再出现旧的翻译按钮和浮动面板</li>
            <li>✅ 侧边栏默认就可以拖拽移动，体验与原EdgeTranslate一致</li>
            <li>✅ 侧边栏支持多引擎翻译切换</li>
            <li>✅ 支持折叠卡片显示翻译结果、单词详解、例句</li>
            <li>✅ 支持边缘吸附功能，拖拽到边缘自动调整</li>
            <li>✅ 钉住功能控制点击外部是否关闭</li>
            <li>✅ 快捷键 Alt+Q 正常工作</li>
            <li>✅ 界面干净整洁，无多余元素</li>
        </ul>
    </div>

    <script>
        // 添加一些测试辅助功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Test page loaded - ready for sidebar testing');
            
            // 添加选择文本的辅助提示
            document.addEventListener('mouseup', function() {
                const selection = window.getSelection();
                if (selection && selection.toString().trim()) {
                    console.log('📝 Text selected:', selection.toString().trim());
                }
            });
        });
    </script>
</body>
</html>
