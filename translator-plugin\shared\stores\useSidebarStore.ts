import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type TranslationEngine = 'google' | 'openai' | 'kimi';
export type AccordionSection = 'translation-result' | 'word-definition' | 'example-sentences';

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

interface SidebarState {
  isVisible: boolean;
  isPinned: boolean;
  position: Position;
  size: Size;
  isDragging: boolean;
  isResizing: boolean;
  activeTab: TranslationEngine;
  expandedAccordions: AccordionSection[];
  isAutoSnapped: boolean;
  snapPosition: 'left' | 'right' | 'none';
}

interface SidebarActions {
  toggleVisibility: () => void;
  togglePin: () => void;
  updatePosition: (position: Position) => void;
  updateSize: (size: Size) => void;
  setDragging: (isDragging: boolean) => void;
  setResizing: (isResizing: boolean) => void;
  setActiveTab: (tab: TranslationEngine) => void;
  toggleAccordion: (section: AccordionSection) => void;
  setAutoSnapped: (isSnapped: boolean, position: 'left' | 'right' | 'none') => void;
  resetToDefaults: () => void;
}

const defaultState: SidebarState = {
  isVisible: false,
  isPinned: false,
  position: { x: 20, y: 100 },
  size: { width: 300, height: 500 },
  isDragging: false,
  isResizing: false,
  activeTab: 'google',
  expandedAccordions: ['translation-result'],
  isAutoSnapped: false,
  snapPosition: 'none',
};

export const useSidebarStore = create<SidebarState & SidebarActions>()(
  persist(
    (set, get) => ({
      ...defaultState,

      toggleVisibility: () => {
        set((state) => ({ isVisible: !state.isVisible }));
      },

      togglePin: () => {
        set((state) => ({ isPinned: !state.isPinned }));
      },

      updatePosition: (position: Position) => {
        set({ position });
      },

      updateSize: (size: Size) => {
        set({ size });
      },

      setDragging: (isDragging: boolean) => {
        set({ isDragging });
      },

      setResizing: (isResizing: boolean) => {
        set({ isResizing });
      },

      setActiveTab: (tab: TranslationEngine) => {
        set({ activeTab: tab });
      },

      toggleAccordion: (section: AccordionSection) => {
        set((state) => {
          const expanded = state.expandedAccordions;
          const isExpanded = expanded.includes(section);
          
          if (isExpanded) {
            return {
              expandedAccordions: expanded.filter(s => s !== section)
            };
          } else {
            return {
              expandedAccordions: [...expanded, section]
            };
          }
        });
      },

      setAutoSnapped: (isSnapped: boolean, position: 'left' | 'right' | 'none') => {
        set({ isAutoSnapped: isSnapped, snapPosition: position });
      },

      resetToDefaults: () => {
        set(defaultState);
      },
    }),
    {
      name: 'translator-sidebar-store',
      partialize: (state) => ({
        isPinned: state.isPinned,
        position: state.position,
        size: state.size,
        activeTab: state.activeTab,
        expandedAccordions: state.expandedAccordions,
      }),
    }
  )
);
