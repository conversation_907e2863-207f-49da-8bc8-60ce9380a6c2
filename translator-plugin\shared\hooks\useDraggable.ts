import { useCallback, useEffect, useRef, RefObject } from 'react';
import { Position } from '../stores/useSidebarStore';

export interface DragBoundaries {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
}

export interface DragStartEvent {
  startX: number;
  startY: number;
  elementX: number;
  elementY: number;
}

export interface DragEvent {
  currentX: number;
  currentY: number;
  deltaX: number;
  deltaY: number;
  elementX: number;
  elementY: number;
}

export interface DragEndEvent {
  finalX: number;
  finalY: number;
  deltaX: number;
  deltaY: number;
}

export interface DraggableOptions {
  onDragStart?: (event: DragStartEvent) => void;
  onDrag?: (event: DragEvent) => void;
  onDragEnd?: (event: DragEndEvent) => void;
  boundaries?: DragBoundaries;
  snapToEdges?: boolean;
  snapThreshold?: number;
  disabled?: boolean;
}

export interface DragHandlers {
  handleMouseDown: (event: React.MouseEvent) => void;
  handleTouchStart: (event: React.TouchEvent) => void;
}

export const useDraggable = (
  elementRef: RefObject<HTMLElement>,
  options: DraggableOptions = {}
): DragHandlers => {
  const {
    onDragStart,
    onDrag,
    onDragEnd,
    boundaries,
    snapToEdges = false,
    snapThreshold = 50,
    disabled = false,
  } = options;

  const isDragging = useRef(false);
  const dragStart = useRef({ x: 0, y: 0 });
  const elementStart = useRef({ x: 0, y: 0 });

  // 获取视口边界
  const getViewportBoundaries = useCallback((): DragBoundaries => {
    if (boundaries) return boundaries;
    
    return {
      minX: 0,
      maxX: window.innerWidth,
      minY: 0,
      maxY: window.innerHeight,
    };
  }, [boundaries]);

  // 约束位置到边界内
  const constrainPosition = useCallback((x: number, y: number): Position => {
    const bounds = getViewportBoundaries();
    const element = elementRef.current;
    
    if (!element) return { x, y };
    
    const rect = element.getBoundingClientRect();
    const constrainedX = Math.max(bounds.minX, Math.min(x, bounds.maxX - rect.width));
    const constrainedY = Math.max(bounds.minY, Math.min(y, bounds.maxY - rect.height));
    
    return { x: constrainedX, y: constrainedY };
  }, [elementRef, getViewportBoundaries]);

  // 自动贴边功能
  const snapToEdge = useCallback((x: number, y: number): Position => {
    if (!snapToEdges) return { x, y };
    
    const bounds = getViewportBoundaries();
    const element = elementRef.current;
    
    if (!element) return { x, y };
    
    const rect = element.getBoundingClientRect();
    let finalX = x;
    let finalY = y;
    
    // 检查是否接近左边缘
    if (x < snapThreshold) {
      finalX = bounds.minX;
    }
    // 检查是否接近右边缘
    else if (x + rect.width > bounds.maxX - snapThreshold) {
      finalX = bounds.maxX - rect.width;
    }
    
    // 检查是否接近顶部边缘
    if (y < snapThreshold) {
      finalY = bounds.minY;
    }
    // 检查是否接近底部边缘
    else if (y + rect.height > bounds.maxY - snapThreshold) {
      finalY = bounds.maxY - rect.height;
    }
    
    return { x: finalX, y: finalY };
  }, [snapToEdges, snapThreshold, elementRef, getViewportBoundaries]);

  // 鼠标移动处理
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging.current || disabled) return;
    
    event.preventDefault();
    
    const deltaX = event.clientX - dragStart.current.x;
    const deltaY = event.clientY - dragStart.current.y;
    
    const newX = elementStart.current.x + deltaX;
    const newY = elementStart.current.y + deltaY;
    
    const constrainedPosition = constrainPosition(newX, newY);
    
    // 更新元素位置
    if (elementRef.current) {
      elementRef.current.style.left = `${constrainedPosition.x}px`;
      elementRef.current.style.top = `${constrainedPosition.y}px`;
    }
    
    // 触发拖拽事件
    onDrag?.({
      currentX: event.clientX,
      currentY: event.clientY,
      deltaX,
      deltaY,
      elementX: constrainedPosition.x,
      elementY: constrainedPosition.y,
    });
  }, [disabled, constrainPosition, elementRef, onDrag]);

  // 鼠标释放处理
  const handleMouseUp = useCallback((event: MouseEvent) => {
    if (!isDragging.current) return;
    
    isDragging.current = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    
    const deltaX = event.clientX - dragStart.current.x;
    const deltaY = event.clientY - dragStart.current.y;
    
    const finalX = elementStart.current.x + deltaX;
    const finalY = elementStart.current.y + deltaY;
    
    // 应用边界约束和自动贴边
    const constrainedPosition = constrainPosition(finalX, finalY);
    const snappedPosition = snapToEdge(constrainedPosition.x, constrainedPosition.y);
    
    // 更新最终位置
    if (elementRef.current) {
      elementRef.current.style.left = `${snappedPosition.x}px`;
      elementRef.current.style.top = `${snappedPosition.y}px`;
      
      // 如果发生了贴边，添加平滑动画
      if (snappedPosition.x !== constrainedPosition.x || snappedPosition.y !== constrainedPosition.y) {
        elementRef.current.style.transition = 'all 0.3s ease-out';
        setTimeout(() => {
          if (elementRef.current) {
            elementRef.current.style.transition = '';
          }
        }, 300);
      }
    }
    
    // 触发拖拽结束事件
    onDragEnd?.({
      finalX: snappedPosition.x,
      finalY: snappedPosition.y,
      deltaX,
      deltaY,
    });
    
    // 移除事件监听器
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }, [constrainPosition, snapToEdge, elementRef, onDragEnd, handleMouseMove]);

  // 鼠标按下处理
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (disabled || event.button !== 0) return; // 只处理左键
    
    event.preventDefault();
    event.stopPropagation();
    
    const element = elementRef.current;
    if (!element) return;
    
    const rect = element.getBoundingClientRect();
    
    isDragging.current = true;
    dragStart.current = { x: event.clientX, y: event.clientY };
    elementStart.current = { x: rect.left, y: rect.top };
    
    // 设置拖拽样式
    document.body.style.cursor = 'grabbing';
    document.body.style.userSelect = 'none';
    
    // 触发拖拽开始事件
    onDragStart?.({
      startX: event.clientX,
      startY: event.clientY,
      elementX: rect.left,
      elementY: rect.top,
    });
    
    // 添加事件监听器
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [disabled, elementRef, onDragStart, handleMouseMove, handleMouseUp]);

  // 触摸开始处理
  const handleTouchStart = useCallback((event: React.TouchEvent) => {
    if (disabled) return;
    
    const touch = event.touches[0];
    if (!touch) return;
    
    // 将触摸事件转换为鼠标事件格式
    const mouseEvent = {
      clientX: touch.clientX,
      clientY: touch.clientY,
      button: 0,
      preventDefault: () => event.preventDefault(),
      stopPropagation: () => event.stopPropagation(),
    } as React.MouseEvent;
    
    handleMouseDown(mouseEvent);
  }, [disabled, handleMouseDown]);

  // 清理函数
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  return {
    handleMouseDown,
    handleTouchStart,
  };
};
