import React, { useState } from 'react';
import { 
  Button, 
  Box, 
  Snackbar, 
  Alert,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Translate as TranslateIcon,
  Language as LanguageIcon,
  Info as InfoIcon
} from '@mui/icons-material';

export const PageTranslationButton: React.FC = () => {
  const [isTranslating, setIsTranslating] = useState(false);
  const [showToast, setShowToast] = useState(false);

  const handlePageTranslation = async () => {
    setIsTranslating(true);
    
    // 模拟翻译过程
    setTimeout(() => {
      setIsTranslating(false);
      setShowToast(true);
    }, 1000);
  };

  const handleCloseToast = () => {
    setShowToast(false);
  };

  return (
    <>
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        p: 1
      }}>
        <Tooltip
          title="整页翻译功能即将上线，敬请期待！"
          placement="top"
        >
          <Button
            variant="outlined"
            size="small"
            startIcon={isTranslating ? <CircularProgress size={14} color="inherit" /> : <TranslateIcon />}
            onClick={handlePageTranslation}
            disabled={isTranslating}
            sx={{
              textTransform: 'none',
              borderRadius: 3,
              px: 2,
              py: 0.5,
              fontSize: '0.75rem',
              fontWeight: 'medium',
              minWidth: 'auto',
              border: '1px solid',
              borderColor: 'primary.main',
              color: 'primary.main',
              '&:hover': {
                backgroundColor: 'primary.main',
                color: 'white',
              },
              '&:disabled': {
                borderColor: 'grey.300',
                color: 'grey.400',
              },
            }}
          >
            {isTranslating ? '翻译中' : '整页'}
          </Button>
        </Tooltip>
      </Box>

      {/* Toast 提示 */}
      <Snackbar
        open={showToast}
        autoHideDuration={3000}
        onClose={handleCloseToast}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseToast} 
          severity="info" 
          variant="filled"
          sx={{ 
            fontSize: '0.875rem',
            '& .MuiAlert-icon': {
              fontSize: '1.2rem'
            }
          }}
        >
          整页翻译功能即将上线，敬请期待！
        </Alert>
      </Snackbar>
    </>
  );
};
