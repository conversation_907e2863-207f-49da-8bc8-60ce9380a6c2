### Attention

* For the installation of the expansion pack in ZIP format for Firefox browser, please refer to [here](https://github.com/EdgeTranslate/EdgeTranslate/blob/master/docs/wiki/en/ToFirefoxUsers.md)!

* In order to further improve the translation experience, we have established an Edge Translate user communication group. Welcome to join: [Edge Translate](https://t.me/EdgeTranslate)

### Re-designed Translation Result Panel

This update brings a __newly designed__ translation result panel, which is more concise and beautiful than the old version, and uses space more effectively. The display panel of the same size can display more content!

![old_new_compare](../../images/old_new_compare.png)

In addition to a more beautiful and efficient display panel style, this update also brings two new functions:

* The content displayed in the display panel can be customized; now, you can hide the unnecessary content to make your result panel more concise and clear!

* Supports automatic folding of the content that is too long; if some content in the result panel is too long, you can choose to fold it up and expand it when you need it!

### PDF Reader Supports Dark Mode

This update also brings support for the dark mode of the built-in PDF reader, and reading documents in the dark will no longer be dazzling!

The dark mode automatically follows the system setting by default. That is, when the system is set to bright mode, the PDF is displayed in normal color:

![pdf_light](../../images/pdf_light.png)

When the system is set to dark mode, the PDF will automatically be displayed in dark color:

![pdf_dark](../../images/pdf_dark.png)

If you have special needs, click the `A` button in the upper right corner of the PDF reader to adjust the PDF display mode. It supports three modes: __Auto__, __Dark__ and __Original__.

### Other Improvements and Fixes

* Improved the result panel style when using the right-to-left layout;

* Updated Google Translate API to provide better translation;

* Fixed the problem of pronunciation error when using Google Translate (#169);

* Fixed the problem that the size of the translation result panel was out of control when using Chrome's native PDF reader (#163);

* Fixed the problem that the word translation button cannot be displayed under certain scenes;

### Sponsor

It took us much time and energy to develop this project. If it truly helped you in some way, you could reward us with cans of Coke to support us to keep improving it: [PayPal](https://paypal.me/EdgeTranslate).

But, this is completely __voluntary__. Sponsoring won't bring any special treatment and you can still use Edge Translate freely without sponsoring. Do it according to your capability!