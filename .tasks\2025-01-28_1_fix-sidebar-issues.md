# 背景
文件名：2025-01-28_1_fix-sidebar-issues.md
创建于：2025-01-28_10:30:00
创建者：用户
主分支：main
任务分支：task/fix-sidebar-issues_2025-01-28_1
Yolo模式：Off

# 任务描述
修复translator-plugin的两个问题：
1. 侧边栏点击侧边栏空白就会自动刷新一下翻译结果，不用这样
2. translator-plugin像EdgeTranslate-master的单词翻译功能没有看到有显示，请完善

# 项目概览
这是一个基于WXT框架的浏览器翻译插件，包含：
- translator-plugin：新的翻译插件实现
- EdgeTranslate-master：参考的原版EdgeTranslate插件

⚠️ 警告：永远不要修改此部分 ⚠️
核心RIPER-5协议规则：
- 必须在每个响应开头声明模式 [MODE: MODE_NAME]
- RESEARCH模式：只能观察和提问，不能建议或实施
- INNOVATE模式：只能讨论解决方案，不能具体规划
- PLAN模式：创建详细技术规范，不能实施
- EXECUTE模式：严格按照计划实施，不能偏离
- REVIEW模式：验证实施与计划的符合程度
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析
通过深入代码分析发现：

## 问题1：侧边栏空白点击刷新问题
- **位置**：translator-plugin/entrypoints/content/TranslatorSidebar.tsx (line 133-153)
- **根本原因**：`handleClickOutside`监听`mousedown`事件，与content.tsx中的`handleMouseUp`事件处理冲突
- **具体机制**：
  - TranslatorSidebar.tsx: `handleClickOutside`在`mousedown`时调用`toggleVisibility()`
  - content.tsx: `handleMouseUp`在400ms延迟后调用`checkSelection()`进行文本选择检测
  - 冲突点：点击侧边栏空白区域时，两个事件处理器都被触发
- **影响**：用户点击侧边栏空白区域时意外触发翻译刷新

## 问题2：单词翻译功能缺失
- **数据层面**：background.ts已经获取了完整的单词详解数据
  - detailedMeanings: 词性和释义 (line 415-423, 509-541)
  - definitions: 定义和例句 (line 427-438)
  - examples: 例句 (line 442-449, 547-560)
- **存储层面**：useTranslationStore.ts已保留这些数据 (line 261-266)
- **显示层面**：TranslationResult.tsx未集成显示这些数据
- **参考实现**：EdgeTranslate-master的Result.jsx完整显示所有单词详解
- **现有组件**：WordDefinition.tsx存在但独立，未与主翻译流程集成

# 提议的解决方案
待INNOVATE模式时填写

# 当前执行步骤："1. 研究阶段"

# 任务进度
[2025-01-28_10:30:00]
- 已修改：无
- 更改：完成深度代码分析和问题定位
- 原因：了解问题根源、数据流和现有代码结构
- 阻碍因素：无
- 状态：研究完成

## 关键发现：
1. **侧边栏刷新问题**：事件冲突在TranslatorSidebar.tsx的handleClickOutside和content.tsx的handleMouseUp之间
2. **单词翻译数据**：background.ts已获取完整数据，但TranslationResult接口缺少字段定义
3. **显示组件**：WordDefinition.tsx存在但未集成到主翻译流程中
4. **数据传递**：useTranslationStore.ts中数据被保留但接口不匹配

# 最终审查
待完成后填写
