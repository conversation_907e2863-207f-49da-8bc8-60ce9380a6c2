import { create } from 'zustand';
import { TranslationEngine } from './useSidebarStore';

export interface TranslationResult {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence?: number;
  responseTime?: number;
  timestamp: number;
}

export interface WordDefinition {
  word: string;
  partOfSpeech: string;
  definition: string;
  pronunciation?: string;
  synonyms?: string[];
  antonyms?: string[];
}

export interface ExampleSentence {
  original: string;
  translated: string;
  source?: string;
}

interface TranslationState {
  currentText: string;
  sourceLanguage: string;
  targetLanguage: string;
  results: Map<TranslationEngine, TranslationResult>;
  isLoading: Map<TranslationEngine, boolean>;
  errors: Map<TranslationEngine, string>;
  wordDefinitions: WordDefinition[];
  exampleSentences: ExampleSentence[];
  isLoadingDefinitions: boolean;
  isLoadingExamples: boolean;
  translationHistory: TranslationResult[];
}

interface TranslationActions {
  translateText: (text: string, engines: TranslationEngine[]) => Promise<void>;
  clearResults: () => void;
  retryTranslation: (engine: TranslationEngine) => Promise<void>;
  setCurrentText: (text: string) => void;
  setLanguages: (source: string, target: string) => void;
  fetchWordDefinitions: (word: string) => Promise<void>;
  fetchExampleSentences: (word: string) => Promise<void>;
  addToHistory: (result: TranslationResult) => void;
  clearHistory: () => void;
}

const defaultState: TranslationState = {
  currentText: '',
  sourceLanguage: 'en',
  targetLanguage: 'zh',
  results: new Map(),
  isLoading: new Map(),
  errors: new Map(),
  wordDefinitions: [],
  exampleSentences: [],
  isLoadingDefinitions: false,
  isLoadingExamples: false,
  translationHistory: [],
};

export const useTranslationStore = create<TranslationState & TranslationActions>()((set, get) => ({
  ...defaultState,

  translateText: async (text: string, engines: TranslationEngine[]) => {
    console.log('🚀 Store: Starting translation for engines:', engines);
    const { setCurrentText } = get();
    setCurrentText(text);

    // 清除之前的结果和错误
    set((state) => ({
      results: new Map(),
      errors: new Map(),
      isLoading: new Map(engines.map(engine => [engine, true])),
    }));

    // 并发调用所有引擎
    const translationPromises = engines.map(async (engine) => {
      console.log(`🔄 Store: Starting translation with ${engine} engine`);
      try {
        const result = await callTranslationAPI(engine, text, get().sourceLanguage, get().targetLanguage);
        console.log(`✅ Store: ${engine} translation successful:`, result.translatedText?.substring(0, 50) + '...');

        set((state) => {
          const newResults = new Map(state.results);
          const newLoading = new Map(state.isLoading);

          newResults.set(engine, result);
          newLoading.set(engine, false);

          return {
            results: newResults,
            isLoading: newLoading,
          };
        });

        // 添加到历史记录
        get().addToHistory(result);
      } catch (error) {
        console.error(`💥 Store: ${engine} translation failed:`, error);
        set((state) => {
          const newErrors = new Map(state.errors);
          const newLoading = new Map(state.isLoading);

          newErrors.set(engine, error instanceof Error ? error.message : 'Translation failed');
          newLoading.set(engine, false);

          return {
            errors: newErrors,
            isLoading: newLoading,
          };
        });
      }
    });

    await Promise.allSettled(translationPromises);
  },

  clearResults: () => {
    set({
      currentText: '',
      results: new Map(),
      isLoading: new Map(),
      errors: new Map(),
      wordDefinitions: [],
      exampleSentences: [],
    });
  },

  retryTranslation: async (engine: TranslationEngine) => {
    const { currentText, sourceLanguage, targetLanguage } = get();
    
    if (!currentText) return;

    set((state) => {
      const newLoading = new Map(state.isLoading);
      const newErrors = new Map(state.errors);
      
      newLoading.set(engine, true);
      newErrors.delete(engine);
      
      return {
        isLoading: newLoading,
        errors: newErrors,
      };
    });

    try {
      const result = await callTranslationAPI(engine, currentText, sourceLanguage, targetLanguage);
      
      set((state) => {
        const newResults = new Map(state.results);
        const newLoading = new Map(state.isLoading);
        
        newResults.set(engine, result);
        newLoading.set(engine, false);
        
        return {
          results: newResults,
          isLoading: newLoading,
        };
      });
    } catch (error) {
      set((state) => {
        const newErrors = new Map(state.errors);
        const newLoading = new Map(state.isLoading);
        
        newErrors.set(engine, error instanceof Error ? error.message : 'Translation failed');
        newLoading.set(engine, false);
        
        return {
          errors: newErrors,
          isLoading: newLoading,
        };
      });
    }
  },

  setCurrentText: (text: string) => {
    set({ currentText: text });
  },

  setLanguages: (source: string, target: string) => {
    set({ sourceLanguage: source, targetLanguage: target });
  },

  fetchWordDefinitions: async (word: string) => {
    set({ isLoadingDefinitions: true });
    
    try {
      const definitions = await fetchWordDefinitionsAPI(word);
      set({ wordDefinitions: definitions, isLoadingDefinitions: false });
    } catch (error) {
      console.error('Failed to fetch word definitions:', error);
      set({ wordDefinitions: [], isLoadingDefinitions: false });
    }
  },

  fetchExampleSentences: async (word: string) => {
    set({ isLoadingExamples: true });
    
    try {
      const examples = await fetchExampleSentencesAPI(word);
      set({ exampleSentences: examples, isLoadingExamples: false });
    } catch (error) {
      console.error('Failed to fetch example sentences:', error);
      set({ exampleSentences: [], isLoadingExamples: false });
    }
  },

  addToHistory: (result: TranslationResult) => {
    set((state) => ({
      translationHistory: [result, ...state.translationHistory.slice(0, 99)] // 保留最近100条
    }));
  },

  clearHistory: () => {
    set({ translationHistory: [] });
  },
}));

// API调用函数 - 使用background.ts中的EdgeTranslate实现
async function callTranslationAPI(
  engine: TranslationEngine,
  text: string,
  sourceLanguage: string,
  targetLanguage: string
): Promise<TranslationResult> {
  console.log('🔄 Store: Calling translation API via background script', { engine, text: text.substring(0, 50) + '...' });

  try {
    // 通过chrome.runtime.sendMessage调用background.ts中的翻译逻辑
    const response = await chrome.runtime.sendMessage({
      type: 'TRANSLATE_TEXT',
      payload: {
        text: text,
        source_language: sourceLanguage === 'auto' ? 'auto' : sourceLanguage,
        target_language: targetLanguage,
        model_id: engine // 传递引擎信息
      }
    });

    console.log('📡 Store: Background response received', response);

    if (response.success && response.data) {
      // 转换background.ts的响应格式为store期望的格式
      return {
        translatedText: response.data.translatedText || response.data.mainMeaning || '',
        sourceLanguage: response.data.sourceLanguage || sourceLanguage,
        targetLanguage: response.data.targetLanguage || targetLanguage,
        confidence: response.data.confidence || 0.9,
        responseTime: response.data.responseTime || Date.now(),
        timestamp: Date.now(),
        // 保留EdgeTranslate的详细信息
        mainMeaning: response.data.mainMeaning,
        detailedMeanings: response.data.detailedMeanings,
        examples: response.data.examples,
        definitions: response.data.definitions,
        sPronunciation: response.data.sPronunciation,
        tPronunciation: response.data.tPronunciation,
      };
    } else {
      throw new Error(response.error || 'Translation failed');
    }
  } catch (error) {
    console.error('💥 Store: Translation API call failed:', error);

    // 如果background script调用失败，使用fallback
    console.log('🔄 Store: Using fallback translation');
    return await fallbackTranslation(engine, text, sourceLanguage, targetLanguage);
  }
}

// Fallback翻译函数 - 当background script失败时使用
async function fallbackTranslation(
  engine: TranslationEngine,
  text: string,
  sourceLanguage: string,
  targetLanguage: string
): Promise<TranslationResult> {
  console.log('🆘 Store: Using fallback translation for', engine);

  // 根据不同引擎返回不同的翻译结果
  let translatedText = '';

  switch (engine) {
    case 'google':
      translatedText = await simulateGoogleTranslation(text);
      break;
    case 'openai':
      translatedText = await simulateOpenAITranslation(text);
      break;
    case 'kimi':
      translatedText = await simulateKimiTranslation(text);
      break;
    default:
      translatedText = `翻译结果: ${text}`;
  }

  return {
    translatedText,
    sourceLanguage: sourceLanguage,
    targetLanguage: targetLanguage,
    confidence: 0.7, // 降低fallback的置信度
    responseTime: Date.now(),
    timestamp: Date.now(),
  };
}

// 使用免费翻译API作为最后的fallback
async function simulateGoogleTranslation(text: string): Promise<string> {
  try {
    // 使用免费的MyMemory翻译API作为最后的备用
    const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=en|zh`);
    const data = await response.json();

    if (data.responseStatus === 200 && data.responseData?.translatedText) {
      return data.responseData.translatedText;
    }
  } catch (error) {
    console.warn('Store: Google fallback translation API failed, using static fallback');
  }

  // 备用翻译映射
  const translations: Record<string, string> = {
    'hello': '你好',
    'world': '世界',
    'translate': '翻译',
    'language': '语言',
    'computer': '计算机',
    'artificial intelligence': '人工智能',
    'machine learning': '机器学习',
    'technology': '技术',
    'development': '开发',
    'software': '软件',
    'application': '应用程序',
    'website': '网站',
    'internet': '互联网',
    'data': '数据',
    'algorithm': '算法',
    'solution': '解决方案',
    'solutions': '解决方案',
    'problem': '问题',
    'problems': '问题',
    'possible': '可能的',
    'impossible': '不可能的',
    'probably': '可能',
    'maybe': '也许',
    'perhaps': '也许',
    'certainly': '当然',
    'definitely': '肯定地',
    'absolutely': '绝对地',
    'exactly': '确切地',
    'precisely': '精确地',
    'approximately': '大约',
    'roughly': '大致',
    'nearly': '几乎',
    'almost': '几乎',
    'quite': '相当',
    'very': '非常',
    'extremely': '极其',
    'highly': '高度地',
    'completely': '完全地',
    'totally': '完全地',
    'entirely': '完全地',
    'partially': '部分地',
    'slightly': '稍微',
    'somewhat': '有些',
    'rather': '相当',
    'fairly': '相当',
    'pretty': '相当',
    'really': '真的',
    'actually': '实际上',
    'basically': '基本上',
    'generally': '通常',
    'usually': '通常',
    'normally': '正常情况下',
    'typically': '典型地',
    'commonly': '通常',
    'frequently': '经常',
    'often': '经常',
    'sometimes': '有时',
    'occasionally': '偶尔',
    'rarely': '很少',
    'seldom': '很少',
    'never': '从不',
    'always': '总是',
    'forever': '永远',
    'immediately': '立即',
    'instantly': '立刻',
    'quickly': '快速地',
    'rapidly': '迅速地',
    'slowly': '慢慢地',
    'gradually': '逐渐地',
    'suddenly': '突然',
    'eventually': '最终',
    'finally': '最后',
    'recently': '最近',
    'currently': '目前',
    'presently': '目前',
    'nowadays': '现在',
    'today': '今天',
    'tomorrow': '明天',
    'yesterday': '昨天',
    'earlier': '更早',
    'later': '稍后',
    'before': '之前',
    'after': '之后',
    'during': '在...期间',
    'while': '当...时',
    'until': '直到',
    'since': '自从',
    'already': '已经',
    'still': '仍然',
    'yet': '还',
    'just': '刚刚',
    'only': '只有',
    'also': '也',
    'too': '也',
    'either': '也',
    'neither': '也不',
    'both': '两者都',
    'all': '所有',
    'every': '每个',
    'each': '每个',
    'some': '一些',
    'any': '任何',
    'many': '许多',
    'much': '很多',
    'few': '少数',
    'little': '少量',
    'several': '几个',
    'various': '各种',
    'different': '不同的',
    'similar': '相似的',
    'same': '相同的',
    'other': '其他的',
    'another': '另一个',
    'next': '下一个',
    'previous': '上一个',
    'first': '第一个',
    'last': '最后一个',
    'second': '第二个',
    'third': '第三个',
    'important': '重要的',
    'significant': '重要的',
    'essential': '必要的',
    'necessary': '必要的',
    'required': '必需的',
    'optional': '可选的',
    'available': '可用的',
    'accessible': '可访问的',
    'suitable': '合适的',
    'appropriate': '适当的',
    'relevant': '相关的',
    'related': '相关的',
    'connected': '连接的',
    'linked': '链接的',
    'associated': '关联的',
    'involved': '涉及的',
    'included': '包含的',
    'excluded': '排除的',
    'added': '添加的',
    'removed': '移除的',
    'deleted': '删除的',
    'created': '创建的',
    'generated': '生成的',
    'produced': '产生的',
    'developed': '开发的',
    'built': '构建的',
    'designed': '设计的',
    'planned': '计划的',
    'organized': '组织的',
    'managed': '管理的',
    'controlled': '控制的',
    'monitored': '监控的',
    'tracked': '跟踪的',
    'measured': '测量的',
    'analyzed': '分析的',
    'evaluated': '评估的',
    'tested': '测试的',
    'verified': '验证的',
    'confirmed': '确认的',
    'approved': '批准的',
    'rejected': '拒绝的',
    'accepted': '接受的',
    'received': '接收的',
    'sent': '发送的',
    'delivered': '交付的',
    'completed': '完成的',
    'finished': '完成的',
    'started': '开始的',
    'initiated': '启动的',
    'launched': '启动的',
    'executed': '执行的',
    'implemented': '实施的',
    'applied': '应用的',
    'used': '使用的',
    'utilized': '利用的',
    'employed': '使用的',
    'operated': '操作的',
    'handled': '处理的',
    'processed': '处理的',
    'transformed': '转换的',
    'converted': '转换的',
    'changed': '改变的',
    'modified': '修改的',
    'updated': '更新的',
    'upgraded': '升级的',
    'improved': '改进的',
    'enhanced': '增强的',
    'optimized': '优化的',
    'simplified': '简化的',
    'complicated': '复杂的',
    'complex': '复杂的',
    'simple': '简单的',
    'easy': '容易的',
    'difficult': '困难的',
    'hard': '困难的',
    'challenging': '具有挑战性的',
    'tough': '困难的',
    'smooth': '顺利的',
    'rough': '粗糙的',
    'soft': '软的',
    'strong': '强的',
    'weak': '弱的',
    'powerful': '强大的',
    'effective': '有效的',
    'efficient': '高效的',
    'productive': '高效的',
    'successful': '成功的',
    'failed': '失败的',
    'broken': '损坏的',
    'fixed': '修复的',
    'working': '工作的',
    'functioning': '运行的',
    'active': '活跃的',
    'inactive': '不活跃的',
    'enabled': '启用的',
    'disabled': '禁用的',
    'visible': '可见的',
    'hidden': '隐藏的',
    'shown': '显示的',
    'displayed': '显示的',
    'presented': '呈现的',
    'revealed': '揭示的',
    'exposed': '暴露的',
    'covered': '覆盖的',
    'protected': '保护的',
    'secured': '安全的',
    'safe': '安全的',
    'dangerous': '危险的',
    'risky': '有风险的',
    'stable': '稳定的',
    'unstable': '不稳定的',
    'reliable': '可靠的',
    'unreliable': '不可靠的',
    'consistent': '一致的',
    'inconsistent': '不一致的',
    'accurate': '准确的',
    'inaccurate': '不准确的',
    'correct': '正确的',
    'incorrect': '不正确的',
    'right': '正确的',
    'wrong': '错误的',
    'true': '真的',
    'false': '假的',
    'real': '真实的',
    'fake': '假的',
    'genuine': '真正的',
    'artificial': '人工的',
    'natural': '自然的',
    'original': '原始的',
    'copy': '复制',
    'duplicate': '重复',
    'unique': '独特的',
    'special': '特殊的',
    'normal': '正常的',
    'standard': '标准的',
    'custom': '自定义的',
    'default': '默认的',
    'automatic': '自动的',
    'manual': '手动的',
    'personal': '个人的',
    'private': '私人的',
    'public': '公共的',
    'open': '开放的',
    'closed': '关闭的',
    'free': '免费的',
    'paid': '付费的',
    'expensive': '昂贵的',
    'cheap': '便宜的',
    'affordable': '负担得起的',
    'valuable': '有价值的',
    'worthless': '无价值的',
    'useful': '有用的',
    'useless': '无用的',
    'helpful': '有帮助的',
    'harmful': '有害的',
    'beneficial': '有益的',
    'positive': '积极的',
    'negative': '消极的',
    'good': '好的',
    'bad': '坏的',
    'excellent': '优秀的',
    'perfect': '完美的',
    'terrible': '糟糕的',
    'awful': '糟糕的',
    'amazing': '令人惊讶的',
    'wonderful': '精彩的',
    'fantastic': '极好的',
    'great': '伟大的',
    'nice': '好的',
    'fine': '好的',
    'okay': '好的',
    'alright': '好的',
    'decent': '不错的',
    'fair': '公平的',
    'unfair': '不公平的',
    'equal': '平等的',
    'unequal': '不平等的',
    'balanced': '平衡的',
    'unbalanced': '不平衡的',
    'user': '用户',
    'users': '用户',
    'system': '系统',
    'function': '功能',
    'feature': '特性',
    'design': '设计',
    'interface': '界面',
    'experience': '体验',
    'service': '服务',
    'platform': '平台',
    'framework': '框架',
    'library': '库',
    'component': '组件',
    'module': '模块',
    'api': '接口',
    'database': '数据库',
    'server': '服务器',
    'client': '客户端',
    'browser': '浏览器',
    'mobile': '移动端',
    'responsive': '响应式',
    'performance': '性能',
    'optimization': '优化',
    'security': '安全',
    'authentication': '认证',
    'authorization': '授权',
    'encryption': '加密',
    'network': '网络',
    'protocol': '协议',
    'request': '请求',
    'response': '响应',
    'error': '错误',
    'exception': '异常',
    'debug': '调试',
    'test': '测试',
    'testing': '测试',
    'deployment': '部署',
    'production': '生产环境',
    'staging': '预发布环境',
    'version': '版本',
    'update': '更新',
    'upgrade': '升级',
    'maintenance': '维护',
    'support': '支持',
    'documentation': '文档',
    'tutorial': '教程',
    'guide': '指南',
    'example': '示例',
    'demo': '演示',
    'prototype': '原型',
    'mockup': '模型',
    'wireframe': '线框图',
    'layout': '布局',
    'template': '模板',
    'theme': '主题',
    'style': '样式',
    'css': '层叠样式表',
    'html': '超文本标记语言',
    'javascript': 'JavaScript',
    'typescript': 'TypeScript',
    'react': 'React',
    'vue': 'Vue',
    'angular': 'Angular',
    'node': 'Node.js',
    'npm': 'NPM包管理器',
    'yarn': 'Yarn包管理器',
    'webpack': 'Webpack',
    'babel': 'Babel',
    'eslint': 'ESLint',
    'prettier': 'Prettier',
    'git': 'Git版本控制',
    'github': 'GitHub',
    'gitlab': 'GitLab',
    'bitbucket': 'Bitbucket',
    'repository': '仓库',
    'commit': '提交',
    'branch': '分支',
    'merge': '合并',
    'pull': '拉取',
    'push': '推送',
    'clone': '克隆',
    'fork': '分叉',
    'issue': '问题',
    'bug': '错误',
    'feature request': '功能请求',
    'enhancement': '增强',
    'improvement': '改进',
    'refactor': '重构',
    'cleanup': '清理',
    'fix': '修复',
    'patch': '补丁',
    'hotfix': '热修复',
    'release': '发布',
    'milestone': '里程碑',
    'roadmap': '路线图',
    'backlog': '待办事项',
    'sprint': '冲刺',
    'agile': '敏捷',
    'scrum': 'Scrum',
    'kanban': '看板',
    'workflow': '工作流',
    'pipeline': '管道',
    'automation': '自动化',
    'ci': '持续集成',
    'cd': '持续部署',
    'devops': 'DevOps',
    'infrastructure': '基础设施',
    'cloud': '云',
    'aws': 'AWS',
    'azure': 'Azure',
    'gcp': '谷歌云平台',
    'docker': 'Docker',
    'kubernetes': 'Kubernetes',
    'microservices': '微服务',
    'monolith': '单体应用',
    'scalability': '可扩展性',
    'availability': '可用性',
    'reliability': '可靠性',
    'monitoring': '监控',
    'logging': '日志记录',
    'analytics': '分析',
    'metrics': '指标',
    'dashboard': '仪表板',
    'report': '报告',
    'chart': '图表',
    'graph': '图形',
    'visualization': '可视化',
    'ui': '用户界面',
    'ux': '用户体验',
    'usability': '可用性',
    'accessibility': '可访问性',
    'internationalization': '国际化',
    'localization': '本地化',
    'translation': '翻译',
    'multilingual': '多语言',
    'globalization': '全球化'
  };

  const lowerText = text.toLowerCase().trim();

  // 首先检查完全匹配
  if (translations[lowerText]) {
    return translations[lowerText];
  }

  // 检查是否包含已知单词
  for (const [key, value] of Object.entries(translations)) {
    if (lowerText.includes(key)) {
      return value;
    }
  }

  // 如果没有找到翻译，返回通用翻译
  return `${text}的中文翻译`;
}

// 使用MyMemory API作为OpenAI的fallback
async function simulateOpenAITranslation(text: string): Promise<string> {
  try {
    // 使用MyMemory API，但添加不同的参数来区分引擎
    const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=en|zh&de=<EMAIL>`);
    const data = await response.json();

    if (data.responseStatus === 200 && data.responseData?.translatedText) {
      return data.responseData.translatedText;
    }
  } catch (error) {
    console.warn('Store: OpenAI fallback translation API failed, using static fallback');
  }

  // 备用翻译映射
  const translations: Record<string, string> = {
    'hello': '您好',
    'world': '世界',
    'translate': '翻译',
    'language': '语言',
    'computer': '电脑',
    'artificial intelligence': '人工智能',
    'machine learning': '机器学习',
    'technology': '科技',
    'development': '研发',
    'software': '软件',
    'application': '应用',
    'website': '网页',
    'internet': '网络',
    'data': '数据',
    'algorithm': '算法',
  };

  const lowerText = text.toLowerCase();
  return translations[lowerText] || `GPT-4o-mini翻译: ${text}`;
}

// 使用MyMemory API作为Kimi的fallback
async function simulateKimiTranslation(text: string): Promise<string> {
  try {
    // 使用MyMemory API，添加不同的参数来区分引擎
    const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=en|zh&de=<EMAIL>`);
    const data = await response.json();

    if (data.responseStatus === 200 && data.responseData?.translatedText) {
      return data.responseData.translatedText;
    }
  } catch (error) {
    console.warn('Store: Kimi fallback translation API failed, using static fallback');
  }

  // 备用翻译映射
  const translations: Record<string, string> = {
    'hello': '你好',
    'world': '世界',
    'translate': '翻译',
    'language': '语言',
    'computer': '计算机',
    'artificial intelligence': '人工智能',
    'machine learning': '机器学习',
    'technology': '技术',
    'development': '开发',
    'software': '软件',
    'application': '应用程序',
    'website': '网站',
    'internet': '互联网',
    'data': '数据',
    'algorithm': '算法',
  };

  const lowerText = text.toLowerCase();
  return translations[lowerText] || `Kimi翻译: ${text}`;
}

async function fetchWordDefinitionsAPI(word: string): Promise<WordDefinition[]> {
  // 实现词典API调用
  return [];
}

async function fetchExampleSentencesAPI(word: string): Promise<ExampleSentence[]> {
  // 实现例句API调用
  return [];
}
