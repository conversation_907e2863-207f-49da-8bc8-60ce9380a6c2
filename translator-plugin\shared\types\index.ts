// 用户相关类型
export interface User {
  id: string;
  email: string;
  provider: 'google' | 'microsoft';
  subscription_status: 'free' | 'premium';
  preferences: UserPreferences;
  created_at: string;
  updated_at: string;
}

export interface UserPreferences {
  source_language: string;
  target_language: string;
  theme: 'light' | 'dark' | 'system';
  auto_translate: boolean;
  show_pronunciation: boolean;
  show_definitions: boolean;
  show_examples: boolean;
  sidebar_position: 'right' | 'left';
  hotkey_enabled: boolean;
  hotkey_combination: string;
}

// AI模型相关类型
export interface AIModel {
  id: string;
  name: string;
  provider: string;
  endpoint: string;
  api_key_encrypted: string;
  is_free: boolean;
  priority: number;
  is_enabled: boolean;
  max_requests_per_day: number;
  supported_languages: string[];
  created_at: string;
  updated_at: string;
}

// 翻译相关类型
export interface TranslationRequest {
  text: string;
  source_language: string;
  target_language: string;
  model_id?: string;
}

export interface TranslationResult {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  pronunciation?: string;
  definitions?: Definition[];
  examples?: Example[];
  model_used: string;
  response_time_ms: number;
}

export interface Definition {
  partOfSpeech: string;
  text: string;
}

export interface Example {
  source: string;
  target: string;
}

// 语言相关类型
export interface Language {
  id: string;
  code: string;
  name: string;
  native_name: string | null;
  is_enabled: boolean;
  ocr_supported: boolean;
  translation_supported: boolean;
  created_at: string;
}

// 用量统计相关类型
export interface UsageLog {
  id: string;
  user_id: string;
  model_id: string | null;
  request_type: 'translate' | 'ocr';
  characters_count: number;
  source_language: string | null;
  target_language: string | null;
  success: boolean;
  error_message: string | null;
  response_time_ms: number | null;
  created_at: string;
}

export interface UsageStats {
  total_requests: number;
  successful_requests: number;
  failed_requests: number;
  total_characters: number;
  average_response_time: number;
  requests_by_date: { date: string; count: number }[];
  requests_by_model: { model_name: string; count: number }[];
  requests_by_language: { language: string; count: number }[];
}

// 消息传递相关类型
export interface ExtensionMessage {
  type: string;
  payload?: any;
}

export interface TranslateMessage extends ExtensionMessage {
  type: 'TRANSLATE_TEXT';
  payload: TranslationRequest;
}

export interface OCRMessage extends ExtensionMessage {
  type: 'OCR_TEXT';
  payload: {
    imageData: string;
    language: string;
  };
}

export interface SettingsMessage extends ExtensionMessage {
  type: 'UPDATE_SETTINGS';
  payload: Partial<UserPreferences>;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 错误类型
export interface TranslationError {
  code: string;
  message: string;
  details?: any;
}

// 存储相关类型
export interface StorageData {
  user_settings: UserPreferences;
  translation_cache: Record<string, TranslationResult>;
  user_info: Partial<User>;
  last_sync: string;
}

// OCR相关类型
export interface OCRResult {
  text: string;
  confidence: number;
  language: string;
  processing_time_ms: number;
}

// UI相关类型
export interface SidebarPosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface UIState {
  sidebar_visible: boolean;
  sidebar_position: SidebarPosition;
  sidebar_pinned: boolean;
  sidebar_minimized: boolean;
  current_translation: TranslationResult | null;
  loading: boolean;
  error: string | null;
}
