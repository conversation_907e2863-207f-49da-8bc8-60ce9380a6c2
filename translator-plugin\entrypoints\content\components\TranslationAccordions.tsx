import React from 'react';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
  IconButton,
  Chip,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Translate as TranslateIcon,
  Book as BookIcon,
  FormatQuote as FormatQuoteIcon,
  VolumeUp as VolumeUpIcon,
  ContentCopy as CopyIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon
} from '@mui/icons-material';
import { useSidebarStore, AccordionSection } from '../../../shared/stores/useSidebarStore';
import { useTranslationStore } from '../../../shared/stores/useTranslationStore';

interface AccordionConfig {
  id: AccordionSection;
  title: string;
  icon: React.ReactNode;
  defaultExpanded: boolean;
}

const accordionConfigs: AccordionConfig[] = [
  {
    id: 'translation-result',
    title: '翻译结果',
    icon: <TranslateIcon />,
    defaultExpanded: true,
  },
  {
    id: 'word-definition',
    title: '单词详解',
    icon: <BookIcon />,
    defaultExpanded: false,
  },
  {
    id: 'example-sentences',
    title: '例句展示',
    icon: <FormatQuoteIcon />,
    defaultExpanded: false,
  },
];

export const TranslationAccordions: React.FC = () => {
  const { expandedAccordions, toggleAccordion, activeTab } = useSidebarStore();
  const { 
    currentText, 
    results, 
    wordDefinitions, 
    exampleSentences,
    isLoadingDefinitions,
    isLoadingExamples,
    fetchWordDefinitions,
    fetchExampleSentences
  } = useTranslationStore();

  const handleAccordionChange = (section: AccordionSection) => {
    toggleAccordion(section);
    
    // 当展开单词详解或例句时，自动获取数据
    if (!expandedAccordions.includes(section)) {
      if (section === 'word-definition' && currentText && wordDefinitions.length === 0) {
        fetchWordDefinitions(currentText);
      } else if (section === 'example-sentences' && currentText && exampleSentences.length === 0) {
        fetchExampleSentences(currentText);
      }
    }
  };

  const handleCopyText = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      console.log('Text copied to clipboard');
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const handlePlayPronunciation = (text: string, language: string = 'en') => {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = language;
      utterance.rate = 0.8;
      speechSynthesis.speak(utterance);
    }
  };

  const getCurrentResult = () => {
    return results.get(activeTab);
  };

  return (
    <Box sx={{ flex: 1, overflow: 'auto' }}>
      {accordionConfigs.map((config) => {
        const isExpanded = expandedAccordions.includes(config.id);
        
        return (
          <Accordion
            key={config.id}
            expanded={isExpanded}
            onChange={() => handleAccordionChange(config.id)}
            sx={{
              '&:before': { display: 'none' },
              boxShadow: 'none',
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: '4px !important',
              mb: 1,
              '&.Mui-expanded': {
                margin: '0 0 8px 0',
              },
            }}
            TransitionProps={{ timeout: 120 }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                minHeight: 48,
                '&.Mui-expanded': {
                  minHeight: 48,
                },
                '& .MuiAccordionSummary-content': {
                  '&.Mui-expanded': {
                    margin: '12px 0',
                  },
                },
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Box sx={{ color: 'primary.main', display: 'flex', alignItems: 'center' }}>
                  {config.icon}
                </Box>
                <Typography variant="subtitle2" fontWeight="medium">
                  {config.title}
                </Typography>
              </Box>
            </AccordionSummary>
            
            <AccordionDetails sx={{ pt: 0 }}>
              {config.id === 'translation-result' && (
                <TranslationResultContent 
                  result={getCurrentResult()}
                  onCopy={handleCopyText}
                  onPlay={handlePlayPronunciation}
                />
              )}
              
              {config.id === 'word-definition' && (
                <WordDefinitionContent 
                  definitions={wordDefinitions}
                  isLoading={isLoadingDefinitions}
                  currentText={currentText}
                />
              )}
              
              {config.id === 'example-sentences' && (
                <ExampleSentencesContent 
                  examples={exampleSentences}
                  isLoading={isLoadingExamples}
                  currentText={currentText}
                />
              )}
            </AccordionDetails>
          </Accordion>
        );
      })}
    </Box>
  );
};

// 翻译结果内容组件
const TranslationResultContent: React.FC<{
  result: any;
  onCopy: (text: string) => void;
  onPlay: (text: string, lang?: string) => void;
}> = ({ result, onCopy, onPlay }) => {
  if (!result) {
    return (
      <Typography variant="body2" color="text.secondary">
        暂无翻译结果
      </Typography>
    );
  }

  return (
    <Box>
      {/* 原文 */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="caption" color="text.secondary" fontWeight="medium">
          原文
        </Typography>
        <Box sx={{ 
          mt: 0.5, 
          p: 1, 
          backgroundColor: 'grey.50', 
          borderRadius: 1,
          fontSize: '0.875rem',
          lineHeight: 1.4
        }}>
          {/* 这里应该显示原文，暂时用占位符 */}
          原文内容...
        </Box>
      </Box>

      {/* 翻译结果 */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="caption" color="text.secondary" fontWeight="medium">
            翻译结果
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <IconButton 
              size="small" 
              onClick={() => onPlay(result.translatedText, result.targetLanguage)}
              title="朗读翻译"
            >
              <VolumeUpIcon fontSize="small" />
            </IconButton>
            <IconButton 
              size="small" 
              onClick={() => onCopy(result.translatedText)}
              title="复制翻译"
            >
              <CopyIcon fontSize="small" />
            </IconButton>
            <IconButton 
              size="small" 
              title="收藏"
            >
              <StarBorderIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>
        
        <Box sx={{ 
          p: 1.5,
          backgroundColor: 'primary.50',
          borderRadius: 1,
          fontSize: '0.875rem',
          lineHeight: 1.5
        }}>
          {result.translatedText}
        </Box>
      </Box>

      {/* 音标 */}
      {result.pronunciation && (
        <Box sx={{ mb: 1 }}>
          <Typography variant="caption" color="text.secondary">
            音标: [{result.pronunciation}]
          </Typography>
        </Box>
      )}
    </Box>
  );
};

// 单词详解内容组件
const WordDefinitionContent: React.FC<{
  definitions: any[];
  isLoading: boolean;
  currentText: string;
}> = ({ definitions, isLoading, currentText }) => {
  if (isLoading) {
    return (
      <Box sx={{ textAlign: 'center', py: 2 }}>
        <Typography variant="body2" color="text.secondary">
          正在获取词汇详解...
        </Typography>
      </Box>
    );
  }

  if (definitions.length === 0) {
    return (
      <Typography variant="body2" color="text.secondary">
        暂无词汇详解
      </Typography>
    );
  }

  return (
    <Box>
      {definitions.map((def, index) => (
        <Box key={index} sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Chip 
              label={def.partOfSpeech} 
              size="small" 
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
            {def.pronunciation && (
              <Typography variant="caption" color="text.secondary">
                [{def.pronunciation}]
              </Typography>
            )}
          </Box>
          
          <Typography variant="body2" sx={{ mb: 1 }}>
            {def.definition}
          </Typography>
          
          {def.synonyms && def.synonyms.length > 0 && (
            <Box sx={{ mb: 1 }}>
              <Typography variant="caption" color="text.secondary">
                近义词: {def.synonyms.join(', ')}
              </Typography>
            </Box>
          )}
          
          {index < definitions.length - 1 && <Divider sx={{ my: 1 }} />}
        </Box>
      ))}
    </Box>
  );
};

// 例句展示内容组件
const ExampleSentencesContent: React.FC<{
  examples: any[];
  isLoading: boolean;
  currentText: string;
}> = ({ examples, isLoading, currentText }) => {
  if (isLoading) {
    return (
      <Box sx={{ textAlign: 'center', py: 2 }}>
        <Typography variant="body2" color="text.secondary">
          正在获取例句...
        </Typography>
      </Box>
    );
  }

  if (examples.length === 0) {
    return (
      <Typography variant="body2" color="text.secondary">
        暂无相关例句
      </Typography>
    );
  }

  return (
    <Box>
      {examples.map((example, index) => (
        <Box key={index} sx={{ mb: 2 }}>
          <Box sx={{ 
            p: 1.5,
            backgroundColor: 'grey.50',
            borderRadius: 1,
            borderLeft: '3px solid',
            borderLeftColor: 'primary.main'
          }}>
            <Typography variant="body2" sx={{ mb: 1, fontStyle: 'italic' }}>
              {example.original}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {example.translated}
            </Typography>
            {example.source && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                来源: {example.source}
              </Typography>
            )}
          </Box>
        </Box>
      ))}
    </Box>
  );
};
